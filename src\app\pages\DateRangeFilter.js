import React from 'react';
import moment from 'moment';
import { DatePicker } from 'antd';

const { RangePicker } = DatePicker;

const DateRangeFilter = ({ columnDef, onFilterChanged }) => {
    const handleDateChange = (dates, dateStrings) => {
        const startDate = dates ? dates[0] : null;
        const endDate = dates ? dates[1] : null;
        onFilterChanged(columnDef.tableData.id, { startDate, endDate });
    };

    return (
        <RangePicker
            onChange={handleDateChange}
            format="DD-MM-YYYY"
        />
    );
};

export default DateRangeFilter;