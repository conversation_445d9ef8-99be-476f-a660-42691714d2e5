import React, { useRef, useState } from 'react'
import { Modal, Button, Form } from 'react-bootstrap'
import Jo<PERSON><PERSON><PERSON><PERSON> from "jodit-react";
function DocReviewerModal({ showModal, setShowModal, data, handleSubmitData, handleReturn }) {
    const [isClicked, setIsClicked] = useState(false);
    const dDesc = useRef()
    const [modal, setModal] = useState(false)
    const handleSubmit = () => {

        handleSubmitData(data)

    }
    const handleReturnSubmit = () => {
        setIsClicked(true);
        handleReturn(data, dDesc.current.value)
        setIsClicked(false);
    }
    const config = {
        askBeforePasteHTML: false,
        askBeforePasteFromWord: false,
        defaultActionOnPaste: "insert_clear_html",
        toolbarAdaptive: false,
        toolbarButtonSize: "large",
        toolbarSticky: false,
        buttons:
          "|,bold,underline,italic,|,font,fontsize,|,superscript,subscript,|,ul,ol,|,outdent,indent,|,align,paste,image,|",
        enableDragAndDropFileToEditor: true,
      };
    
    const renderField = (field, index) => {


        if (field.toolType === 'WEB_LINK') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <a href={field.title} >{field.title}</a>
                    </div>
                </div>
            )


        } else if (field.toolType === 'IMAGE') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <img src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_image/' + field.title} style={{ maxWidth: '100%' }} />
                    </div>
                </div>
            )
        } else if (field.toolType === 'YOUTUBE') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <iframe sandbox="allow-same-origin allow-forms allow-popups allow-scripts allow-presentation" src={field.title} allowfullscreen=""></iframe>
                    </div>
                </div>
            )
        } else if (field.toolType === 'VIDEO') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>

                        <video controls style={{ maxWidth: '100%' }}>
                            <source src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_video/' + field.title} type="video/mp4" />
                            Your browser does not support video play.
                        </video>
                    </div>
                </div>
            )
        } else if (field.toolType === "PARAGRAPH") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p dangerouslySetInnerHTML={{ __html: field.content }} />

                    </div>
                </div>
            )
        } else if (field.toolType === "AUDIO") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <div className="col-12 text-center">
                            <audio controls style={{ maxWidth: '100%' }}>
                                <source src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_audio/' + field.title} />
                            </audio>
                        </div>

                    </div>
                </div>
            )
        } else if (field.toolType === "PDF") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <div className="col-12 text-center">
                            <a href={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/pdf_uploads/' + field.title} target="_blank"><i className="fa fa-file-pdf-o fa-5x"></i><p style={{ textDecoration: 'none' }}>click to view</p></a></div>

                    </div>
                </div>
            )
        } else if (field.toolType === "EMBEDCODE") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <div className="col-12 text-center">
                            <div dangerouslySetInnerHTML={{ __html: field.title }} style={{ width: '100%' }} /></div>

                    </div>
                </div>
            )
        }
        else if (field.toolType === "MCQ") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p
                            dangerouslySetInnerHTML={{ __html: field.title }}
                        />
                        {field.radios.map(item => {
                            return (
                                <div className="form-check">
                                    <input


                                        className="form-check-input"
                                        type="checkbox"
                                        id="isRequired"
                                    />
                                    <label className="form-check-label" htmlFor="isRequired">
                                        {item.value}
                                    </label>
                                </div>
                            )
                        })}

                    </div>
                </div>
            )
        } else if (field.toolType === "TEXT_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>
                        <input


                            className="form-control"
                            type="text"
                            id="isRequired"
                        />
                    </div>
                </div>
            )
        } else if (field.toolType === "IMAGE_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "VIDEO_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "AUDIO_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "OPTION_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p
                            dangerouslySetInnerHTML={{ __html: field.title }}
                        />
                        {field.radios.map(item => {
                            return (
                                <div className="form-check">
                                    <input


                                        className="form-check-input"
                                        type="checkbox"
                                        id="isRequired"
                                    />
                                    <label className="form-check-label" htmlFor="isRequired">
                                        {item.value}
                                    </label>
                                </div>
                            )
                        })}

                    </div>
                </div>
            )
        } else if (field.toolType === "SIGN_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "CHECK_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p
                            dangerouslySetInnerHTML={{ __html: field.title }}
                        />
                        {field.radios.map(item => {
                            return (
                                <div className="form-check">
                                    <input


                                        className="form-check-input"
                                        type="checkbox"
                                        id="isRequired"
                                    />
                                    <label className="form-check-label" htmlFor="isRequired">
                                        {item.value}
                                    </label>
                                </div>
                            )
                        })}

                    </div>
                </div>
            )
        } else if (field.toolType === "MULTIMEDIA") {
            return (
                <></>
            )
        }
    }
    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0"> {data.actionType === 'doc_created' ? 'Document Reviewer' : 'Document Approver'}</h2>
                        <div className="text-center mt-0">

                        </div>
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className={`animate-col-md-12`}>
                            {
                                JSON.parse(data.applicationDetails.value).map((field, index) => {
                                    return renderField(field, index)
                                })
                            }
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer className="flex-wrap">
                    <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={() => setModal(true)}
                    >
                        Return To Author
                    </Button>

                    <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={handleSubmit}
                    >
                        {data.applicationDetails.status === "2" ? 'Submit to Approver' : 'Approve'}
                    </Button>


                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>
            }

            <Modal
                show={modal}
                size="md"
                onHide={() => setModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static">
                <Modal.Header>

                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0">Document Return</h2>
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className={`animate-col-md-12`}>
                            <div className="form-group">
                                <label htmlFor="document_category" >Reason for Returning</label>
                                {/* <Form.Control as={'textarea'} ref={dDesc} id="document_category" placeholder="Enter Reason" /> */}
                                <JoditEditor
                                    ref={dDesc}
                                    config={config}
                                    // onBlur={(newContent) => this.changeValue("CONTENT", newContent)}
                                    // onChange={(newContent) => this.changeValue("CONTENT", newContent)}
                                />
                            </div>

                        </div>
                    </div>

                </Modal.Body>
                <Modal.Footer className="flex-wrap">


                    <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={handleReturnSubmit}
                        disabled={isClicked}
                    >
                        Submit
                    </Button>


                    <Button variant="light" onClick={() => { setModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>
        </>
    )
}

export default DocReviewerModal