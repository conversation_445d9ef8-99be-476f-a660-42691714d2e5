import React, { useEffect, useState, useCallback } from "react";
import { Modal, Button, Form } from 'react-bootstrap';
import { AIR_WITH_ID_URL, ALL_LOCATIONTHREE_LOCATIONFOUR_URL, PPES_URL, INJURY_URL, GENERAL_USER_URL, EQUIPMENT_CATEGORIES_URL, GHS_ONE_URL, GHS_TWO_URL, LIGHTING_URL, LOCATION3_URL, SURFACE_CONDITION_URL, SURFACE_TYPE_URL, TIER2_TIER3_URL, WEATHER_CONDITION_URL, WORKING_GROUP_URL, WORK_ACTIVITIES_URL } from "../constants";
import API from "../services/API";
import cogoToast from "cogo-toast";
import moment from "moment";
import Switch from "react-switch";
import { BodyComponent } from "reactjs-human-body";
import Select from "react-select";
import SelectDropdown from "./SelectDropdown";

const IncidentEdit = ({ data, onSaved, triggerSubmit }) => {

    // State
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');
    const [selectedWorkingGroup, setSelectedWorkingGroup] = useState('');
    const [selectedDescription, setSelectedDescription] = useState('');
    const [selectedWeatherCondition, setSelectedWeatherCondition] = useState('');
    const [selectedMoreDetails, setSelectedMoreDetails] = useState('');
    const [selectedWorkActivity, setSelectedWorkActivity] = useState('');
    const [selectedWorkActivityDepartment, setSelectedWorkActivityDepartment] = useState('');
    const [selectedIsReported, setSelectedIsReported] = useState('');
    const [selectedLighting, setSelectedLighting] = useState('');
    const [selectedSurfaceType, setSelectedSurfaceType] = useState('');
    const [selectedSurfaceCondition, setSelectedSurfaceCondition] = useState('');
    const [locationFour, setLocationFour] = useState([]);
    const [locationThree, setLocationThree] = useState([]);
    const [workingGroup, setWorkingGroup] = useState([]);
    const [weatherCondition, setWeatherCondition] = useState([]);
    const [workActivity, setWorkActivity] = useState([]);
    const [workActivityDepartment, setWorkActivityDepartment] = useState([]);
    const [lighting, setLighting] = useState([]);
    const [surfaceType, setSurfaceType] = useState([]);
    const [surfaceCondition, setSurfaceCondition] = useState([]);

    const [equipmentCategory, setEquimentCategory] = useState([]);

    const [generalUsers, setGeneralUsers] = useState([]);
    const [generalUsersOptions, setGeneralUsersOptions] = useState([]);

    const [ppes, setPpes] = useState([]);
    const [injuries, setInjuries] = useState([]);

    const [incidentDate, setIncidentDate] = useState('');
    const [vesselDetails, setVesselDetails] = useState('');
    const [damageType, setDamageType] = useState('');
    const [actionsTaken, setActionsTaken] = useState('');
    const [trucksInvolved, setTrucksInvolved] = useState('');

    useEffect(() => { if (triggerSubmit) { handleSubmit() } }, [triggerSubmit])
    // API Calls
    const getLocationThree = useCallback(async () => {
        const response = await API.get(LOCATION3_URL);
        if (response.status === 200) {
            setLocationThree(response.data);
        }
    }, []);

    const getLocationFour = async (locationThreeId) => {

        if (locationThreeId) {
            const response = await API.get(ALL_LOCATIONTHREE_LOCATIONFOUR_URL(locationThreeId));
            if (response.status === 200) {
                setLocationFour(response.data);
            }
        }

    }

    const getWorkingGroup = useCallback(async () => {
        const response = await API.get(WORKING_GROUP_URL);
        if (response.status === 200) {
            setWorkingGroup(response.data);
        }
    }, []);

    const getWeatherCondition = useCallback(async () => {
        const response = await API.get(WEATHER_CONDITION_URL);
        if (response.status === 200) {
            setWeatherCondition(response.data);
        }
    }, []);

    const getWorkActivity = useCallback(async () => {
        const response = await API.get(GHS_TWO_URL);
        if (response.status === 200) {
            setWorkActivity(response.data);
        }
    }, []);

    const getWorkActivityDepartment = useCallback(async () => {
        const response = await API.get(GHS_ONE_URL);
        if (response.status === 200) {
            setWorkActivityDepartment(response.data);
        }
    }, []);

    const getLighting = useCallback(async () => {
        const response = await API.get(LIGHTING_URL);
        if (response.status === 200) {
            setLighting(response.data);
        }
    }, []);

    const getSurfaceType = useCallback(async () => {
        const response = await API.get(SURFACE_TYPE_URL);
        if (response.status === 200) {
            setSurfaceType(response.data);
        }
    }, []);

    const getSurfaceCondition = useCallback(async () => {
        const response = await API.get(SURFACE_CONDITION_URL);
        if (response.status === 200) {
            setSurfaceCondition(response.data);
        }
    }, []);

    const getEquipmentCategory = useCallback(async () => {
        const response = await API.get(EQUIPMENT_CATEGORIES_URL);
        if (response.status === 200) {
            setEquimentCategory(response.data);
        }
    }, []);

    const getGeneralUsers = useCallback(async () => {
        const response = await API.get(GENERAL_USER_URL);
        if (response.status === 200) {
            setGeneralUsers(response.data);
            setGeneralUsersOptions(response.data.map(user => ({
                value: user.id,
                label: `${user.uniqueId} - ${user.name}`,
            })))
        }
    }, []);

    const handleChange = (selectedOption, index) => {
        handlePersonnelInvolvedChange(index, "selectedEmp", selectedOption.value, true);
    };

    const handlePChange = (selectedOption, index) => {
        handlePersonImpactedChange(index, "selectedEmp", selectedOption.value, true);
    };


    const getPPEs = useCallback(async () => {
        const response = await API.get(PPES_URL);
        if (response.status === 200) {
            setPpes(response.data.map(option => ({ value: option.id, label: option.name })));
        }
    }, []);

    const getInjuries = useCallback(async () => {
        const response = await API.get(INJURY_URL);
        if (response.status === 200) {
            setInjuries(response.data.map(option => ({ value: option.id, label: option.name })));
        }
    }, []);

    const [equipment, setEquipment] = useState({
        damagedEquipmentNumber: [
            {
                category: "",
                number: "",
                damageType: ""
            }
        ]
    })





    const addImmediateAction = () => {
        setEquipment(prevState => ({
            ...prevState,
            damagedEquipmentNumber: [...prevState.damagedEquipmentNumber, {
                category: "",
                number: "",
                damageType: ""
            }],
        }));
    };

    const handleImmediateActionChange = (index, field, value) => {
        setEquipment(prevState => {
            const updatedActions = [...prevState.damagedEquipmentNumber];
            updatedActions[index][field] = value;
            return { ...prevState, damagedEquipmentNumber: updatedActions };
        });
    };

    const handleDeleteImmediateAction = (index) => {
        const newImmediateActions = [...equipment.damagedEquipmentNumber];
        newImmediateActions.splice(index, 1);
        setEquipment(prevState => ({ ...prevState, damagedEquipmentNumber: newImmediateActions }));
    };


    const [witnessInvolved, setWitnessInvolved] = useState({
        witnessInvolved: [
            {
                "internal": true,
                "selectedEmp": {

                },
                "name": "",
                "empId": "",
                "designation": "",
                "comments": ""
            }
        ],
        personInvolved: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
        ],
        personnelImpacted: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
        ]

    })

    const addWitness = () => {
        setWitnessInvolved(prevState => ({
            ...prevState,
            witnessInvolved: [...prevState.witnessInvolved, {
                "internal": true,
                "selectedEmp": {

                },
                "name": "",
                "empId": "",
                "designation": "",
                "comments": ""
            }],
        }));
    };

    const addPersonInvolved = () => {
        setWitnessInvolved(prevState => ({
            ...prevState,
            personInvolved: [...prevState.personInvolved, {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }],
        }));
    };

    const addPersonnelImpacted = () => {
        setWitnessInvolved(prevState => ({
            ...prevState,
            personnelImpacted: [...prevState.personnelImpacted, {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }],
        }));
    };

    const handleWChange = (selectedOption, index) => {

        handleWitnessChange(index, "selectedEmp", selectedOption.value, true)
    };

    const handleWitnessChange = (index, field, value, flag) => {
        let newValue = value;

        if (flag && field === 'selectedEmp') {
            const data = generalUsers.find(i => i.id === value)

            newValue = {
                "internal": true,
                "selectedEmp": data,
                "name": "",
                "empId": "",
                "designation": "",
                "comments": ""
            }
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.witnessInvolved];
                updatedActions[index] = newValue;

                return { ...prevState, witnessInvolved: updatedActions };
            });
        } else {
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.witnessInvolved];
                updatedActions[index][field] = newValue;

                return { ...prevState, witnessInvolved: updatedActions };
            });
        }

    };

    const handlePersonImpactedChange = (index, field, value, flag) => {
        let newValue = value;

        if (flag && field === 'selectedEmp') {
            const data = generalUsers.find(i => i.id === value)

            newValue = {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": data,
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personInvolved];
                updatedActions[index] = newValue;

                return { ...prevState, personInvolved: updatedActions };
            });
        } else {
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personInvolved];
                updatedActions[index][field] = newValue;

                return { ...prevState, personInvolved: updatedActions };
            });
        }

    };

    const handlePersonnelInvolvedChange = (index, field, value, flag) => {
        let newValue = value;

        if (flag && field === 'selectedEmp') {
            const data = generalUsers.find(i => i.id === value)

            newValue = {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": data,
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personnelImpacted];
                updatedActions[index] = newValue;

                return { ...prevState, personnelImpacted: updatedActions };
            });
        } else {
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personnelImpacted];
                updatedActions[index][field] = newValue;

                return { ...prevState, personnelImpacted: updatedActions };
            });
        }

    };

    const handleDeleteWitness = (index) => {
        const newImmediateActions = [...witnessInvolved.witnessInvolved];
        newImmediateActions.splice(index, 1);
        setWitnessInvolved(prevState => ({ ...prevState, witnessInvolved: newImmediateActions }));
    };

    const handleDeletePersonInvolved = (index) => {
        const newImmediateActions = [...witnessInvolved.personInvolved];
        newImmediateActions.splice(index, 1);
        setWitnessInvolved(prevState => ({ ...prevState, personInvolved: newImmediateActions }));
    };

    const handleDeletePersonnelImpacted = (index) => {
        const newImmediateActions = [...witnessInvolved.personnelImpacted];
        newImmediateActions.splice(index, 1);
        setWitnessInvolved(prevState => ({ ...prevState, personnelImpacted: newImmediateActions }));
    };

    // Effects
    useEffect(() => {
        getLocationThree();
        getWorkingGroup();
        getWeatherCondition();
        getWorkActivity();
        getWorkActivityDepartment();
        getLighting();
        getSurfaceType();
        getSurfaceCondition();
        getEquipmentCategory();
        getGeneralUsers();
        getPPEs();
        getInjuries();
    }, [getLocationThree, getWorkingGroup, getWeatherCondition]);

    useEffect(() => {
        if (data) {
            setSelectedLocationThree(data.locationThreeId);
            setSelectedLocationFour(data.locationFourId);
            setSelectedWorkingGroup(data.workingGroupId);
            setSelectedDescription(data.description);
            setSelectedWeatherCondition(data.weatherConditionId);
            setSelectedWorkActivity(data.workActivityId)
            setSelectedWorkActivityDepartment(data.workActivityDepartmentId)
            setSelectedMoreDetails(data.moreDetails);
            setSelectedLighting(data.lightingId)
            setIncidentDate(data.incidentDate)
            setVesselDetails(data.vesselDetails)
            setSelectedSurfaceType(data.surfaceTypeId)
            setEquipment({ damagedEquipmentNumber: data.damagedEquipmentNumber })
            setWitnessInvolved({ witnessInvolved: data.witnessInvolved, personnelImpacted: data.personnelImpacted, personInvolved: data.personInvolved })
            setSelectedSurfaceCondition(data.surfaceConditionId);
            setDamageType(data.damageType)
            setActionsTaken(data.actionsTaken)
            setTrucksInvolved(data.truckDetails)
            getLocationFour(data.locationThreeId);
            setSelectedIsReported(data.isReported)
        }
    }, [data]);

    useEffect(() => {
        getLocationFour(selectedLocationThree);
    }, [selectedLocationThree]);

    // Handling form submission
    const handleSubmit = async () => {
        // Implement your desired functionality here.
        if (data.id) {
            const response = await API.patch(AIR_WITH_ID_URL(data.id), {
                locationThreeId: selectedLocationThree,
                locationFourId: selectedLocationFour,
                workingGroupId: selectedWorkingGroup,
                description: selectedDescription,
                weatherConditionId: selectedWeatherCondition,
                moreDetails: selectedMoreDetails,
                incidentDate: incidentDate,
                vesselDetails: vesselDetails,
                damageType: damageType,
                actionsTaken: actionsTaken,
                truckDetails: trucksInvolved,
                workActivityId: selectedWorkActivity,
                workActivityDepartmentId: selectedWorkActivityDepartment,
                isReported: selectedIsReported,
                lightingId: selectedLighting,
                surfaceTypeId: selectedSurfaceType,
                surfaceConditionId: selectedSurfaceCondition,
                damagedEquipmentNumber: equipment.damagedEquipmentNumber,
                witnessInvolved: witnessInvolved.witnessInvolved,
                personInvolved: witnessInvolved.personInvolved,
                personnelImpacted: witnessInvolved.personnelImpacted
            })

            if (response.status === 204) {
                cogoToast.success('Updated!')
                onSaved(true)

            }
        }
    };
    const reverseToMobileFormatBodyParts = obj => Object.keys(obj).map(key =>
        key.split('_')
            .map((word, index) => index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1))
            .map(word => word === 'left' ? 'right' : word === 'right' ? 'left' : word)
            .join('')
    );

    const [isEdit, setIsEdit] = useState(false)

    return (
        <>

            <Form>
                <div className="row">
                    <label className="d-flex align-items-center">
                        Enable Edit
                        <Switch className="ms-2" onChange={(e) => setIsEdit(e)} checked={isEdit} />
                    </label>
                    <div className="col-6">
                        <Form.Group controlId="AreaSelect">
                            <Form.Label>Area</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedLocationThree} onChange={(e) => setSelectedLocationThree(e.target.value)}>
                                {locationThree.map(i => (
                                    <option key={i.id} value={i.id}>{i.name}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>
                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Zone</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedLocationFour} onChange={(e) => setSelectedLocationFour(e.target.value)}>
                                {locationFour.map(i => (
                                    <option key={i.id} value={i.id}>{i.name}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>
                </div>

                <div className="row">
                    <div className="col-12 mt-3">
                        <Form.Group controlId="description">
                            <Form.Label>Description</Form.Label>
                            <textarea disabled={!isEdit} value={selectedDescription} className="form-control" onChange={(e) => { setSelectedDescription(e.target.value) }}>

                            </textarea>
                        </Form.Group>
                    </div>

                </div>

                <div className="row">
                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Working Group</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedWorkingGroup} onChange={(e) => setSelectedWorkingGroup(e.target.value)}>
                                {workingGroup.map(i => (
                                    <option key={i.id} value={i.id}>{i.name}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>



                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Weather Condition</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedWeatherCondition} onChange={(e) => setSelectedWeatherCondition(e.target.value)}>
                                {weatherCondition.map(i => (
                                    <option key={i.id} value={i.id}>{i.name}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>

                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Lighting</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedLighting} onChange={(e) => setSelectedLighting(e.target.value)}>
                                {lighting.map(i => (
                                    <option key={i.id} value={i.id}>{i.name}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>

                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Surface Type</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedSurfaceType} onChange={(e) => setSelectedSurfaceType(e.target.value)}>
                                {surfaceType.map(i => (
                                    <option key={i.id} value={i.id}>{i.name}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>

                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Surface Condition</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedSurfaceCondition} onChange={(e) => setSelectedSurfaceCondition(e.target.value)}>
                                {surfaceCondition.map(i => (
                                    <option key={i.id} value={i.id}>{i.name}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>

                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Work Activity</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedWorkActivity} onChange={(e) => setSelectedWorkActivity(e.target.value)}>
                                {[...workActivity].sort((a, b) => a.title.localeCompare(b.title)).map(i => (
                                    <option key={i.id} value={i.id}>{i.title}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>

                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Department</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedWorkActivityDepartment} onChange={(e) => setSelectedWorkActivityDepartment(e.target.value)}>
                                {workActivityDepartment.map(i => (
                                    <option key={i.id} value={i.id}>{i.title}</option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </div>

                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Reported Status</Form.Label>
                            <Form.Select disabled={!isEdit} value={selectedIsReported.toLowerCase()} onChange={(e) => setSelectedIsReported(e.target.value)}>
                                <option value={''}>Choose</option>
                                <option value={'reported'}>Reported</option>
                                <option value={'unreported'}>UnReported</option>
                            </Form.Select>
                        </Form.Group>
                    </div>
                </div>
                <div className="row">
                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Incident Date</Form.Label>
                            <input type="text" disabled={!isEdit} className="form-control" value={incidentDate} onChange={(e) => setIncidentDate(e.target.value)} />
                        </Form.Group>
                    </div>
                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Vessel Details</Form.Label>
                            <input type="text" disabled={!isEdit} className="form-control" value={vesselDetails} onChange={(e) => setVesselDetails(e.target.value)} />
                        </Form.Group>
                    </div>
                </div>

                <div className="row">
                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Any Other Type of Damage</Form.Label>
                            <input type="text" disabled={!isEdit} className="form-control" value={damageType} onChange={(e) => setDamageType(e.target.value)} />
                        </Form.Group>
                    </div>
                    <div className="col-6">
                        <Form.Group controlId="ZoneSelect">
                            <Form.Label>Any Immediate Actions Taken</Form.Label>
                            <input type="text" disabled={!isEdit} className="form-control" value={actionsTaken} onChange={(e) => setActionsTaken(e.target.value)} />
                        </Form.Group>
                    </div>
                </div>

                <div className="row">
                    <div className="col-12 mt-3">
                        <Form.Group controlId="description">
                            <Form.Label>How and Why this Incident Occur?</Form.Label>
                            <textarea disabled={!isEdit} value={selectedMoreDetails} className="form-control" onChange={(e) => { setSelectedMoreDetails(e.target.value) }}>

                            </textarea>
                        </Form.Group>
                    </div>

                </div>

                <div className="row">
                    <div className="col-12 mt-3">
                        <Form.Group controlId="description">
                            <Form.Label>Trucks Involved (Separated by Comma)</Form.Label>
                            <textarea disabled={!isEdit} value={trucksInvolved} className="form-control" onChange={(e) => { setTrucksInvolved(e.target.value) }}>

                            </textarea>
                        </Form.Group>
                    </div>

                </div>

                <div className="row">
                    <div>
                        <Form.Label>  Damaged Equipments </Form.Label>
                        {equipment.damagedEquipmentNumber.filter(i => i).map((action, index) => (
                            <div className="form-group d-flex align-items-center" key={index}>
                                <label>
                                    <Form.Label> Equipment Category  </Form.Label>
                                    <select
                                        disabled={!isEdit}
                                        value={action.category}
                                        onChange={(e) =>
                                            handleImmediateActionChange(index, "category", e.target.value)
                                        }
                                        className='form-select'>
                                        <option value={''}>Choose</option>
                                        {
                                            equipmentCategory.map(i => {
                                                return (
                                                    <option value={i.name}>
                                                        {i.name}
                                                    </option>
                                                )
                                            }

                                            )
                                        }
                                    </select>
                                </label>

                                <label>
                                    <Form.Label> Equipment Number </Form.Label>
                                    <input
                                        className="form-control"
                                        type="text"
                                        disabled={!isEdit}
                                        value={action.number}
                                        onChange={(e) =>
                                            handleImmediateActionChange(index, "number", e.target.value)
                                        }
                                    />
                                </label>

                                <label>
                                    <Form.Label>  Damage Type  </Form.Label>

                                    <input
                                        className="form-control"
                                        type="text"
                                        disabled={!isEdit}
                                        value={action.damageType}
                                        onChange={(e) =>
                                            handleImmediateActionChange(index, "damageType", e.target.value)
                                        }
                                    />

                                </label>
                                {isEdit && <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={() => handleDeleteImmediateAction(index)}
                                >
                                    Delete
                                </button>}
                            </div>
                        ))}

                        {isEdit && <button variant="light" className='d-block btn btn-light mb-4' type="button" onClick={addImmediateAction}>
                            Add More
                        </button>}

                    </div>
                </div>
                <div className="row">
                    <div>
                        <Form.Label> Person Involved </Form.Label>
                        {witnessInvolved.personInvolved.filter(i => i).map((action, index) => (
                            <div className="form-group" key={index}>

                                {
                                    action.internal && (
                                        <>
                                            {/* <label>
                                                    <Switch onChange={(e) => handleWitnessChange(index, "internal", e.target.value, true)} checked={!action.internal} />
                                                </label> */}
                                            <label className="w-25">
                                                <Form.Label> Choose Person  </Form.Label>
                                                <SelectDropdown
                                                    flag={!isEdit}
                                                    options={generalUsersOptions}
                                                    value={action.selectedEmp?.id}
                                                    onChange={handlePChange}
                                                    placeholder="Choose"
                                                    index={index}
                                                    className="w-25"
                                                />
                                                <p className="my-2">Department: {action.selectedEmp?.department} | Designation: {action.selectedEmp?.designation}  </p>
                                            </label>
                                            <br />
                                            <label className="d-flex align-items-center">
                                                Is this Person Injured
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonImpactedChange(index, "injured", e, true)} checked={action.injured} />
                                            </label>
                                            <br />
                                            {
                                                action.injured && (
                                                    <>
                                                        {isEdit && <BodyComponent onChange={(value) => handlePersonImpactedChange(index, "injuryParts", reverseToMobileFormatBodyParts(value), true)} partsInput={action.injuryParts.reduce((acc, part) => {
                                                            let modifiedPart = part
                                                                .replace(/^left/i, 'TEMP')
                                                                .replace(/^right/i, 'left')
                                                                .replace(/^TEMP/i, 'right');
                                                            modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();
                                                            acc[modifiedPart] = { selected: true };
                                                            return acc;
                                                        }, {})} />}

                                                        <label>More Details on Injury</label>
                                                        <Select
                                                            isMulti
                                                            isDisabled={!isEdit}
                                                            options={injuries}
                                                            value={action.injuryDetails && action.injuryDetails?.filter(i => i).map(option => ({ value: option?.id || '', label: option?.name || '' })) || []}
                                                            onChange={(selected) => handlePersonImpactedChange(index, "injuryDetails", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                            classNamePrefix="react-select"
                                                        />
                                                    </>
                                                )
                                            }
                                            <label className="d-flex align-items-center">
                                                Is protective equipment(s) used by this person?
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonImpactedChange(index, "isPPE", e, true)} checked={action.isPPE} />
                                            </label>
                                            {action.isPPE && (
                                                <>
                                                    <label>if yes, what type?</label>
                                                    <Select
                                                        isDisabled={!isEdit}
                                                        isMulti
                                                        options={ppes}
                                                        value={action.ppes.filter(i => i).map(option => ({ value: option.id, label: option.name }))}
                                                        onChange={(selected) => handlePersonImpactedChange(index, "ppes", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                        classNamePrefix="react-select"
                                                    />
                                                </>
                                            )}




                                        </>
                                    )
                                }

                                {
                                    !action.internal && (
                                        <>
                                            {/* <label>
                                                    <Switch onChange={(e) => handleWitnessChange(index, "internal", e.target.value, false)} checked={!action.internal} />
                                                </label> */}
                                            <label>
                                                <Form.Label> NIC </Form.Label>
                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.empId}
                                                    onChange={(e) =>
                                                        handlePersonImpactedChange(index, "empId", e.target.value, false)
                                                    }
                                                />
                                            </label>

                                            <label>
                                                <Form.Label> Name </Form.Label>
                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.name}
                                                    onChange={(e) =>
                                                        handlePersonImpactedChange(index, "name", e.target.value, false)
                                                    }
                                                />
                                            </label>

                                            <label>
                                                <Form.Label>  Remarks / Comments  </Form.Label>

                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.comments}
                                                    onChange={(e) =>
                                                        handlePersonImpactedChange(index, "comments", e.target.value, false)
                                                    }
                                                />

                                            </label>
                                            <br />
                                            <label className="d-flex align-items-center">
                                                Is this Person Injured
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonImpactedChange(index, "injured", e, true)} checked={action.injured} />
                                            </label>
                                            <br />
                                            {
                                                action.injured && (
                                                    <>
                                                        {isEdit && <BodyComponent onChange={(value) => handlePersonImpactedChange(index, "injuryParts", reverseToMobileFormatBodyParts(value), true)} partsInput={action.injuryParts.reduce((acc, part) => {
                                                            let modifiedPart = part
                                                                .replace(/^left/i, 'TEMP')
                                                                .replace(/^right/i, 'left')
                                                                .replace(/^TEMP/i, 'right');
                                                            modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();
                                                            acc[modifiedPart] = { selected: true };
                                                            return acc;
                                                        }, {})} />
                                                        }
                                                        <label>More Details on Injury</label>
                                                        <Select
                                                            isDisabled={!isEdit}
                                                            isMulti
                                                            options={injuries}
                                                            value={action.injuryDetails && action.injuryDetails?.filter(i => i).map(option => ({ value: option?.id || '', label: option?.name || '' })) || []}
                                                            onChange={(selected) => handlePersonImpactedChange(index, "injuryDetails", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                            classNamePrefix="react-select"
                                                        />
                                                    </>
                                                )
                                            }
                                            <br />
                                            <label className="d-flex align-items-center">
                                                Is protective equipment(s) used by this person?
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonImpactedChange(index, "isPPE", e, true)} checked={action.isPPE} />
                                            </label>
                                            {action.isPPE && (
                                                <>
                                                    <label>if yes, what type?</label>
                                                    <Select
                                                        isMulti
                                                        isDisabled={!isEdit}
                                                        options={ppes}
                                                        value={action.ppes.filter(i => i).map(option => ({ value: option?.id || '', label: option?.name || '' }))}
                                                        onChange={(selected) => handlePersonImpactedChange(index, "ppes", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                        classNamePrefix="react-select"
                                                    />
                                                </>
                                            )}
                                        </>
                                    )
                                }
                                <br />
                                {isEdit && <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={() => handleDeletePersonInvolved(index)}
                                >
                                    Delete
                                </button>}
                            </div>
                        ))}
                        {isEdit && <button variant="light" className='btn btn-light mb-4' type="button" onClick={addPersonInvolved}>
                            Add More
                        </button>}

                    </div>
                </div>


                <div className="row">
                    <div>
                        <Form.Label> Personnel Injured </Form.Label>
                        {witnessInvolved.personnelImpacted.filter(i => i).map((action, index) => (
                            <div className="form-group" key={index}>

                                {
                                    action.internal && (
                                        <>
                                            {/* <label>
                                                    <Switch onChange={(e) => handleWitnessChange(index, "internal", e.target.value, true)} checked={!action.internal} />
                                                </label> */}
                                            <label className="w-25">
                                                <Form.Label> Choose Person  </Form.Label>
                                                <SelectDropdown
                                                    flag={!isEdit}
                                                    options={generalUsersOptions}
                                                    value={action.selectedEmp?.id}
                                                    onChange={handleChange}
                                                    placeholder="Choose"
                                                    index={index}
                                                    className="w-25"
                                                />
                                                <p className="my-2">Department: {action.selectedEmp?.department} | Designation: {action.selectedEmp?.designation}  </p>
                                            </label>
                                            <br />
                                            <label className="d-flex align-items-center">
                                                Is this Person Injured
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonnelInvolvedChange(index, "injured", e, true)} checked={action.injured} />
                                            </label>
                                            <br />
                                            {
                                                action.injured && (
                                                    <>
                                                        {isEdit && <BodyComponent onChange={(value) => handlePersonnelInvolvedChange(index, "injuryParts", reverseToMobileFormatBodyParts(value), true)} partsInput={action.injuryParts.reduce((acc, part) => {
                                                            let modifiedPart = part
                                                                .replace(/^left/i, 'TEMP')
                                                                .replace(/^right/i, 'left')
                                                                .replace(/^TEMP/i, 'right');
                                                            modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();
                                                            acc[modifiedPart] = { selected: true };
                                                            return acc;
                                                        }, {})} />
                                                        }
                                                        <label>More Details on Injury</label>
                                                        <Select
                                                            isDisabled={!isEdit}
                                                            isMulti
                                                            options={injuries}
                                                            value={action.injuryDetails && action.injuryDetails?.filter(i => i).map(option => ({ value: option?.id || '', label: option?.name || '' })) || []}
                                                            onChange={(selected) => handlePersonnelInvolvedChange(index, "injuryDetails", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                            classNamePrefix="react-select"
                                                        />
                                                    </>
                                                )
                                            }
                                            <label className="d-flex align-items-center">
                                                Is protective equipment(s) used by this person?
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonnelInvolvedChange(index, "isPPE", e, true)} checked={action.isPPE} />
                                            </label>
                                            {action.isPPE && (
                                                <>
                                                    <label>if yes, what type?</label>
                                                    <Select
                                                        isMulti
                                                        isDisabled={!isEdit}
                                                        options={ppes}
                                                        value={action.ppes.filter(i => i).map(option => ({ value: option.id, label: option.name }))}
                                                        onChange={(selected) => handlePersonnelInvolvedChange(index, "ppes", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                        classNamePrefix="react-select"
                                                    />
                                                </>
                                            )}

                                        </>
                                    )
                                }

                                {
                                    !action.internal && (
                                        <>
                                            {/* <label>
                                                    <Switch onChange={(e) => handleWitnessChange(index, "internal", e.target.value, false)} checked={!action.internal} />
                                                </label> */}
                                            <label>
                                                <Form.Label> NIC </Form.Label>
                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.empId}
                                                    onChange={(e) =>
                                                        handlePersonnelInvolvedChange(index, "empId", e.target.value, false)
                                                    }
                                                />
                                            </label>

                                            <label>
                                                <Form.Label> Name </Form.Label>
                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.name}
                                                    onChange={(e) =>
                                                        handlePersonnelInvolvedChange(index, "name", e.target.value, false)
                                                    }
                                                />
                                            </label>

                                            <label>
                                                <Form.Label>  Remarks / Comments  </Form.Label>

                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.comments}
                                                    onChange={(e) =>
                                                        handlePersonnelInvolvedChange(index, "comments", e.target.value, false)
                                                    }
                                                />

                                            </label>
                                            <br />
                                            <label className="d-flex align-items-center">
                                                Is this Person Injured
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonnelInvolvedChange(index, "injured", e, true)} checked={action.injured} />
                                            </label>
                                            <br />
                                            {
                                                action.injured && (
                                                    <>
                                                        {isEdit && <BodyComponent onChange={(value) => handlePersonnelInvolvedChange(index, "injuryParts", reverseToMobileFormatBodyParts(value), true)} partsInput={action.injuryParts.reduce((acc, part) => {
                                                            let modifiedPart = part
                                                                .replace(/^left/i, 'TEMP')
                                                                .replace(/^right/i, 'left')
                                                                .replace(/^TEMP/i, 'right');
                                                            modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();
                                                            acc[modifiedPart] = { selected: true };
                                                            return acc;
                                                        }, {})} />}
                                                        <label>More Details on Injury</label>
                                                        <Select
                                                            isDisabled={!isEdit}
                                                            isMulti
                                                            options={injuries}
                                                            value={action.injuryDetails.filter(i => i).map(option => ({ value: option.id, label: option.name }))}
                                                            onChange={(selected) => handlePersonnelInvolvedChange(index, "injuryDetails", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                            classNamePrefix="react-select"
                                                        />
                                                    </>
                                                )
                                            }
                                            <br />
                                            <label className="d-flex align-items-center">
                                                Is protective equipment(s) used by this person?
                                                <Switch disabled={!isEdit} className="ms-2" onChange={(e) => handlePersonnelInvolvedChange(index, "isPPE", e, true)} checked={action.isPPE} />
                                            </label>
                                            {action.isPPE && (
                                                <>
                                                    <label>if yes, what type?</label>
                                                    <Select
                                                        isDisabled={!isEdit}
                                                        isMulti
                                                        options={ppes}
                                                        value={action.ppes.filter(i => i).map(option => ({ value: option.id, label: option.name }))}
                                                        onChange={(selected) => handlePersonnelInvolvedChange(index, "ppes", selected.map(option => ({ id: option.value, name: option.label })), true)}
                                                        classNamePrefix="react-select"
                                                    />
                                                </>
                                            )}
                                        </>
                                    )
                                }
                                <br />
                                {isEdit && <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={() => handleDeletePersonnelImpacted(index)}
                                >
                                    Delete
                                </button>}
                            </div>
                        ))}
                        {isEdit && <button variant="light" className='btn btn-light mb-4' type="button" onClick={addPersonnelImpacted}>
                            Add More
                        </button>
                        }
                    </div>
                </div>



                <div className="row">
                    <div>
                        <Form.Label> Witness Involved </Form.Label>
                        {witnessInvolved.witnessInvolved.map((action, index) => (
                            <div className="form-group d-flex align-items-center" key={index}>

                                {
                                    action.internal && (
                                        <>
                                            {/* <label>
                                                    <Switch onChange={(e) => handleWitnessChange(index, "internal", e.target.value, true)} checked={!action.internal} />
                                                </label> */}
                                            <label className="w-25">
                                                <Form.Label> Choose Witness  </Form.Label>
                                                <SelectDropdown
                                                    flag={!isEdit}
                                                    options={generalUsersOptions}
                                                    value={action.selectedEmp?.id}
                                                    onChange={handleWChange}
                                                    placeholder="Choose"
                                                    index={index}
                                                    className="w-25"
                                                />
                                                <p className="my-2">Department: {action.selectedEmp?.department} | Designation: {action.selectedEmp?.designation}  </p>
                                            </label>


                                        </>
                                    )
                                }

                                {
                                    !action.internal && (
                                        <>
                                            {/* <label>
                                                    <Switch onChange={(e) => handleWitnessChange(index, "internal", e.target.value, false)} checked={!action.internal} />
                                                </label> */}
                                            <label>
                                                <Form.Label> NIC </Form.Label>
                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.empId}
                                                    onChange={(e) =>
                                                        handleWitnessChange(index, "empId", e.target.value, false)
                                                    }
                                                />
                                            </label>

                                            <label>
                                                <Form.Label> Name </Form.Label>
                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.name}
                                                    onChange={(e) =>
                                                        handleWitnessChange(index, "name", e.target.value, false)
                                                    }
                                                />
                                            </label>

                                            <label>
                                                <Form.Label>  Remarks / Comments  </Form.Label>

                                                <input
                                                    className="form-control"
                                                    type="text"
                                                    disabled={!isEdit}
                                                    value={action.comments}
                                                    onChange={(e) =>
                                                        handleWitnessChange(index, "comments", e.target.value, false)
                                                    }
                                                />

                                            </label>
                                        </>
                                    )
                                }

                                {isEdit && <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={() => handleDeleteWitness(index)}
                                >
                                    Delete
                                </button>}
                            </div>
                        ))}
                        {isEdit && <button variant="light" className='btn btn-light mb-4' type="button" onClick={addWitness}>
                            Add More
                        </button>}

                    </div>
                </div>

                {/* <Button variant="primary" onClick={handleSubmit}>Continue</Button> */}

            </Form>

        </>
    );
}

export default IncidentEdit;
