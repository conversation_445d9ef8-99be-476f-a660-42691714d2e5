import React,{useState} from 'react'
import Select from 'react-select'

function ActionView() {

  const [option,setOption] =useState([{label:'Observation',value:'obs'},{label:'ePermit Work',value:'eptw'},{label:'Document',value:'doc'},{label:'Risk',value:'ra'},{label:'Incident',value:'ir'}])

  return (
    <>
      <div className='row'>
        <div className='col-12'>
          <div className='col-3 mt-3 mb-2'>
            <Select
              labelKey="label"
              id="user_description"
              placeholder="Type..."
              options={option}
              className={'fw-bold'}
            />
          </div>
        </div>
        {/* <p className='mb-5 actionDesc '>Nam quo sed id quis. Odio est perferendis sit necessitatibus accusamus sint in.</p> */}
      </div>


    </>
  )
}

export default ActionView