import React, { useEffect,useState } from "react";
import { Line, Bar, Doughnut, Chart } from "react-chartjs-2";
import { ProgressBar, Tabs, Tab, Dropdown } from "react-bootstrap";
import Progress from "@delowar/react-circle-progressbar";
import API from "../services/API";
import {DASHBOARD_KA_ADMIN_DATA} from "../constants"
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
//Doughchart Legend

export const Dashboard =()=> {
  // constructor(props) {
  //   super(props);
  //   this.state = {
     
  //     inputValue: "",
  //     dashboard:{},
     
  //     loader:true
  //   };
    
  // }
  const history =useHistory()
  const [loader,setLoader] =useState(true)
  const [dashboard,setDashboard] =useState({})

  useEffect(() => {

    getChecklist();


  }, []);
 const getChecklist= async()=>{
    const response = await API.get(DASHBOARD_KA_ADMIN_DATA);
    if (response.status === 200) {
      setDashboard(response.data)
      setLoader(false)
      // this.setState({dashboard:response.data,loader:false})

    }
  }
 
 const southAmericaData = ()=>{


   return {datasets: [
      {
        data: [dashboard.green,dashboard.blue,dashboard.yellow,dashboard.red],
        backgroundColor: ["#7ed321", "#005284", "#f5a623", "#c94e57"],
        borderColor: ["#7ed321", "#005284", "#f5a623", "#c94e57"],
      },
    ],

    // These labels appear in the legend and in the tooltips when hovering different arcs
    labels: ["Total", "Net", "Gross", "AVG"],
    text: "",
  };
}
 
 const southAmericaOptions = {
    cutoutPercentage: 80,
    responsive: true,
    animationEasing: "easeOutBounce",
    animation: {
      animateScale: true,
      animateRotate: true,
    },
    maintainAspectRatio: true,
    showScale: true,
    legend: false,
  };

 

  
    return (
      <div>
        {loader ===false?
        <div className="row">
          <div className="col-12">
            <div className="card boxShadow">
              <div className="card-body ">
                <h4 className="text-center mb-4">Insights</h4>
                <div className="row">
                  <div
                    className="col-sm-6 d-flex flex-column align-items-center"
                    style={{ position: "relative" }}
                  >
                    <p style={{ position: "absolute", top: 60, fontSize: 30 }}>
                    {dashboard.insight}
                    </p>
                    <Doughnut
                      data={()=>southAmericaData()}
                      options={southAmericaOptions}
                      width={70}
                      height={18}
                    />
                    <h4 className="mt-3">Knowledge Index</h4>
                  </div>
                  <div
                    className="col-sm-6 d-flex flex-column align-items-center"
                    style={{ position: "relative" }}
                  >
                    <p style={{ position: "absolute", top: 60, fontSize: 30 }}>
                    {dashboard.pullRate}
                    </p>
                    <Progress
                      percent={dashboard.pullRate}
                      size={145}
                      borderWidth={15}
                      borderBgWidth={15}
                      fillColor={"#1F3BB3"}
                    />
                    <h4 className="mt-3">Pull Rate</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-12 mt-4">
            <div className="row">
              <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card  boxShadow" onClick={()=>history.push('/area')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4"  style={{color:'#1F3BB3'}}>
                          Knowledge Area
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.tierOne}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                            <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb' }}
                              ></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card   boxShadow"  onClick={()=>history.push('/topic')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4"  style={{color:'#1F3BB3'}}>
                          Knowledge Topic
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.tierTwo}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                              <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb'  }}
                              ></i> 
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card   boxShadow" onClick={()=>history.push('/unit')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4" style={{color:'#1F3BB3'}}>
                          Knowledge Unit
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.tierThree}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                            <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb' }}
                              ></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* <div className="col-12 ">
            <div className="row">
            <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card   boxShadow" onClick={()=>history.push('/apps/checklist')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4" style={{color:'#1F3BB3'}}>
                          Checklist
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.checklist}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                            <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb' }}
                              ></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card  boxShadow" onClick={()=>history.push('/apps/document')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4" style={{color:'#1F3BB3'}}>
                          Document
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.documents}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                            <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb' }}
                              ></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card  boxShadow" onClick={()=>history.push('/apps/forms')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4" style={{color:'#1F3BB3'}}>
                          Form
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.form}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                            <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb' }}
                              ></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div> */}
          {/* <div className="col-12">
            <div className="row">
            <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card boxShadow" onClick={()=>history.push('/users/enduser')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4" style={{color:'#1F3BB3'}}>
                          Users
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.user}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                            <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb' }}
                              ></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 d-flex flex-column">
                <div className="row flex-grow">
                  <div className="col-md-6 col-lg-12 grid-margin stretch-card">
                    <div className="card boxShadow" onClick={()=>history.push('/groups')}>
                      <div className="card-body ">
                        <h4 className="card-title card-title-dash mb-4" style={{color:'#1F3BB3'}}>
                          Groups
                        </h4>
                        <div className="row">
                          <div className="col-sm-10 pr-0">
                            <p className="status-summary-ight-white mb-1">
                              Total Value
                            </p>
                            <h2 style={{color:'rgb(31, 59, 179)'}}>{dashboard.group}</h2>
                          </div>
                          <div className="col-sm-2">
                            <div className="status-summary-chart-wrapper pb-4 text-center">
                              <i
                                className="mdi mdi-arrow-right"
                                style={{ fontSize: 25,color:'#05c3fb' }}
                              ></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
             
            </div>
          </div> */}

          
        </div>:''}
      </div>
    );
 
}

export default Dashboard;
