import React, { useState, useEffect } from "react";
import ListBox from "../form-elements/ListBox";
import { K_TIER1_TIER2_URL, K_TIER2_TIER3_URL,  K_EDIT_TIER_URL,TIER1_URL,TITTLE_CONFIG,TITLE_URL } from "../constants";
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { deletePopup, singlePopup } from "../notifications/Swal";
import { useSelector } from "react-redux";
import K_ListBox from "../form-elements/K_ListBox";


const Curate= () => {
   const history = useHistory();
    const [activityModal, setActivityModal] = useState(false);
    const [selectedLocation, setSelectedLocation] = useState({ value: '', label: '' });
    // const enterpriseId = useSelector((state) =>state.details.enterprise.id);

  


    const [title, setTitles] = useState({ tier1: 'Tier I', tier2: 'Tier II', tier3: 'Tier III' });

    useEffect(() => {
      
            getLocationConfigs();
            getLocationTier1();
        
            
    }, [])

    const getLocationConfigs = async () => {
        const response = await fetch(TITLE_URL)
        
        if (response.ok) {
            
            const locationConfig = await response.json();
            console.log(locationConfig)
            if(locationConfig.length !== 0){
               
                 setTitles({tier1: locationConfig[0].tier1, tier2: locationConfig[0].tier2, tier3: locationConfig[0].tier3 })
            }else{
                const newLocationConfig = await createLocationConfigs();
                setTitles({ tier1: newLocationConfig.tier1, tier2: newLocationConfig.tier2, tier3: newLocationConfig.tier3 })
            }
        }else{
            const newLocationConfig = await createLocationConfigs();
            setTitles({ tier1: newLocationConfig.tier1, tier2: newLocationConfig.tier2, tier3: newLocationConfig.tier3 })
        }
    }
    const createLocationConfigs = async () => {
        const response = await fetch(TITLE_URL, {
            method: 'POST',
            body: JSON.stringify({ tier1: 'Tier I', tier2: 'Tier II', tier3: 'Tier III'}),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }
        });
        if (response.ok) {
            const newLocationConfig = await response.json();
         
            return newLocationConfig;
        }
    }

    const handleOnEditLocationConfigTitle = async (value, mode) => {
        const response = await fetch(TITLE_URL, {
            method: 'PATCH',
            body: JSON.stringify({
                [mode]: value
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }
        })

        if (response.ok) {
            cogoToast.info('Updated', { position: 'top-right' })
        }
    }

    const getLocationTier1 = async () => {
        const response = await fetch(TIER1_URL);
        if (response.ok) {
            const tier1 = await response.json();
            setTier1(tier1);
        }
    }

    const [tier1, setTier1] = useState([]);
    const [selectedTier1, setSelectedTier1] = useState({ id: '' });

    useEffect(() => {
     
           
        
        // setSelectedTier1({ id: '' });
        setSelectedTier2({ id: '' });
        setSelectedTier3({ id: '' });
        // setSelectedTier4({ id: '' });
    }, [])

    const handleTier1Select = (id) => {
        setSelectedTier1(tier1.find(i => i.id === id))
    }

    const createTier1 = async (value) => {
        const response = await fetch(TIER1_URL, {
            method: 'POST',
            body: JSON.stringify({
                title: value
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }

        })
        if (response.ok) {
            const createdTier1 = await response.json();
            setTier1((prev) => [...prev, createdTier1]);
            cogoToast.info('Created!', { position: 'top-right' })

        }

    }

    const getTier1Tier2 = async () => {
        const response = await fetch(K_TIER1_TIER2_URL(selectedTier1.id));
        if (response.ok) {
            const tier2 = await response.json();
            setTier2(tier2);
        }
    }

    const [tier2, setTier2] = useState([]);
    const [selectedTier2, setSelectedTier2] = useState({ id: '' });

    useEffect(() => {
        if (selectedTier1.id !== '')
            getTier1Tier2();
        setSelectedTier2({ id: '' });
        setSelectedTier3({ id: '' });
        // setSelectedTier4({ id: '' });

    }, [selectedTier1.id])

    const handleTier2Select = (id) => {
        setSelectedTier2(tier2.find(i => i.id === id))
    }

    const createTier2 = async (value) => {
        const response = await fetch(K_TIER1_TIER2_URL(selectedTier1.id), {
            method: 'POST',
            body: JSON.stringify({
                title: value
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }

        })
        if (response.ok) {
            const createdTier2 = await response.json();
            setTier2((prev) => [...prev, createdTier2]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }

    const getTier2Tier3 = async () => {
        const response = await fetch(K_TIER2_TIER3_URL(selectedTier2.id));
        if (response.ok) {
            const tier3 = await response.json();
            setTier3(tier3);
        }
    }

    const [tier3, setTier3] = useState([]);
    const [selectedTier3, setSelectedTier3] = useState({ id: '' });

    useEffect(() => {
        if (selectedTier2.id !== '')
            getTier2Tier3();
        setSelectedTier3({ id: '' });
        // setSelectedTier4({ id: '' });

    }, [selectedTier2.id])

    const handleTier3Select = (id) => {
        setSelectedTier3(tier3.find(i => i.id === id))
    }

    const createTier3 = async (value) => {
        const response = await fetch(K_TIER2_TIER3_URL(selectedTier2.id), {
            method: 'POST',
            body: JSON.stringify({
                title: value
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }

        })
        if (response.ok) {
            const createdTier3 = await response.json();
            setTier3((prev) => [...prev, createdTier3]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }

  

    const handleDeleteItem = async (mode, id) => {

        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await fetch(K_EDIT_TIER_URL(mode, id), {
                    method: 'DELETE',
                    headers: {
                        "Content-type": "application/json; charset=UTF-8"
                    }
                })

                if (response.ok) {
                    switch (mode) {
                        case 'tier1':
                            setTier1(prev => prev.filter(i => i.id !== id))
                            setSelectedTier1({ id: '' });
                            setSelectedTier2({ id: '' });
                            setSelectedTier3({ id: '' });
                            // setSelectedTier4({ id: '' });
                            break;
                        case 'tier2':
                            setTier2(prev => prev.filter(i => i.id !== id))
                            setSelectedTier2({ id: '' });
                            setSelectedTier3({ id: '' });
                            // setSelectedTier4({ id: '' });

                            break;
                        case 'tier3':
                            setTier3(prev => prev.filter(i => i.id !== id))
                            setSelectedTier3({ id: '' });
                            // setSelectedTier4({ id: '' });

                            break;
                        // case 'tier4':

                        //     setTier4(prev => prev.filter(i => i.id !== id))
                        //     // setSelectedTier4({ id: '' });
                        //     break;

                        default: break;
                    }
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                }

            }
        })

    }
const handleCurateItem =  (mode,id) =>{
    console.log(mode,id)


     history.push('/knowledge/curate/edit',id)


}
    const handleCloneItem = async (mode, id) => {
        const response = await fetch(K_EDIT_TIER_URL(mode, id), {
            method: 'POST',
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }
        })

        if (response.ok) {
            singlePopup.fire(
                'Cloned!',
                '',
                'success'
            );
            switch (mode) {
                case 'tier1':
                    getLocationTier1();
                    break;
                case 'tier2':
                    getTier1Tier2();

                    break;
                case 'tier3':
                    getTier2Tier3();

                    break;
              
                default: break;

            }
        }
    }

    const handleActivityItem = async (mode, id) => {
        setActivityModal(true)
        // setFilterSubmit(prev => submitted.filter(i => i.testCase.tierMode === mode && i.testCase.tierId === id))
}

return (
    <>
        <div className="row">
            <div className="col-lg-12 p-0">


                <h4 className="card-title mb-4">Curate</h4>

              

               
                    <>
                        <div className="row">
                            <div className="col p-1 ps-3">
                                <K_ListBox handleActivity={handleActivityItem} handleClone={handleCloneItem} handleDeleteItem={handleDeleteItem} location={selectedLocation.value} documents={''} changeTitle={(id, value) => setTier1(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} checklist={''} title={title.tier1} onHandleCreateItem={createTier1} lists={tier1} selected={true} handleSelect={handleTier1Select} selectedItem={selectedTier1} onEditTitle={handleOnEditLocationConfigTitle} mode='tier1' />
                            </div>
                            <div className="col p-1">
                                <K_ListBox handleActivity={handleActivityItem} handleClone={handleCloneItem} handleDeleteItem={handleDeleteItem} location={selectedLocation.value} documents={''} changeTitle={(id, value) => setTier2(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} checklist={''} title={title.tier2} onHandleCreateItem={createTier2} lists={tier2} selected={selectedTier1.id !== ''} handleSelect={handleTier2Select} selectedItem={selectedTier2} onEditTitle={handleOnEditLocationConfigTitle} mode='tier2' />
                            </div>
                            <div className="col p-1">
                                <K_ListBox handleActivity={handleActivityItem} handleClone={handleCloneItem} handleCurate={handleCurateItem} handleDeleteItem={handleDeleteItem} location={selectedLocation.value} documents={''} changeTitle={(id, value) => setTier3(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} checklist={''} title={title.tier3} onHandleCreateItem={createTier3} lists={tier3} selected={selectedTier2.id !== ''} handleSelect={handleTier3Select} selectedItem={selectedTier3} onEditTitle={handleOnEditLocationConfigTitle} mode='tier3' />
                            </div>
                            {/* <div className="col p-1 pe-3">
                                <ListBox handleActivity={handleActivityItem} handleClone={handleCloneItem} handleDeleteItem={handleDeleteItem} location={selectedLocation.value} documents={''} changeTitle={(id, value) => setTier4(prev => prev.map(i => { return i.id === id ? { ...i, title: value } : i }))} checklist={''} title={title.tier4} onHandleCreateItem={createTier4} lists={tier4} selected={selectedTier3.id !== ''} handleSelect={handleTier4Select} selectedItem={selectedTier4} onEditTitle={handleOnEditLocationConfigTitle} mode='tier4' />
                            </div> */}


                        </div>
                    </>
         

                {/* {!selectedLocation.value &&

                    (<div className="w-25">
                        <Alert variant="primary">
                            Please Select Location from above to Continue
                        </Alert>
                    </div>)} */}



            </div>
        </div>

      

        
    </>
);

}

export default Curate;