import React, { Component } from 'react';

class Preview extends Component {

    render() {
        console.log(this.props.fields)
        return (
            <div id={this.props.id} tabIndex="-1" >

                <div className="mobile-preview-container preview-step-pg">
                    <div className='mobile-container'>
                        {
                            this.props.fields.map((field, index) => {
                                return this.renderField(field, index)
                            })
                        }
                    </div>
                </div>
            </div>


        )
    }

    componentWillReceiveProps() {
    }

    componentDidUpdate() {
        // setTimeout(() => {
        //     $('.date_time_picker').datetimepicker({
        //         timeFormat: "hh:mm tt"
        //     });
        //     $('.time_picker').timepicker({
        //         timeFormat: "hh:mm tt"
        //     });
        //     $('.date_picker').datepicker();
        // }, 0)
    }

    renderField(field, index) {
        if (field === undefined || index === -1) {
            return;
        }
        if (this.props.previews) {
            let Preview = this.props.previews.filter((tool) => {
                if (tool.states.toolType === field.toolType) {
                    return tool;
                } else {
                    return false;
                }
            })[0];
            if (Preview) {
                let PreviewClonedComponent = React.cloneElement(Preview.preview, field);
                return <div key={index}> {PreviewClonedComponent} </div>
            }
        }

        if (field.toolType === 'WEB_LINK') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <a href={field.title} >{field.title}</a>
                    </div>
                </div>
            )


        } else if (field.toolType === 'IMAGE') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <img src={'https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/form_image/' + field.title} style={{ maxWidth: '100%' }} />
                    </div>
                </div>
            )
        } else if (field.toolType === 'YOUTUBE') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <iframe sandbox="allow-same-origin allow-forms allow-popups allow-scripts allow-presentation" src={field.title} allowfullscreen=""></iframe>
                    </div>
                </div>
            )
        } else if (field.toolType === 'VIDEO') {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>

                        <video controls style={{ maxWidth: '100%' }}>
                            <source src={'https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/form_video/' + field.title} type="video/mp4" />
                            Your browser does not support video play.
                        </video>
                    </div>
                </div>
            )
        } else if (field.toolType === "PARAGRAPH") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p dangerouslySetInnerHTML={{ __html: field.content }} />

                    </div>
                </div>
            )
        } else if (field.toolType === "AUDIO") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <div className="col-12 text-center">
                            <audio controls style={{ maxWidth: '100%' }}>
                                <source src={'https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/form_audio/' + field.title} />
                            </audio>
                        </div>

                    </div>
                </div>
            )
        } else if (field.toolType === "PDF") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <div className="col-12 text-center">
                            <a href={'https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/pdf_uploads/' + field.title} target="_blank"><i className="fa fa-file-pdf-o fa-5x"></i><p style={{ textDecoration: 'none' }}>click to view</p></a></div>

                    </div>
                </div>
            )
        } else if (field.toolType === "EMBEDCODE") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <div className="col-12 text-center">
                            <div dangerouslySetInnerHTML={{ __html: field.title }} style={{ width: '100%' }} /></div>

                    </div>
                </div>
            )
        }
        else if (field.toolType === "MCQ") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p
                            dangerouslySetInnerHTML={{ __html: field.title }}
                        />
                        {field.radios.map(item => {
                            return (
                                <div className="form-check">
                                    <input


                                        className="form-check-input"
                                        type="checkbox"
                                        id="isRequired"
                                    />
                                    <label className="form-check-label" htmlFor="isRequired">
                                        {item.value}
                                    </label>
                                </div>
                            )
                        })}

                    </div>
                </div>
            )
        } else if (field.toolType === "TEXT_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>
                        <input


                            className="form-control"
                            type="text"
                            id="isRequired"
                        />
                    </div>
                </div>
            )
        } else if (field.toolType === "IMAGE_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "VIDEO_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "AUDIO_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "OPTION_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p
                            dangerouslySetInnerHTML={{ __html: field.title }}
                        />
                        {field.radios.map(item => {
                            return (
                                <div className="form-check">
                                    <input


                                        className="form-check-input"
                                        type="checkbox"
                                        id="isRequired"
                                    />
                                    <label className="form-check-label" htmlFor="isRequired">
                                        {item.value}
                                    </label>
                                </div>
                            )
                        })}

                    </div>
                </div>
            )
        } else if (field.toolType === "SIGN_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p>{field.title}</p>

                    </div>
                </div>
            )
        } else if (field.toolType === "CHECK_INPUT") {
            return (
                <div className='card mb-3'>
                    <div className='card-body boxShadow'>
                        <p
                            dangerouslySetInnerHTML={{ __html: field.title }}
                        />
                        {field.radios.map(item => {
                            return (
                                <div className="form-check">
                                    <input


                                        className="form-check-input"
                                        type="checkbox"
                                        id="isRequired"
                                    />
                                    <label className="form-check-label" htmlFor="isRequired">
                                        {item.value}
                                    </label>
                                </div>
                            )
                        })}

                    </div>
                </div>
            )
        } else if (field.toolType === "MULTIMEDIA") {
            return (
                <></>
            )
        }
    }
}

export default Preview;