import React, { Component } from "react";

class Weblink extends Component {
  constructor(props) {
    super(props);
    this.state = {
      toolType: "WEB_LINK",
      title: "",
      isEmpty:false
      
    };


  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue(stateFor, value) {
    switch (stateFor) {
      case "TITLE":

        this.setState({ title: value });
        break;
     

      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }
  createChecklistHandler() {
    this.setState({ mdShow: false });
  }
  render() {
    return (
      <>
        <div className="paragraph mb-3" style={ this.state.title ==="" && this.props.field.isEmpty?{boxShadow: '0px 0px 12px 3px #dfdfdf',border:'1px solid red'} :{boxShadow: '0px 0px 12px 3px #dfdfdf'}}>
          <div className="card">
            <div className="card-header d-flex justify-content-between">
              <div> <i className="fa fa-link mr-1"></i> Weblink</div>
             
            
              <div className="">
              {this.props.index !== 0 ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveUp(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-up"></i>
                  </span>
                  : ""}
                {this.props.index !== this.props.length ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveDown(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-down"></i>
                  </span>
                  : ''}
                <span
                  className=""
                  onClick={() => this.props.removeField(this.props.index)}
                >
                  <i className="mdi mdi-close"></i>
                </span>
              </div>
            </div>

            <div className="card-body">
              <div className="form-group">
                <label className="label" htmlFor="title">
                  Web Url
                </label>
                <input
                  id="title"
                  value={this.state.title}
                  onChange={(e) => this.changeValue("TITLE", e.target.value)}
                  placeholder="Copy or Enter Web URL."
                  className="form-control"
                  type="text"
                />


              </div>

              
              
             
        
             
            </div>
          </div>
        </div>
       
      </>
    );
  }
}

export default Weblink;
