import React, { useState } from "react";
import { useEffect } from "react";
import CheckboxTree from "react-checkbox-tree";
import "react-checkbox-tree/lib/react-checkbox-tree.css";
import API from "../services/API";
import { useSelector } from "react-redux";
import {
  ASSIGNED_AREA_BY_id,
  ASSIGNED_UNIT_URL,
  // ENTERPRISE_TIER1_URL,
  // ENTERPRISE_USER_URL,
  GROUP_URL,
  GROUP_ASSIGNED_AREA_BY_id,
  GROUP_ASSIGNED_UNIT_URL,
  USERS_URL,
  TIER1_URL
} from "../constants";
import Select from "react-select";
import { Form, Tab, Nav, Row, Col } from "react-bootstrap";
import cogoToast from "cogo-toast";
import { Link } from "react-router-dom/cjs/react-router-dom";
import Swal from "sweetalert2";

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-primary",
  },
  buttonsStyling: false,
});

function Assignment() {
  const [checked, setChecked] = useState([]);
  const [expanded, setExpanded] = useState([]);
  const [notes, setNotes] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [filteredNode, setFilteredNode] = useState([]);
  const [user, setUser] = useState([]);
  const [group, setGroup] = useState([]);
  const [userId, setUserId] = useState("");
  const [type, setType] = useState("");

  const onCheckTree = async (checked) => {
    setChecked(checked);
  };

  useEffect(() => {
   
      getTierList();
      getUserList();
       getGroupList();
    
  }, []);
  const getGroupList = async () => {
    const response = await API.get(GROUP_URL);
    if (response.status === 200) {
      let user = [];
      response.data.map((item) => {
        user.push({ label: item.name, value: item.id, name: "group" });
      });
      setGroup(user);
    }
  };
  const getUserList = async () => {
    const response = await API.get(USERS_URL);
    if (response.status === 200) {
      const data = response.data
      let user = [];
      data.map((item) => {
        user.push({ label: item.firstName, value: item.id, name: "user" });
      });
      setUser(user);
    }
  };
  const getTierList = async () => {
    const params = {
      include: [
        {
          relation: "topics",
          scope: { include: [{ relation: "units" }] },
        },
      ],
    };

    let url = `${TIER1_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`;
    const response = await API.get(url);
    if (response.status === 200) {
      let arr = [];

      response.data.forEach((item) => {
        let data = { value: item.id, label: item.title, children: [] };

        if (item.topics) {
          item.topics.forEach((item1) => {
            let data1 = { value: item1.id, label: item1.title, children: [] };
            if (item1.units) {
              item1.units.forEach((item2) => {
                data1.children.push({ value: item2.id, label: item2.title });
              });
            }

            // Only add data1 to data.children if it has children
            if (data1.children.length > 0) {
              data.children.push(data1);
            }
          });
        }

        // Only add data to arr if it has children
        if (data.children.length > 0) {
          arr.push(data);
        }
      });
      setNotes(arr);
      setFilteredNode(arr);
    }
  };
  const onFilterChange = (e) => {
    setFilterText(e.target.value);
    filterTree();
  };
  const filterTree = () => {
    if (!filterText) {
      setFilteredNode(notes);

      return;
    }
    setFilteredNode(notes.reduce(filterNodes, []));
    // this.setState({
    //     filteredNodes: nodes.reduce(this.filterNodes, []),
    // });
  };
  const filterNodes = (filtered, node) => {
    const children = (node.children || []).reduce(filterNodes, []);

    if (
      // Node's label matches the search string
      node.label.toLocaleLowerCase().indexOf(filterText.toLocaleLowerCase()) >
        -1 ||
      // Or a children has a matching node
      children.length
    ) {
      filtered.push({ ...node, children });
    }

    return filtered;
  };
  const onSelectUser = async (e) => {
    setUserId(e.value);
    setType(e.name);
    let arr = [];
    if (e.name === "user") {
      const response = await API.get(ASSIGNED_AREA_BY_id(e.value));
      if (response.status === 200) {
        response.data.map((item) => {
          arr.push(item.unitId);
        });
        setChecked(arr);
      }
    } else if (e.name === "group") {
      const response = await API.get(GROUP_ASSIGNED_AREA_BY_id(e.value));
      if (response.status === 200) {
        response.data.map((item) => {
          arr.push(item.unitId);
        });
        setChecked(arr);
      }
    }
  };
  const onSaveKa = async () => {
    if (type === "user") {
      const response = await API.post(ASSIGNED_UNIT_URL, {
        tierThreeId: checked,
        userid: userId,
      });
      if (response.status === 204) {
        customSwal2.fire("Knowledge Allocated!", "", "success");
      }
    } else if (type === "group") {
      const response = await API.post(GROUP_ASSIGNED_UNIT_URL, {
        tierThreeId: checked,
        groupid: userId,
      });
      if (response.status === 204) {
        customSwal2.fire("Knowledge Allocated!", "", "success");
      }
    }
  };
  const Option = [
    {
      label: "Groups",
      options: group,
    },
    {
      label: "Users",
      options: user,
    },
  ];
  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body ">
              <h4 className="card-title">Knowledge Assignment</h4>
                <Row>
                  <Col xs={12}>
                    <div className="row">
                      <div className="col-6">
                        <Select
                          labelKey="label"
                          id="user_description"
                          options={Option}
                          placeholder="Select User"
                          onChange={(e) => onSelectUser(e)}
                        />
                      </div>
                      <div className="row">
                        <div className="col-6 mt-4">
                          <div className="form-group">
                            <Form.Control
                              type="text"
                              value={filterText}
                              id="document_name"
                              placeholder="Search..."
                              onChange={(e) => onFilterChange(e)}
                            />
                          </div>

                          <div
                            className="col-12"
                            style={{
                              border: "2px solid hsl(0,0%,80%)",
                              padding: "10px 20px",
                              height: 400,
                              overflow: "auto",
                            }}
                          >
                            <CheckboxTree
                              nodes={filteredNode}
                              checked={checked}
                              expanded={expanded}
                              onCheck={(checked) => onCheckTree(checked)}
                              onExpand={(expanded) => setExpanded(expanded)}
                            />
                          </div>
                          <button
                            type="button"
                            className="btn btn-primary mt-4 mb-3 "
                            onClick={(e) => {
                              e.preventDefault();
                              onSaveKa();
                            }}
                          >
                            Assign Knowledge
                          </button>
                        </div>
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Assignment;
