import React, { Component, useState } from 'react'
import CardOverlay from './CardOverlay';
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';
const CostEstimator = () => {

    const [combinedInvoiceItems, setCombinedInvoiceItems] = useState([]);

    const handleUpdateApiData = (updatedApiData) => {
        setApiData(updatedApiData);
    };
    const handleAddCombinedInvoiceItem = (item, title) => {
        setCombinedInvoiceItems([...combinedInvoiceItems, { ...item, title }]);
    };

    const [apiData, setApiData] = useState([
        {
            title: "Direct Parts & Material Cost to Repair the Damage",
            items: []

        },
        {
            title: "Direct Labour Cost to Repair the Damage",
            items: []
        },
        {
            title: "Tools / Equipment / Loss Time Charges",
            items: []
        },
        {
            title: "Professional and Inspection Charges",
            items: []
        }
    ]);


    return (
        <>
            <CardOverlay>
                <h4 className="card-title">Cost Estimator Tool</h4>
                {apiData.map((data, index) => (
                    <InvoiceGenerator
                        key={index}
                        title={data.title}
                        addCombinedInvoiceItem={handleAddCombinedInvoiceItem}
                        initialItems={data.items}
                        updateApiData={handleUpdateApiData}
                        apiData={apiData}
                    />
                ))}

                <TotalCalculator apiData={apiData} />
            </CardOverlay>
        </>
    )

}

export default CostEstimator;
