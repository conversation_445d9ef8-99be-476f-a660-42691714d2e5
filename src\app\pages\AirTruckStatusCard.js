import React from 'react';
import Box from '@mui/material/Box';
import { STATIC_URL } from '../constants';

const AirTruckStatusCard = ({ data }) => {
    return (<>
        <Box>

            <div className="row mt-4">
                <div className="col-4">
                    <div className="form-group">
                        <label>Truck Status: {data.truckStatus ? (data.truckStatus ? 'Truck Released' : 'Truck on Hold') : '-'} </label>

                    </div>
                </div>
                <p className="h5 mb-4">Receipts:</p>
                <div className="form-group">
                    <label>{
                        (data.truckDocuments && data.truckDocuments.length > 0) ? data.truckDocuments.map(i => {
                            return (
                                <div>
                                    <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                </div>
                            )
                        }) : 'No Receipts Uploaded'
                    }</label>
                </div>
            </div>
        </Box>
    </>)
}

export default AirTruckStatusCard;