/* eslint-disable jsx-a11y/anchor-is-valid */
// @ts-nocheck
import React, { useState, useRef, useEffect } from "react";
import ListBoxItem from "./ListBoxItem";
import { Form } from "react-bootstrap";
import Editable from "react-bootstrap-editable";
import autoAnimate from "@formkit/auto-animate";
import { EDIT_TIER_URL } from "../constants";


const ListBox = (props) => {
    const animateListItem = useRef();
    const animateTestItem = useRef();
    const [showModal, setShowModal] = useState(false);
    const [tierDetails, setTierDetails] = useState({ title: '', certificate_date: new Date(), documents: [] });


    const title = useRef();
 

    const handleConfigurationModal = (id) => {

        getTierDetails(props.mode, id);

    }

   
    const parseDate = (value) => {
        const dateArray = value.split("/");
        return new Date(parseInt(dateArray[2]), parseInt(dateArray[1]) - 1, parseInt(dateArray[0]));
    }

    const getTierDetails = async (mode, id) => {
        const response = await fetch(EDIT_TIER_URL(mode, id));
        if (response.ok) {
            const tierData = await response.json();
          
 
            setTierDetails(tierData);
            setShowModal(true);
        }
    }

   
    const handleCreateItem = (e) => {
        e.preventDefault();
        // @ts-ignore
        props.onHandleCreateItem(title.current.value);
        // @ts-ignore
        title.current.value = '';
        

    }


    useEffect(() => { animateListItem.current && autoAnimate(animateListItem.current) }, [animateListItem])
    useEffect(() => { animateTestItem.current && autoAnimate(animateTestItem.current) }, [animateTestItem])

    const handleDeleteItem = (id) => {
        props.handleDeleteItem(props.mode, id)
    }

  
    return (<>
        <div className="card shadow">
            <div className="card-body p-0">
                <div className="card-title pencil-icon p-3 pb-0 mb-0">
                    <Editable initialValue={props.title} onSubmit={(value) => props.onEditTitle(value, props.mode)} className="d-flex" mode="inline" />
                </div>

                {
                    props.selected && (<>
                        <Form.Group className="form-group p-3 mb-0">
                            <div className="input-group">
                                <Form.Control type="text" ref={title} className="form-control mb-0" placeholder="Enter name..." aria-label="item" aria-describedby="basic-addon2" />
                                <div className="input-group-append">
                                    <button className="btn btn-primary btn-icon mb-0 z-0" type="button" onClick={(e) => { handleCreateItem(e) }}><i className="mdi mdi-plus-circle"></i></button>
                                </div>
                            </div>
                        </Form.Group>
                        <div className="h-250" ref={animateListItem}>
                            {
                                props.lists.map((i) => {
                                    return <ListBoxItem key={i.id} onHandleDelete={handleDeleteItem} selectedId={props.selectedItem.id} data={i} onHandleSelect={(id) => props.handleSelect(id)}  />
                                })
                            }


                        </div>
                    </>)
                }

                {!props.selected && (<p className="p-3">Please select a item from left pane to continue</p>)}


            </div>

        </div>

   
    </>);
}

export default ListBox;