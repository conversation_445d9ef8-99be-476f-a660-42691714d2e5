import React, { useState, useEffect, useMemo } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Row, Col, Card, Button, Form, Collapse } from 'react-bootstrap';
import Select from 'react-select';
import { Calendar } from 'primereact/calendar';
import * as Icon from 'feather-icons-react';
import moment from 'moment';
import {
  processRiskStatusData,
  processRiskTypeData,
  processRiskDepartmentData,
  processRiskLeaderData,
  processRiskMonthlyTrends,
  processRiskOverdueData,
  processRiskAdditionalControlsData,
  processRiskActivityData,
  calculateRiskSummaryStats,
  getPieChartOptions,
  getColumnChartOptions,
  getLineChartOptions
} from '../utils/chartUtils';

const RiskAssessmentAnalytics = ({ 
  data = [], 
  overdue = [], 
  additional = [], 
  aOverdue = [], 
  archived = [] 
}) => {
  const [filteredData, setFilteredData] = useState(data);
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: [],
    type: [],
    department: [],
    leader: [],
    dateRange: { start: null, end: null }
  });
  
  const [chartData, setChartData] = useState({
    status: [],
    type: [],
    department: [],
    leader: [],
    monthly: { categories: [], data: [] },
    overdue: [],
    additionalControls: [],
    activity: []
  });

  // Generate filter options from data
  const filterOptions = useMemo(() => {
    if (!data || data.length === 0) return {};
    
    const uniqueValues = (accessor) => {
      const values = data.map(accessor).filter(Boolean);
      return [...new Set(values)].sort().map(value => ({ value, label: value }));
    };

    return {
      status: uniqueValues(item => item.status),
      type: uniqueValues(item => item.type?.label),
      department: uniqueValues(item => item.department),
      leader: uniqueValues(item => item.captain || item.user?.firstName)
    };
  }, [data]);

  // Apply filters to data
  useEffect(() => {
    let filtered = [...data];

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(item => 
        filters.status.some(filter => filter.value === item.status)
      );
    }

    // Apply type filter
    if (filters.type.length > 0) {
      filtered = filtered.filter(item => 
        filters.type.some(filter => filter.value === item.type?.label)
      );
    }

    // Apply department filter
    if (filters.department.length > 0) {
      filtered = filtered.filter(item => 
        filters.department.some(filter => filter.value === item.department)
      );
    }

    // Apply leader filter
    if (filters.leader.length > 0) {
      filtered = filtered.filter(item => {
        const leader = item.captain || item.user?.firstName;
        return filters.leader.some(filter => filter.value === leader);
      });
    }

    // Apply date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.date, ['DD-MM-YYYY', 'DD-MM-YYYY HH:mm', moment.ISO_8601]);
        if (!itemDate.isValid()) return false;

        const start = filters.dateRange.start ? moment(filters.dateRange.start).startOf('day') : null;
        const end = filters.dateRange.end ? moment(filters.dateRange.end).endOf('day') : null;

        if (start && end) {
          return itemDate.isBetween(start, end, null, '[]');
        } else if (start) {
          return itemDate.isSameOrAfter(start);
        } else if (end) {
          return itemDate.isSameOrBefore(end);
        }
        return true;
      });
    }

    setFilteredData(filtered);
  }, [data, filters]);

  // Update chart data when filtered data changes
  useEffect(() => {
    if (filteredData && filteredData.length >= 0) {
      setChartData({
        status: processRiskStatusData(filteredData),
        type: processRiskTypeData(filteredData),
        department: processRiskDepartmentData(filteredData),
        leader: processRiskLeaderData(filteredData),
        monthly: processRiskMonthlyTrends(filteredData),
        overdue: processRiskOverdueData(filteredData),
        additionalControls: processRiskAdditionalControlsData(filteredData),
        activity: processRiskActivityData(filteredData)
      });
    }
  }, [filteredData]);

  // Filter handling functions
  const handleFilterChange = (filterType, selectedOptions) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: selectedOptions || []
    }));
  };

  const handleDateRangeChange = (field, date) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: date
      }
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      status: [],
      type: [],
      department: [],
      leader: [],
      dateRange: { start: null, end: null }
    });
  };

  const hasActiveFilters = () => {
    return Object.values(filters).some(filter => {
      if (Array.isArray(filter)) return filter.length > 0;
      if (typeof filter === 'object' && filter !== null) {
        return filter.start !== null || filter.end !== null;
      }
      return false;
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'dateRange') {
        if (value.start || value.end) count++;
      } else if (Array.isArray(value) && value.length > 0) {
        count++;
      }
    });
    return count;
  };

  // Calculate summary statistics
  const summaryStats = calculateRiskSummaryStats(filteredData, overdue, additional, aOverdue, archived);

  // Custom styles for react-select
  const selectStyles = {
    control: (provided) => ({
      ...provided,
      minHeight: '38px',
      border: '1px solid #ced4da',
      borderRadius: '0.375rem',
      '&:hover': {
        borderColor: '#007bff'
      }
    }),
    multiValue: (provided) => ({
      ...provided,
      backgroundColor: '#6f42c1',
      color: 'white'
    }),
    multiValueLabel: (provided) => ({
      ...provided,
      color: 'white'
    }),
    multiValueRemove: (provided) => ({
      ...provided,
      color: 'white',
      '&:hover': {
        backgroundColor: '#5a2d91',
        color: 'white'
      }
    })
  };

  // Chart options
  const statusChartOptions = {
    ...getPieChartOptions('Risk Assessment Status Distribution', 'Current status of all risk assessments'),
    series: [{
      name: 'Risk Assessments',
      colorByPoint: true,
      data: chartData.status
    }]
  };

  const typeChartOptions = {
    ...getPieChartOptions('Risk Assessment Type Distribution', 'Distribution by assessment types'),
    series: [{
      name: 'Risk Assessments',
      colorByPoint: true,
      data: chartData.type
    }]
  };

  const overdueChartOptions = {
    ...getPieChartOptions('Review Status Analysis', 'Overdue and upcoming reviews'),
    series: [{
      name: 'Risk Assessments',
      colorByPoint: true,
      data: chartData.overdue
    }]
  };

  const additionalControlsChartOptions = {
    ...getPieChartOptions('Additional Controls Analysis', 'Risk assessments requiring additional controls'),
    series: [{
      name: 'Risk Assessments',
      colorByPoint: true,
      data: chartData.additionalControls
    }]
  };

  const departmentChartOptions = {
    ...getColumnChartOptions('Top 10 Departments by Risk Assessments', 'Departments with highest RA counts'),
    series: [{
      name: 'Risk Assessments',
      data: chartData.department,
      colorByPoint: true
    }]
  };

  const leaderChartOptions = {
    ...getColumnChartOptions('Top 10 RA Leaders', 'Leaders with most risk assessments'),
    series: [{
      name: 'Risk Assessments',
      data: chartData.leader,
      colorByPoint: true
    }]
  };

  const activityChartOptions = {
    ...getColumnChartOptions('Top 10 Activities/Processes', 'Most common activities in risk assessments'),
    series: [{
      name: 'Risk Assessments',
      data: chartData.activity,
      colorByPoint: true
    }]
  };

  const monthlyTrendsOptions = {
    ...getLineChartOptions('Monthly Risk Assessment Trends', 'Risk assessments created over the last 12 months'),
    xAxis: {
      categories: chartData.monthly.categories
    },
    series: [{
      name: 'Risk Assessments',
      data: chartData.monthly.data,
      color: '#6f42c1'
    }],
    tooltip: {
      valueSuffix: ' risk assessments'
    }
  };

  const cardStyle = {
    marginBottom: '20px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    border: 'none'
  };

  const cardHeaderStyle = {
    backgroundColor: '#f8f9fa',
    borderBottom: '1px solid #dee2e6',
    padding: '15px 20px',
    fontSize: '16px',
    fontWeight: '600',
    color: '#495057'
  };

  return (
    <div className="risk-assessment-analytics">
      <div className="mb-4">
        <h2 className="text-purple">Risk Assessment Analytics Dashboard</h2>
        <p className="text-muted">Comprehensive analysis of risk assessment data with interactive charts</p>
      </div>

      {/* Summary Statistics */}
      <Row className="mb-4">
        <Col lg={12}>
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Summary Statistics
            </Card.Header>
            <Card.Body className="summary-stats">
              <Row>
                <Col md={2} className="text-center">
                  <h3 className="text-purple">{summaryStats.total}</h3>
                  <p className="text-muted">Total Risk Assessments</p>
                </Col>
                <Col md={2} className="text-center">
                  <h3 className="text-success">{summaryStats.published}</h3>
                  <p className="text-muted">Published</p>
                </Col>
                <Col md={2} className="text-center">
                  <h3 className="text-warning">{summaryStats.pending}</h3>
                  <p className="text-muted">Pending</p>
                </Col>
                <Col md={2} className="text-center">
                  <h3 className="text-danger">{summaryStats.overdue}</h3>
                  <p className="text-muted">Overdue for Review</p>
                </Col>
                <Col md={2} className="text-center">
                  <h3 className="text-info">{summaryStats.requiresAdditional}</h3>
                  <p className="text-muted">Requires Additional Controls</p>
                </Col>
                <Col md={2} className="text-center">
                  <h3 className="text-secondary">{summaryStats.archived}</h3>
                  <p className="text-muted">Archived</p>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Filters Section */}
      <Card style={cardStyle} className="mb-4">
        <Card.Header 
          style={{...cardHeaderStyle, cursor: 'pointer'}} 
          onClick={() => setFiltersOpen(!filtersOpen)}
        >
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <Icon.Filter size={18} className="me-2" />
              <span>Filters</span>
              {hasActiveFilters() && (
                <span className="badge bg-purple ms-2 position-relative">
                  {getActiveFiltersCount()}
                  <span className="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle">
                    <span className="visually-hidden">Active filters</span>
                  </span>
                </span>
              )}
            </div>
            <div className="d-flex align-items-center">
              {hasActiveFilters() && (
                <Button 
                  variant="outline-secondary" 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    clearAllFilters();
                  }}
                  className="me-2"
                >
                  <Icon.X size={14} className="me-1" />
                  Clear All
                </Button>
              )}
              <Icon.ChevronDown 
                size={18} 
                className={`transition-transform ${filtersOpen ? 'rotate-180' : ''}`}
                style={{ 
                  transform: filtersOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s ease'
                }}
              />
            </div>
          </div>
        </Card.Header>
        <Collapse in={filtersOpen}>
          <Card.Body className="filters-section">
            <Row>
              <Col md={2} className="mb-3">
                <Form.Label>Status</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.status || []}
                  value={filters.status}
                  onChange={(selected) => handleFilterChange('status', selected)}
                  placeholder="Select status..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Type</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.type || []}
                  value={filters.type}
                  onChange={(selected) => handleFilterChange('type', selected)}
                  placeholder="Select type..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Department</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.department || []}
                  value={filters.department}
                  onChange={(selected) => handleFilterChange('department', selected)}
                  placeholder="Select department..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>RA Leader</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.leader || []}
                  value={filters.leader}
                  onChange={(selected) => handleFilterChange('leader', selected)}
                  placeholder="Select leader..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Date From</Form.Label>
                <Calendar
                  value={filters.dateRange.start}
                  onChange={(e) => handleDateRangeChange('start', e.value)}
                  placeholder="Select start date"
                  dateFormat="dd/mm/yy"
                  showIcon
                  className="w-100"
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Date To</Form.Label>
                <Calendar
                  value={filters.dateRange.end}
                  onChange={(e) => handleDateRangeChange('end', e.value)}
                  placeholder="Select end date"
                  dateFormat="dd/mm/yy"
                  showIcon
                  className="w-100"
                />
              </Col>
            </Row>
            <Row>
              <Col>
                <div className="filter-summary">
                  <div className="text-muted small">
                    Showing <strong>{filteredData.length}</strong> of <strong>{data.length}</strong> risk assessments
                    {hasActiveFilters() && (
                      <span className="text-purple"> ({getActiveFiltersCount()} filter{getActiveFiltersCount() !== 1 ? 's' : ''} applied)</span>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Collapse>
      </Card>

      <Row>
        {/* Status Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Status Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={statusChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Type Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Type Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={typeChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Review Status Analysis */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Review Status Analysis
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={overdueChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Additional Controls Analysis */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Additional Controls Analysis
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={additionalControlsChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Monthly Trends */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Monthly Trends
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={monthlyTrendsOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Top Departments */}
        <Col lg={4} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Departments
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={departmentChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Top RA Leaders */}
        <Col lg={4} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top RA Leaders
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={leaderChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Top Activities */}
        <Col lg={4} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Activities/Processes
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={activityChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RiskAssessmentAnalytics;
