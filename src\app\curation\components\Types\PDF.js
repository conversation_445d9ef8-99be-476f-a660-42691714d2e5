import React, { Component } from "react";
import S3 from "react-aws-s3";
import FullLoader from "../../../shared/FullLoader";
class PDF extends Component {
  constructor(props) {
    super(props);
    this.state = {
      toolType: "PDF",
      title: "",
      caption:"",
      isEmpty: false,
      loader:false
    };


    this.config = {
      bucketName: "sagt",
      region: "ap-southeast-1",
      accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY,
      secretAccessKey: process.env.REACT_APP_AWS_SECRET_KEY,
    };
  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue = async (stateFor, value) => {
    switch (stateFor) {
      case "TITLE":
        if(value.length ===1){
        this.setState({loader:true})
        const ReactS3Client = new S3(this.config);
        const filename = new Date().getTime() + value[0].name

        await ReactS3Client.uploadFile(value[0], 'uploads/pdf_uploads/' + filename)
          .then((data) => console.log(data))
          .catch((err) => console.error(err));
        this.setState({ title: filename });
        this.setState({loader:false})
        }
        break;
      case "CAPTION":
        this.setState({ caption: value });
        break;

      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }
  createChecklistHandler() {
    this.setState({ mdShow: false });
  }
  render() {
    return (
      <>
        <div
          className="paragraph mb-3"
          style={this.state.title === '' && this.props.field.isEmpty ? { boxShadow: '0px 0px 12px 3px #dfdfdf', border: '1px solid red', borderRadius: 0 } : { boxShadow: '0px 0px 12px 3px #dfdfdf', borderRadius: 0 }}
        >
          <div className="card">
            <div className="card-header d-flex justify-content-between">
              <div>
              <i className="fa fa-file-pdf-o mr-1"></i> PDF
              </div>
              
             
              <div className="">
              {this.props.index !== 0 ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveUp(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-up"></i>
                  </span>
                  : ""}
                {this.props.index !== this.props.length ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveDown(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-down"></i>
                  </span>
                  : ''}
                <span
                  className=""
                  onClick={() => this.props.removeField(this.props.index)}
                >
                  <i className="mdi mdi-close"></i>
                </span>
              </div>
            </div>

            <div className="card-body">
              {/* <div className="form-group">
                <label className="label" htmlFor="title">
                  Caption
                </label>
                <input
                  id="title"
                  defaultValue={this.state.caption}
                  onChange={(e) => this.changeValue("CAPTION", e.target.value)}
                  className="form-control"
                  type="text"
                />
              </div> */}

              <div className="form-group">
                <label className="label" htmlFor="upload">
                  Pdf Upload
                </label>
                <input
                  id="upload"

                  onChange={(e) => this.changeValue("TITLE", e.target.files)}
                  className="form-control"
                  type="file"
                  accept="application/pdf,application/vnd.ms-excel"
                />
              </div>
              {this.state.title !== '' ?
                <div className="col-12 text-center">
                  <a href={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/pdf_uploads/' + this.state.title} target="_blank"><i className="fa fa-file-pdf-o fa-5x"></i><p style={{textDecoration:'none'}}>click to view</p></a></div> :
                ''}
                 {this.state.loader && <FullLoader />}
            </div>
          </div>
        </div>
      </>
    );
  }
}
export default PDF;
