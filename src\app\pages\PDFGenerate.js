import React from "react"
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import Logo from '../../assets/images/logo.png'
// Register the fonts
pdfMake.vfs = pdfFonts.pdfMake.vfs;
const PDFGenerate = () => {

    const generatePDF = () => {
        const documentDefinition = {
            content: [	
                 {image:Logo, style:'logoImg'},
                 {text: 'Risk Assessment', style: 'header'},
                 {
                    style: 'tableExample',
                    table: {
                        widths: [100, '*'],
                        body: [
                            ['RA ID', 'star-sized'],
                            ['Type','']
                        ]
                    }
                },
                
        ],
        styles: {
        
            logoImg:{
                alignment:'center',
                width:150
            },
            header: {
                fontSize: 18,
                bold: true,
                margin: [0, 0, 0, 10]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 10, 0, 5]
            },
        },
        defaultStyle: {
            // alignment: 'justify'
        }
    }
       pdfMake.createPdf(documentDefinition).download('sable.pdf');
    }

    return (

        <button onClick={()=>generatePDF()}>Generate PDF</button>
       


    )
}
export default PDFGenerate