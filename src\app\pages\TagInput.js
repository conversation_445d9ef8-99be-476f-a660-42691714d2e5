import React, { useState } from "react";
import ReactTags from "react-tag-autocomplete";

const TagInput = () => {
    const [tags, setTags] = useState([]);

    const handleDelete = (i) => {
        const newTags = tags.filter((tag, index) => index !== i);
        setTags(newTags);
    };

    const handleAddition = (tag) => {
        setTags([...tags, tag]);
    };

    return (
        <>
            <ReactTags
                tags={tags}
                allowNew={true}
                placeholderText="Enter Email Address"
                handleDelete={handleDelete}
                handleAddition={handleAddition}
            />
        </>
    );
};

export default TagInput;
