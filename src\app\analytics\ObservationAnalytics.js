import React, { useState, useEffect } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Row, Col, Card } from 'react-bootstrap';
import { 
  processStatusData, 
  processCategoryData, 
  processTypeData, 
  processSeverityData,
  processDepartmentData,
  processMonthlyTrends,
  processActionCompletionData,
  processOverdueData,
  getPieChartOptions,
  getColumnChartOptions,
  getLineChartOptions
} from '../utils/chartUtils';

const ObservationAnalytics = ({ data = [] }) => {
  const [chartData, setChartData] = useState({
    status: [],
    category: [],
    type: [],
    severity: [],
    department: [],
    monthly: { categories: [], data: [] },
    actionCompletion: [],
    overdue: []
  });

  useEffect(() => {
    if (data && data.length > 0) {
      setChartData({
        status: processStatusData(data),
        category: processCategoryData(data),
        type: processTypeData(data),
        severity: processSeverityData(data),
        department: processDepartmentData(data),
        monthly: processMonthlyTrends(data),
        actionCompletion: processActionCompletionData(data),
        overdue: processOverdueData(data)
      });
    }
  }, [data]);

  // Status Distribution Chart
  const statusChartOptions = {
    ...getPieChartOptions('Observation Status Distribution', 'Current status of all observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.status
    }]
  };

  // Category Distribution Chart
  const categoryChartOptions = {
    ...getPieChartOptions('Category Distribution', 'Health, Safety, and Environment observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.category
    }]
  };

  // Type Distribution Chart
  const typeChartOptions = {
    ...getPieChartOptions('Type Distribution', 'Unsafe Acts, Unsafe Conditions, and Positive observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.type
    }]
  };

  // Severity Distribution Chart
  const severityChartOptions = {
    ...getPieChartOptions('Severity Distribution', 'High, Medium, and Low severity observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.severity
    }]
  };

  // Department-wise Chart
  const departmentChartOptions = {
    ...getColumnChartOptions('Top 10 Departments by Observations', 'Departments with highest observation counts'),
    series: [{
      name: 'Observations',
      data: chartData.department,
      colorByPoint: true
    }]
  };

  // Monthly Trends Chart
  const monthlyTrendsOptions = {
    ...getLineChartOptions('Monthly Observation Trends', 'Observation count over the last 12 months'),
    xAxis: {
      categories: chartData.monthly.categories
    },
    series: [{
      name: 'Observations',
      data: chartData.monthly.data,
      color: '#007bff'
    }]
  };

  // Action Completion Chart
  const actionCompletionOptions = {
    ...getPieChartOptions('Action Completion Status', 'Completed vs Pending actions'),
    series: [{
      name: 'Actions',
      colorByPoint: true,
      data: chartData.actionCompletion
    }]
  };

  // Overdue Analysis Chart
  const overdueOptions = {
    ...getPieChartOptions('Due Date Analysis', 'Overdue, due soon, and upcoming observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.overdue
    }]
  };

  const cardStyle = {
    marginBottom: '20px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    border: 'none'
  };

  const cardHeaderStyle = {
    backgroundColor: '#f8f9fa',
    borderBottom: '1px solid #dee2e6',
    padding: '15px 20px',
    fontSize: '16px',
    fontWeight: '600',
    color: '#495057'
  };

  return (
    <div className="observation-analytics">
      <div className="mb-4">
        <h2 className="text-primary">Observation Analytics Dashboard</h2>
        <p className="text-muted">Comprehensive analysis of observation data with interactive charts</p>
      </div>
      
      <Row>
        {/* Status Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Status Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={statusChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Category Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Category Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={categoryChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Type Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Type Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={typeChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Severity Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Severity Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={severityChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Monthly Trends */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Monthly Trends
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={monthlyTrendsOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Department-wise Analysis */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Departments by Observations
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={departmentChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Action Completion */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Action Completion Status
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={actionCompletionOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Overdue Analysis */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Due Date Analysis
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={overdueOptions}
              />
            </Card.Body>
          </Card>
        </Col>
      </Row>

    </div>
  );
};

export default ObservationAnalytics;
