import React, { useState, useEffect, useMemo } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Row, Col, Card, Button, Form, Collapse } from 'react-bootstrap';
import Select from 'react-select';
import { Calendar } from 'primereact/calendar';
import * as Icon from 'feather-icons-react';
import moment from 'moment';
import {
  processStatusData,
  processCategoryData,
  processTypeData,
  processSeverityData,
  processDepartmentData,
  processMonthlyTrends,
  processActionCompletionData,
  processOverdueData,
  getPieChartOptions,
  getColumnChartOptions,
  getLineChartOptions
} from '../utils/chartUtils';

const ObservationAnalytics = ({ data = [] }) => {
  const [filteredData, setFilteredData] = useState(data);
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: [],
    category: [],
    type: [],
    severity: [],
    department: [],
    reportedBy: [],
    dateRange: { start: null, end: null }
  });

  const [chartData, setChartData] = useState({
    status: [],
    category: [],
    type: [],
    severity: [],
    department: [],
    monthly: { categories: [], data: [] },
    actionCompletion: [],
    overdue: []
  });

  // Generate filter options from data
  const filterOptions = useMemo(() => {
    if (!data || data.length === 0) return {};

    const uniqueValues = (field, accessor) => {
      const values = data.map(accessor).filter(Boolean);
      return [...new Set(values)].sort().map(value => ({ value, label: value }));
    };

    return {
      status: uniqueValues('status', item => item.status),
      category: uniqueValues('category', item => item.category),
      type: uniqueValues('type', item => item.type),
      severity: uniqueValues('severity', item => item.severity),
      department: uniqueValues('department', item => item.workActivityDepartment?.name),
      reportedBy: uniqueValues('reportedBy', item => item.submitted?.firstName)
    };
  }, [data]);

  // Apply filters to data
  useEffect(() => {
    let filtered = [...data];

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(item =>
        filters.status.some(filter => filter.value === item.status)
      );
    }

    // Apply category filter
    if (filters.category.length > 0) {
      filtered = filtered.filter(item =>
        filters.category.some(filter => filter.value === item.category)
      );
    }

    // Apply type filter
    if (filters.type.length > 0) {
      filtered = filtered.filter(item =>
        filters.type.some(filter => filter.value === item.type)
      );
    }

    // Apply severity filter
    if (filters.severity.length > 0) {
      filtered = filtered.filter(item =>
        filters.severity.some(filter => filter.value === item.severity)
      );
    }

    // Apply department filter
    if (filters.department.length > 0) {
      filtered = filtered.filter(item =>
        filters.department.some(filter => filter.value === item.workActivityDepartment?.name)
      );
    }

    // Apply reported by filter
    if (filters.reportedBy.length > 0) {
      filtered = filtered.filter(item =>
        filters.reportedBy.some(filter => filter.value === item.submitted?.firstName)
      );
    }

    // Apply date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.created, ['Do MMM YYYY', 'DD-MM-YYYY', moment.ISO_8601]);
        if (!itemDate.isValid()) return false;

        const start = filters.dateRange.start ? moment(filters.dateRange.start).startOf('day') : null;
        const end = filters.dateRange.end ? moment(filters.dateRange.end).endOf('day') : null;

        if (start && end) {
          return itemDate.isBetween(start, end, null, '[]');
        } else if (start) {
          return itemDate.isSameOrAfter(start);
        } else if (end) {
          return itemDate.isSameOrBefore(end);
        }
        return true;
      });
    }

    setFilteredData(filtered);
  }, [data, filters]);

  // Update chart data when filtered data changes
  useEffect(() => {
    if (filteredData && filteredData.length >= 0) {
      setChartData({
        status: processStatusData(filteredData),
        category: processCategoryData(filteredData),
        type: processTypeData(filteredData),
        severity: processSeverityData(filteredData),
        department: processDepartmentData(filteredData),
        monthly: processMonthlyTrends(filteredData),
        actionCompletion: processActionCompletionData(filteredData),
        overdue: processOverdueData(filteredData)
      });
    }
  }, [filteredData]);

  // Filter handling functions
  const handleFilterChange = (filterType, selectedOptions) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: selectedOptions || []
    }));
  };

  const handleDateRangeChange = (field, date) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: date
      }
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      status: [],
      category: [],
      type: [],
      severity: [],
      department: [],
      reportedBy: [],
      dateRange: { start: null, end: null }
    });
  };

  const hasActiveFilters = () => {
    return Object.values(filters).some(filter => {
      if (Array.isArray(filter)) return filter.length > 0;
      if (typeof filter === 'object' && filter !== null) {
        return filter.start !== null || filter.end !== null;
      }
      return false;
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'dateRange') {
        if (value.start || value.end) count++;
      } else if (Array.isArray(value) && value.length > 0) {
        count++;
      }
    });
    return count;
  };

  // Custom styles for react-select
  const selectStyles = {
    control: (provided) => ({
      ...provided,
      minHeight: '38px',
      border: '1px solid #ced4da',
      borderRadius: '0.375rem',
      '&:hover': {
        borderColor: '#007bff'
      }
    }),
    multiValue: (provided) => ({
      ...provided,
      backgroundColor: '#007bff',
      color: 'white'
    }),
    multiValueLabel: (provided) => ({
      ...provided,
      color: 'white'
    }),
    multiValueRemove: (provided) => ({
      ...provided,
      color: 'white',
      '&:hover': {
        backgroundColor: '#0056b3',
        color: 'white'
      }
    })
  };

  // Status Distribution Chart
  const statusChartOptions = {
    ...getPieChartOptions('Observation Status Distribution', 'Current status of all observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.status
    }]
  };

  // Category Distribution Chart
  const categoryChartOptions = {
    ...getPieChartOptions('Category Distribution', 'Health, Safety, and Environment observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.category
    }]
  };

  // Type Distribution Chart
  const typeChartOptions = {
    ...getPieChartOptions('Type Distribution', 'Unsafe Acts, Unsafe Conditions, and Positive observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.type
    }]
  };

  // Severity Distribution Chart
  const severityChartOptions = {
    ...getPieChartOptions('Severity Distribution', 'High, Medium, and Low severity observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.severity
    }]
  };

  // Department-wise Chart
  const departmentChartOptions = {
    ...getColumnChartOptions('Top 10 Departments by Observations', 'Departments with highest observation counts'),
    series: [{
      name: 'Observations',
      data: chartData.department,
      colorByPoint: true
    }]
  };

  // Monthly Trends Chart
  const monthlyTrendsOptions = {
    ...getLineChartOptions('Monthly Observation Trends', 'Observation count over the last 12 months'),
    xAxis: {
      categories: chartData.monthly.categories
    },
    series: [{
      name: 'Observations',
      data: chartData.monthly.data,
      color: '#007bff'
    }]
  };

  // Action Completion Chart
  const actionCompletionOptions = {
    ...getPieChartOptions('Action Completion Status', 'Completed vs Pending actions'),
    series: [{
      name: 'Actions',
      colorByPoint: true,
      data: chartData.actionCompletion
    }]
  };

  // Overdue Analysis Chart
  const overdueOptions = {
    ...getPieChartOptions('Due Date Analysis', 'Overdue, due soon, and upcoming observations'),
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: chartData.overdue
    }]
  };

  const cardStyle = {
    marginBottom: '20px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    border: 'none'
  };

  const cardHeaderStyle = {
    backgroundColor: '#f8f9fa',
    borderBottom: '1px solid #dee2e6',
    padding: '15px 20px',
    fontSize: '16px',
    fontWeight: '600',
    color: '#495057'
  };

  return (
    <div className="observation-analytics">
      <div className="mb-4">
        <h2 className="text-primary">Observation Analytics Dashboard</h2>
        <p className="text-muted">Comprehensive analysis of observation data with interactive charts</p>
      </div>

      {/* Filters Section */}
      <Card style={cardStyle} className="mb-4">
        <Card.Header
          style={{...cardHeaderStyle, cursor: 'pointer'}}
          onClick={() => setFiltersOpen(!filtersOpen)}
        >
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <Icon.Filter size={18} className="me-2" />
              <span>Filters</span>
              {hasActiveFilters() && (
                <span className="badge bg-primary ms-2 position-relative">
                  {getActiveFiltersCount()}
                  <span className="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle">
                    <span className="visually-hidden">Active filters</span>
                  </span>
                </span>
              )}
            </div>
            <div className="d-flex align-items-center">
              {hasActiveFilters() && (
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    clearAllFilters();
                  }}
                  className="me-2"
                >
                  <Icon.X size={14} className="me-1" />
                  Clear All
                </Button>
              )}
              <Icon.ChevronDown
                size={18}
                className={`transition-transform ${filtersOpen ? 'rotate-180' : ''}`}
                style={{
                  transform: filtersOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s ease'
                }}
              />
            </div>
          </div>
        </Card.Header>
        <Collapse in={filtersOpen}>
          <Card.Body className="filters-section">
          <Row>
            <Col md={2} className="mb-3">
              <Form.Label>Status</Form.Label>
              <Select
                isMulti
                options={filterOptions.status || []}
                value={filters.status}
                onChange={(selected) => handleFilterChange('status', selected)}
                placeholder="Select status..."
                styles={selectStyles}
                isClearable
              />
            </Col>
            <Col md={2} className="mb-3">
              <Form.Label>Category</Form.Label>
              <Select
                isMulti
                options={filterOptions.category || []}
                value={filters.category}
                onChange={(selected) => handleFilterChange('category', selected)}
                placeholder="Select category..."
                styles={selectStyles}
                isClearable
              />
            </Col>
            <Col md={2} className="mb-3">
              <Form.Label>Type</Form.Label>
              <Select
                isMulti
                options={filterOptions.type || []}
                value={filters.type}
                onChange={(selected) => handleFilterChange('type', selected)}
                placeholder="Select type..."
                styles={selectStyles}
                isClearable
              />
            </Col>
            <Col md={2} className="mb-3">
              <Form.Label>Severity</Form.Label>
              <Select
                isMulti
                options={filterOptions.severity || []}
                value={filters.severity}
                onChange={(selected) => handleFilterChange('severity', selected)}
                placeholder="Select severity..."
                styles={selectStyles}
                isClearable
              />
            </Col>
            <Col md={2} className="mb-3">
              <Form.Label>Department</Form.Label>
              <Select
                isMulti
                options={filterOptions.department || []}
                value={filters.department}
                onChange={(selected) => handleFilterChange('department', selected)}
                placeholder="Select department..."
                styles={selectStyles}
                isClearable
              />
            </Col>
            <Col md={2} className="mb-3">
              <Form.Label>Reported By</Form.Label>
              <Select
                isMulti
                options={filterOptions.reportedBy || []}
                value={filters.reportedBy}
                onChange={(selected) => handleFilterChange('reportedBy', selected)}
                placeholder="Select reporter..."
                styles={selectStyles}
                isClearable
              />
            </Col>
            <Col md={2} className="mb-3">
              <Form.Label>Date From</Form.Label>
              <Calendar
                value={filters.dateRange.start}
                onChange={(e) => handleDateRangeChange('start', e.value)}
                placeholder="Select start date"
                dateFormat="dd/mm/yy"
                showIcon
                className="w-100"
              />
            </Col>
            <Col md={2} className="mb-3">
              <Form.Label>Date To</Form.Label>
              <Calendar
                value={filters.dateRange.end}
                onChange={(e) => handleDateRangeChange('end', e.value)}
                placeholder="Select end date"
                dateFormat="dd/mm/yy"
                showIcon
                className="w-100"
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <div className="filter-summary">
                <div className="text-muted small">
                  Showing <strong>{filteredData.length}</strong> of <strong>{data.length}</strong> observations
                  {hasActiveFilters() && (
                    <span className="text-primary"> ({getActiveFiltersCount()} filter{getActiveFiltersCount() !== 1 ? 's' : ''} applied)</span>
                  )}
                </div>
              </div>
            </Col>
          </Row>
          </Card.Body>
        </Collapse>
      </Card>

      <Row>

        {/* Summary Statistics */}
        <Row className="mt-4">
          <Col lg={12}>
            <Card style={cardStyle}>
              <Card.Header style={cardHeaderStyle}>
                Summary Statistics
              </Card.Header>
              <Card.Body className="summary-stats">
                <Row>
                  <Col md={3} className="text-center">
                    <h3 className="text-primary">{filteredData.length}</h3>
                    <p className="text-muted">Total Observations</p>
                  </Col>
                  <Col md={3} className="text-center">
                    <h3 className="text-success">
                      {chartData.actionCompletion.find(item => item.name === 'Completed')?.y || 0}
                    </h3>
                    <p className="text-muted">Completed Actions</p>
                  </Col>
                  <Col md={3} className="text-center">
                    <h3 className="text-warning">
                      {chartData.actionCompletion.find(item => item.name === 'Pending')?.y || 0}
                    </h3>
                    <p className="text-muted">Pending Actions</p>
                  </Col>
                  <Col md={3} className="text-center">
                    <h3 className="text-danger">
                      {chartData.overdue.find(item => item.name === 'Overdue')?.y || 0}
                    </h3>
                    <p className="text-muted">Overdue Items</p>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Status Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Status Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={statusChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Category Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Category Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={categoryChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Type Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Type Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={typeChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Severity Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Severity Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={severityChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Monthly Trends */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Monthly Trends
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={monthlyTrendsOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Department-wise Analysis */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Departments by Observations
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={departmentChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Action Completion */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Action Completion Status
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={actionCompletionOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Overdue Analysis */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Due Date Analysis
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={overdueOptions}
              />
            </Card.Body>
          </Card>
        </Col>
      </Row>

    </div>
  );
};

export default ObservationAnalytics;
