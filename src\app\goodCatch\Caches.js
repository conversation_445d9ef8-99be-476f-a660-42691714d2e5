import React, { Component, useState, useEffect, useRef } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { OBSERVATION_REPORT_BY_OTHERS_URL, OBSERVATION_REPORT_URL, GET_USER_ROLE_BY_MODE, USERS_URL, OBSERVATION_REPORT_WITH_ID, STATIC_URL, LOCATION_THREE, TIER2_TIER3_URL } from '../constants';
import { Button } from 'primereact/button';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import ObservationModal from '../pages/ObservationModal';
// import { observationColumns, tableOptions } from './TableColumns';
import CardOverlay from '../pages/CardOverlay';
import { DropzoneArea } from "material-ui-dropzone";
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';

import { Checkbox } from '@mui/material';
import moment from 'moment'
import { Badge, Modal, Form, Alert } from 'react-bootstrap';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import Select from "react-select";
import { Calendar } from 'primereact/calendar';
import GoodCatchModal from './GoodCatchModal';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const Catches = ({ obsdata }) => {
    console.log(obsdata)
    const dt = useRef()
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [showOverdue, setShowOverdue] = useState(false);
    const [project, setProject] = useState([])
    const [user, setUser] = useState([])
    const [users, setUsers] = useState([])
    const [filters, setFilters] = useState(null);
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [showModal, setShowModal] = useState(false)
    const history = useHistory();
    const thead = [
        'id',
        'Type',
        'Category',
        'Description',
        'Rectified Status',
        'Remarks',
        'Action Taken',
    ];
    const defaultMaterialTheme = createTheme();
    const [formData, setFormData] = useState({
        whatDidYouObserve: "",
        whereDidYouObserve: "",
        whatCouldHaveGoneWrong: "",
        preventiveAction: "",
        workActivity: "",
        uploads: [],
        importanceRating: "",
        adminId: null,
        locationThreeId: null,
        locationFourId: null
    });
    const [data, setData] = useState(obsdata);
    const [locationThree, setLocationThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    const [admin, setAdmin] = useState([]);
    const [filterData, setFilterData] = useState(obsdata);
    const [assignee, setAssignee] = useState([])
    const [source, setSource] = useState([])
    const [category, setCategory] = useState([{ name: 'Health', value: 'Health' }, { name: 'Safety', value: 'Safety' }, { name: 'Environment', value: 'Environment' }])
    useEffect(() => {
        const preprocessObservationData = () => {
            if (!obsdata) return;

            // Preprocess the data to add `obsSourceName`
            const preprocessedData = obsdata.map(item => {
                const remarksData = JSON.parse(item.remarks || '{}');
                return {
                    ...item,
                    obsSourceName: remarksData.obsSourceName || '' // Adding obsSourceName directly to data
                };
            });

            // Set the preprocessed data
            setData(preprocessedData);
            setFilterData(preprocessedData);

            // Initialize filters
            initFilters();
        };

        // Call external data fetching functions
        const fetchInitialData = async () => {
            await getAllUsers();   // Assuming this is an async call
            await getObservationData(); // Assuming this is also an async call
        };

        // Only run when obsdata exists
        if (obsdata) {
            fetchInitialData();
            preprocessObservationData();
            getUserList()
            getLoactionThree();
        }

    }, [obsdata]);

    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)


    }

    const getLoactionThree = async () => {
        const response = await API.get(LOCATION_THREE);
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {

                // if (item.id !== user.id) {
                data.push({ label: item.name, value: item.id });
                // }
            });

            setLocationThree(data)

        }


    }

    const getUserList = async () => {
        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'good-catch-program-administrator'
        });
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {

                // if (item.id !== user.id) {
                data.push({ label: item.firstName, value: item.id });
                // }
            });

            setAdmin(data)

        }
    }

    const [errors, setErrors] = useState({});
    const [showErrorAlert, setShowErrorAlert] = useState(false);
    function getName(id) {
        if (id) {
            const user = users.find(user => user.id === id)
            return id ? user?.firstName || '' : ''
        }
    }
    const initFilters = () => {
        setFilters({
            global: { value: null, matchMode: FilterMatchMode.CONTAINS },
            'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
            category: { value: null, matchMode: FilterMatchMode.IN },
            type: { value: null, matchMode: FilterMatchMode.IN },
            severity: { value: null, matchMode: FilterMatchMode.IN },
            dueDate: { value: null, matchMode: FilterMatchMode.IN },
            color: { value: null, matchMode: FilterMatchMode.IN },
            'locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
            created: { value: null, matchMode: FilterMatchMode.CUSTOM },
            status: { value: null, matchMode: FilterMatchMode.IN },
            'submitted.firstName': { value: null, matchMode: FilterMatchMode.IN },
            'reporter.firstName':{ value: null, matchMode: FilterMatchMode.IN },
            actionOwnerId: { value: null, matchMode: FilterMatchMode.IN },
            'obsSourceName': { value: null, matchMode: FilterMatchMode.IN }
        })
        setGlobalFilterValue('');
    }

    const onDateSearch = () => {
        if (!startDate && !endDate) return; // No date filter if both are null

        const start = startDate ? moment(startDate).startOf('day') : null;
        const end = endDate ? moment(endDate).endOf('day') : null;

        const searchData = data.filter(item => {
            const itemDate = moment(item.created, ['DD-MM-YYYY HH:mm', 'Do MMM YYYY', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

            if (start && end) {
                return itemDate.isBetween(start, end, null, '[]');
            } else if (start) {
                return itemDate.isSameOrAfter(start);
            } else if (end) {
                return itemDate.isSameOrBefore(end);
            } else {
                return true;
            }
        });

        setFilterData(searchData);
    };
    const isBetweenDateRange = (dateString, date1, date2) => {
        // Parse the date strings using Moment.js
        const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

        // Check if the parsed date is between date1 and date2
        return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
    }

    const getObservationData = async () => {

        const obs = obsdata.map(item => {
            return { name: item.locationFour?.name || '-', value: item.locationFour?.name || '' }
        })
        setProject(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
        const user = obsdata.map(item => {
            return { name: item.reporter?.firstName || '-', value: item.reporter?.firstName || '' }
        })
        setUser(user.filter((ele, ind) => ind === user.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs1 = obsdata.map(item => {
            return { name: getName(item.actionOwnerId), value: item.actionOwnerId || '-' }
        })
        setAssignee(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))


    }

    const [showReportModal, setShowReportModal] = useState(false);
    const [reportData, setReportData] = useState(null);

    const viewObservationReport = async (id) => {

        // const params = {
        //     "include": [{ "relation": "actions" }, { "relation": "workActivityDepartment" }, { "relation": "ghsOne" }, { "relation": "ghsTwo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }]

        // };
        // const response = await API.get(`${OBSERVATION_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

        // if (response.status === 200) {


            setReportData(id)
            setShowReportModal(true)
        // }

    }
    const validateFields = () => {
        const newErrors = {};
        if (!formData.whatDidYouObserve.trim()) newErrors.whatDidYouObserve = "This field is required.";
        if (!formData.whereDidYouObserve.trim()) newErrors.whereDidYouObserve = "This field is required.";
        if (!formData.whatCouldHaveGoneWrong.trim())
            newErrors.whatCouldHaveGoneWrong = "This field is required.";
        if (!formData.preventiveAction.trim()) newErrors.preventiveAction = "This field is required.";
        if (!formData.adminId) newErrors.adminId = "Admin ID is required.";
        if (!formData.locationThreeId) newErrors.locationThreeId = "Location Three ID is required.";
        if (!formData.locationFourId) newErrors.locationFourId = "Location Four ID is required.";
        return newErrors;
    };
    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        {
            icon: 'visibility',
            tooltip: 'View Report',
            onClick: (event, rowData) => {
                // Do save operation

                viewObservationReport(rowData.id)
            }
        }
    ]
    const viewBodyTemplate = (row) => {
        return (
            <div className="table-action d-flex ">
                <i className="mdi mdi-eye" onClick={() => viewObservationReport(row.id)}></i>

            </div>
        )
    }
    const localization = {
        header: {
            actions: 'View'
        }
    };
    // useEffect(()=>{
    //   const filteredData = data.filter(item => {
    //     return (
    //       (locationOneId === '' || item.locationOneId === locationOneId) &&
    //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
    //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
    //       (locationFourId === '' || item.locationFourId === locationFourId)
    //     );
    //   });

    //   setFilterData(filteredData);
    //   setTotalOtherObservation(filterData.length)

    // },[locationOneId,locationTwoId,locationThreeId,locationFourId])


    // const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
    //   const filteredData = data.filter(item => {
    //     return (
    //       (locationOneId === '' || item.locationOneId === locationOneId) &&
    //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
    //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
    //       (locationFourId === '' || item.locationFourId === locationFourId)
    //     );
    //   });

    //   setFilterData(filteredData);
    // };
    useEffect(() => {
        let filteredData = data; // Assuming response.data is your fetched data

        if (showOverdue) {
            const currentDate = moment();
            filteredData = filteredData.filter(item => {
                return moment(item.dueDate, 'DD-MM-YYYY').isBefore(currentDate);
            });
        }

        setFilterData(filteredData);
    }, [showOverdue]);
    const exportCSV = () => {
        dt.current.exportCSV();
    };
    const renderHeader = () => {
        // const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end align-items-center'>
                {/* <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setShowModal(true) }}> Record your Good Catch</button> */}
                {/* <div className="">
          <span className='me-3'>Date Filter :</span>
          <Calendar className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="dd/mm/yy" showIcon />
          <Calendar className="w-full me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="dd/mm/yy" showIcon />

          <Button className='me-3' rounded text raised severity="success" aria-label="Search" label='Apply' onClick={() => onDateSearch()} />
          <Button rounded text raised severity="danger" aria-label="Cancel" label='Clear All Filter' onClick={() => { setFilterData(data); initFilters(); setStartDate(null); setEndDate(null) }} />

        </div> */}
                {/* <h5 className='m-0'> A listing of all observations reported on the platform for the selected location(s) and time frame.</h5> */}
                {/* <span className="p-input-icon-left">
          <i className="fa fa-search" />
          <InputText type="search" value={globalFilterValue} onChange={(e) => onGlobalFilterChange(e)} />
        </span>
        <Button type="button" icon="pi pi-file-excel" severity="success" rounded onClick={exportCSV} data-pr-tooltip="XLS" /> */}
            </div>
        );
    };

    const header = renderHeader();
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
        setGlobalFilterValue(value)
    };
    const maskIdBodyTemplate = (row) => {

        return (
            <div className='maskid' onClick={() => viewObservationReport(row)}>
                {row.maskId}
            </div>
        );

    }
    const dateBodyTemplate = (row) => {

        return (<>{moment(row.created).format('DD-MM-YYYY')}</>)

    }

    const immediateBodyTemplate = (row) => {

        return (<>{row.immediateActionDate && moment(row.immediateActionDate).format('DD-MM-YYYY')}</>)

    }
    const statusBodyTemplate = (rowData) => {
        return <Badge pill bg={getSeverity(rowData.color)} >{rowData.color}</Badge>
    };

    const categoryFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={category} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const typeFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={[{ name: 'Unsafe Act', value: 'Unsafe Act' }, { name: 'Unsafe Condition', value: 'Unsafe Condition' }, { name: 'Positive', value: 'Positive' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const currentStatusFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={[{ name: 'Under Review', value: 'Under Review' }, { name: 'Action Reassigned', value: 'Action Reassigned' }, { name: 'Action Verified - Closed', value: 'Action Verified - Closed' }, { name: 'Reported & Closed', value: 'Reported & Closed' }, { name: 'Reported & Rectified on Spot', value: 'Reported & Rectified on Spot' }, { name: 'Actions Assigned', value: 'Actions Assigned' }, { name: 'Archived without actions', value: 'Archived without actions' }, { name: 'Actions Taken - Pending Verification', value: 'Actions Taken - Pending Verification' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const sourceFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={source} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };

    const dueDateTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.immediateActionDate

                    ? moment(option.immediateActionDate
                        , ["DD-MM-YYYY", "DD/MM/YYYY"]).format('DD-MM-YYYY')
                    : '-'}</span>
            </div>
        );
    };

    const statusFilterTemplate = (options) => {
        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={statuses} itemTemplate={statusItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        )
        // <Dropdown value={options.value} options={statuses} onChange={(e) => options.filterCallback(e.value, options.index)} itemTemplate={statusItemTemplate} placeholder="Select One" className="p-column-filter" showClear />;
    };
    const statusItemTemplate = (option) => {
        return <Badge pill bg={getSeverity(option.value)}>{option.name}</Badge>
    };
    const statuses = [{ name: 'Overdue', value: 'Overdue' }, { name: 'Upcoming', value: 'Upcoming' }, { name: 'Due Soon', value: 'Due Soon' }, { name: 'None', value: 'None' }];
    const getSeverity = (status) => {
        switch (status) {
            case 'Overdue':
                return 'danger';

            case 'Upcoming':
                return 'info';

            case 'Due Soon':
                return 'warning';


            case 'None':
                return null;
        }
    };
    const reportFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={user} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const assigneeFilterTemplate = (options) => {
        return <>
            <MultiSelect value={options.value} options={assignee} itemTemplate={assigneeItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
        </>
    }

    const assigneeItemTemplate = (option) => {
        return getName(option.value)
    }
    const severityFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={[{ name: 'Medium', value: 'Medium' }, { name: 'Low', value: 'Low' }, { name: 'High', value: 'High' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const assigneeTemplate = (row) => {
        return getName(row.actionOwnerId)
    }

    const sourceBodyTemplate = (row) => {
        // Parse the 'remarks' JSON string
        const remarksData = JSON.parse(row.remarks);

        // Return the obsSourceName
        return remarksData.obsSourceName || '';
    };

    const handleClose = () => {
        setShowModal(false)
    }

    const handleChange = (e) => {
        const { name, value, files } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: files ? files[0] : value,
        }));
    };

    const handleSubmit = () => {
        const validationErrors = validateFields();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            setShowErrorAlert(true);
        } else {
            setErrors({});
            setShowErrorAlert(false);
            console.log("Form Submitted Successfully:", formData);
            handleClose();
        }
    };

    const handleFileChange = (files) => {
        setFormData((prev) => ({ ...prev, uploads: files }));
    };

    const handleSelectChange = async(name, selectedOption) => {
        setFormData((prev) => ({ ...prev, [name]: selectedOption.value })); // Pass only value

        if(name ==='locationThreeId'){
            const response = await API.get(TIER2_TIER3_URL(selectedOption.value));
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {

                // if (item.id !== user.id) {
                data.push({ label: item.name, value: item.id });
                // }
            });

            setLocationFour(data)

        }

        }
      };
    return (
        <>

            <DataTable ref={dt} value={filterData} paginator rows={10} header={header} filters={filters} globalFilterFields={["maskId"]} onFilter={(e) => { console.log(e); setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found." >

                {/* <Column body={viewBodyTemplate} header="Action" ></Column> */}
                {/* <Column field='color' body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column> */}

                <Column field='maskId' body={maskIdBodyTemplate} header=" ID" ></Column>
                <Column field="status" header="Current Status" filter filterElement={currentStatusFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field='created' body={dateBodyTemplate} header="Date / Time of Report" sortable headerStyle={{ width: '16%' }}></Column>

                {/* <Column field="submitted.firstName" header="Reported By" sortable  ></Column> */}

                <Column field="reporter.firstName" header="Reporter" sortable filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field="type" header="Department" sortable filter filterElement={typeFilterTemplate} showFilterMatchModes={false}></Column>

                {/* <Column field="description" header="Brief Description" sortable  ></Column> */}


                <Column field='immediateActionDate' header="Immediate Action Date/Time" body={immediateBodyTemplate} showFilterMatchModes={false}></Column>

                <Column field="submitted.firstName" header="Importance Rating" filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field="actionOwnerId" header="Action Action Owner" body={assigneeTemplate} filter filterElement={assigneeFilterTemplate} showFilterMatchModes={false}></Column>

                {/* <Column field="status" header="Status" sortable  ></Column>  */}

                {/* <Column field="locationFour.name" header="Project/DC name" filter filterElement={projectFilterTemplate} showFilterMatchModes={false}></Column> */}

                {/* <Column field="severity" header="Severity" filter filterElement={severityFilterTemplate} showFilterMatchModes={false}></Column> */}


                <Column field="immediateActionDate" body={dueDateTemplate} header="Closure Actions Date/Time " sortable></Column>



            </DataTable>

            <GoodCatchModal reportData1={reportData} showReportModal={showReportModal} setShowReportModal={(status) => setShowReportModal(status)} />

            <Modal show={showModal} onHide={handleClose}>
                <Modal.Header closeButton>
                    <Modal.Title>Good Catch Form</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {showErrorAlert && (
                        <Alert variant="danger">Please fill all the required fields!</Alert>
                    )}
                    <Form>

                        <Form.Group>
                            <Form.Label>Select Location Three</Form.Label>
                            <Select
                                options={locationThree}
                                placeholder="Choose Location Three"
                                onChange={(selected) => handleSelectChange("locationThreeId", selected)}
                            />
                            {errors.locationThreeId && (
                                <div className="text-danger mt-1">{errors.locationThreeId}</div>
                            )}
                        </Form.Group>

                        <Form.Group>
                            <Form.Label>Select Location Four</Form.Label>
                            <Select
                                options={locationFour}
                                placeholder="Choose Location Four"
                                onChange={(selected) => handleSelectChange("locationFourId", selected)}
                            />
                            {errors.locationFourId && (
                                <div className="text-danger mt-1">{errors.locationFourId}</div>
                            )}
                        </Form.Group>
                        <Form.Group>
                            <Form.Label>What did you observe?</Form.Label>
                            <Form.Control
                                as="textarea"
                                name="whatDidYouObserve"
                                isInvalid={!!errors.whatDidYouObserve}
                                onChange={handleChange}
                            />
                            <Form.Control.Feedback type="invalid">
                                {errors.whatDidYouObserve}
                            </Form.Control.Feedback>
                        </Form.Group>

                        <Form.Group>
                            <Form.Label>Where did you observe?</Form.Label>
                            <Form.Control
                                as="textarea"
                                name="whereDidYouObserve"
                                isInvalid={!!errors.whereDidYouObserve}
                                onChange={handleChange}
                            />
                            <Form.Control.Feedback type="invalid">
                                {errors.whereDidYouObserve}
                            </Form.Control.Feedback>
                        </Form.Group>

                        <Form.Group>
                            <Form.Label>What could have gone wrong?</Form.Label>
                            <Form.Control
                                as="textarea"
                                name="whatCouldHaveGoneWrong"
                                isInvalid={!!errors.whatCouldHaveGoneWrong}
                                onChange={handleChange}
                            />
                            <Form.Control.Feedback type="invalid">
                                {errors.whatCouldHaveGoneWrong}
                            </Form.Control.Feedback>
                        </Form.Group>

                        <Form.Group>
                            <Form.Label>What action did you take to prevent it from going wrong?</Form.Label>
                            <Form.Control
                                as="textarea"
                                name="preventiveAction"
                                isInvalid={!!errors.preventiveAction}
                                onChange={handleChange}
                            />
                            <Form.Control.Feedback type="invalid">
                                {errors.preventiveAction}
                            </Form.Control.Feedback>
                        </Form.Group>

                        <Form.Group>
                            <Form.Label>What was the work activity happening? (Optional)</Form.Label>
                            <Form.Control
                                as="textarea"
                                name="workActivity"
                                onChange={handleChange}
                            />
                        </Form.Group>

                        <Form.Group>
                            <Form.Label>Upload photos to elaborate (Optional)</Form.Label>
                            <DropzoneArea
                                acceptedFiles={[]}
                                dropzoneText={"Drag and Drop Evidence Images"}
                                filesLimit={10}
                                maxFileSize={104857600}
                                onChange={handleFileChange}
                            />
                        </Form.Group>

                        <Form.Group>
                            <Form.Label>Select Admin</Form.Label>
                            <Select
                                options={admin}
                                placeholder="Choose an Admin"
                                onChange={(selected) => handleSelectChange("adminId", selected)}
                            />
                            {errors.adminId && <div className="text-danger mt-1">{errors.adminId}</div>}
                        </Form.Group>


                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>
                    <Button variant="primary" onClick={handleSubmit}>
                        Submit
                    </Button>
                </Modal.Footer>
            </Modal>

        </>
    )
}

export default Catches;
