import React, { Component } from "react";
import * as _ from "lodash";

class TextInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      
      toolType: "TEXT_INPUT",
      title: "",
      isEmpty:false,
      validation: {
        isRequired: false,
      },
    
    };
    this.removeOption = this.removeOption.bind(this);
  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue(stateFor, value) {
    switch (stateFor) {
      case "TITLE":
        this.setState({ title: value });
        break;

    
      case "IS_REQUIRED":
        this.setState({
          validation: { ...this.state.validation, isRequired: value },
        });
        break;

   
      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  removeOption(index) {
    let radios = this.state.radios;
    radios.splice(index, 1);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  render() {
    return (
      <div className="card mb-3" style={this.state.title ==='' && this.props.field.isEmpty ?{boxShadow: '0px 0px 12px 3px #dfdfdf',border:'1px solid red',borderRadius:0}:{boxShadow: '0px 0px 12px 3px #dfdfdf',borderRadius:0}}>
        <div className="card-header d-flex justify-content-between">
          <div> <i className="mdi mdi-equal-box  mr-1"></i> Text Input</div>
         
          <div className="">
          {this.props.index !== 0 ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveUp(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-up"></i>
                  </span>
                  : ""}
                {this.props.index !== this.props.length ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveDown(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-down"></i>
                  </span>
                  : ''}
                <span
                  className=""
                  onClick={() => this.props.removeField(this.props.index)}
                >
                  <i className="mdi mdi-close"></i>
                </span>
              </div>
        </div>
        <div className="card-body">
       
         
            <div className="card-body">
              <div className="row">
                <div className="col-12">
                  <div className="form-group">
                    <label htmlFor="title">Text Title</label>
                    <textarea
                      value={this.state.title}
                      onChange={(e) =>
                        this.changeValue("TITLE", e.target.value)
                      }
                      className="form-control"
                    ></textarea>
                  </div>
                </div>
              </div>

              
             
              <div className="form-check">
                <input
                  defaultChecked={this.state.validation.isRequired}
                  onChange={(e) =>
                    this.changeValue("IS_REQUIRED", e.target.checked)
                  }
                  className="form-check-input"
                  type="checkbox"
                  id="isRequired"
                />
                <label className="form-check-label" htmlFor="isRequired">
                  Required
                </label>
              </div>

              
            </div>
          

          
        </div>
      
      </div>
    );
  }

  changeOptionValue(index, value, state) {
    let radios = this.state.radios;
    let radio = {};
    if (state === "DEFAULT_VALUE") {
      this.setState({
        defaultValue: index,
      });
    }
    if (state === "TITLE") {
      radio = {
        ...radios[index],
        title: value,
      };
    } else if (state === "SELECTED") {
      radio = {
        ...radios[index],
        selected: !radios[index].selected,
      };
    } else if (state === "VALUE") {
      radio = {
        ...radios[index],
        value: value,
      };
    } else {
      radio = {
        ...radios[index],
      };
    }

    radios[index] = radio;
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  duplicates() {
    let radios = this.state.radios;
    let u = _.uniqBy(radios, "value");
    if (!_.isEqual(radios, u)) {
      this.setState({
        duplicate: true,
      });
    } else {
      this.setState({
        duplicate: false,
      });
    }
  }

  addOption() {
    let radio = {
      title: "",
      value: "",
      selected: false,
    };
    let radios = this.state.radios;
    radios.push(radio);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }
}

export default TextInput;
