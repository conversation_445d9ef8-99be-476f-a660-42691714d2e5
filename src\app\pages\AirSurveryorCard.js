import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";

import Select from "react-select";
import { DropzoneArea } from 'material-ui-dropzone';

import { AIR_COST_REVIEWER_URL, AIR_COST_REVIEWER_WITH_ID_URL, AIR_DUTY_MANAGER_ESTIMATION_WITH_ID_URL, AIR_FINANCE_URL, AIR_SURVEYOR_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';

import TagInput from "./TagInput";

const AirSurveryorCard = ({ showModal, setShowModal, data, setData }) => {


    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }



    const [surveyStatus, setSurveyStatus] = useState('');
    const [isClicked, setIsClicked] = useState(false);

    const handleSubmit = async () => {
        setIsClicked(true);
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_SURVEYOR_WITH_ID_URL(data.id, data.actionId), {


                surveyStatus: surveyStatus
            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                setIsClicked(false);
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            setIsClicked(false);
            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };

    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0">IR Information</h2>

                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">


                        <div className={`col-md-6 `}>
                            <IncidentStory data={data} />
                        </div>
                        <div className={`col-md-6`}>
                            <Box>


                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label>Update Survey Status</label>
                                            <textarea className="form-control" value={surveyStatus} onChange={(e) => setSurveyStatus(e.target.value)}>

                                            </textarea>
                                        </div>
                                    </div>
                                </div>
                          
                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <DropzoneArea
                                                acceptedFiles={[
                                                    'application/pdf',
                                                    'image/jpeg',
                                                    'image/png'

                                                ]}
                                                dropzoneText={"Drag and drop files / documents / pictures"}
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                onChange={handleFileChange}
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="row">
                                    <div className="col-3">
                                        <Button variant="primary" onClick={handleSubmit} disabled={isClicked}>
                                            Submit
                                        </Button>
                                    </div>

                                </div>
                            </Box>
                        </div>


                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirSurveryorCard;