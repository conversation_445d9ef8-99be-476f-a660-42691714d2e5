import React, { useEffect, useRef } from 'react';
import { createPopper } from '@popperjs/core';

const ImageTooltip = ({ imageUrl, children }) => {
  const tooltipRef = useRef();
  const childRef = useRef();
  
  useEffect(() => {
    const popper = createPopper(childRef.current, tooltipRef.current, {
      placement: 'top',
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 10],
          },
        },
      ],
    });
    
    return () => {
      popper.destroy();
    };
  }, []);
  
  return (
    <div style={{ position: 'relative', display: 'inline-block' }} onMouseEnter={() => {
        tooltipRef.current.style.visibility = 'visible';
      }} onMouseLeave={() => {
        tooltipRef.current.style.visibility = 'hidden';
      }}>
      <div ref={childRef}>
        {children}
      </div>
      <div ref={tooltipRef} className="bg-dark text-white p-2 rounded" style={{ position: 'absolute', visibility: 'hidden', zIndex: 10 }}>
        <img src={imageUrl} alt="Tooltip" style={{ maxWidth: '500px' }} />
      </div>
    </div>
  );
};

export default ImageTooltip;
