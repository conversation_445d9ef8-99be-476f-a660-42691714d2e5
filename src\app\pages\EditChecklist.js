import React, { useState, useEffect } from "react";
import Form<PERSON>uilder from "app/editors/FormBuilder";
import { CHECKLIST_WITH_ID_URL } from '../../constants';
import { singlePopup } from './../notifications/Swal';

const EditChecklist = (props) => {
    const [isLoading, setIsLoading] = useState(true);
    const [checklistData, setChecklistData] = useState({ name: '', value: {} });

    useEffect(() => {
        getChecklistData(props.id);
    }, [props.id]);

    const getChecklistData = async (id) => {

        const response = await fetch(CHECKLIST_WITH_ID_URL(id));
        if (response.ok) {
            setChecklistData(await response.json())
            setIsLoading(false)
        }

    }

    const handleChecklistSave = async (data) => {
        const response = await fetch(CHECKLIST_WITH_ID_URL(props.id), {
            method: 'PATCH',
            body: JSON.stringify({
                value: data
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }
        })
        if (response.ok) {
            singlePopup.fire(
                'Checklist Saved!',
                '',
                'success'
            );
        } else {
            //show error
            singlePopup.fire(
                'Please Try Again',
                '',
                'error'
            )
        }
    }


    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">

                                <h4 className="card-title">{checklistData.name}</h4>
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            {isLoading && <h4>Please Wait! Loading...</h4>}
                                            {!isLoading && <FormBuilder onSubmit={handleChecklistSave} values={checklistData.value} id={props.id} />}

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

export default EditChecklist;