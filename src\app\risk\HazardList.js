import React, { useEffect, useRef, useState } from 'react'
// import SagoTable from '../tables/SagoTable'
import API from '../services/API';
// import { FILE_URL, HAZARDS_CATEGOTY, HAZARD_WITH_ID_URL, HAZARDS, DOWNLOAD_DOCS_URL, HAZARDS_WITH_ID_URL, HAZARD_CATEGOTY_WITH_ID_URL } from '../../constants'
import Select from 'react-select'
import Editable from "react-bootstrap-editable";
import { Button, Form, Modal } from 'react-bootstrap';
import Swal from 'sweetalert2';
import MaterialTable from "material-table";
import { ThemeProvider, createTheme } from "@mui/material";
// import { Draggable } from 'react-drag-reorder';
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import S3 from "react-aws-s3";
import { Buffer } from "buffer";

Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
const config = {
    bucketName: "mpower-s3",
    region: "ap-southeast-1",
    accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY,
    secretAccessKey: process.env.REACT_APP_AWS_SECRET_KEY,
};
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})
const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})
const getItems = count =>
    Array.from({ length: count }, (v, k) => k).map(k => ({
        id: `item-${k}`,
        content: `item ${k}`
    }));

// a little function to help us with reordering the result
const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
};

const grid = 8;

const getItemStyle = (isDragging, draggableStyle) => ({
    // some basic styles to make the items look a bit nicer
    userSelect: "none",
    padding: grid * 2,
    margin: `0 0 ${grid}px 0`,
    boxShadow: '0px 0px 10px 3px #c3c0c0',
    // change background colour if dragging
    background: isDragging ? "lightgreen" : "white",

    // styles we need to apply on draggables
    ...draggableStyle
});

const getListStyle = isDraggingOver => ({
    background: isDraggingOver ? "lightblue" : "white",
    padding: grid,

});
const defaultMaterialTheme = createTheme();
const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
};
const API_URL = 'https://risk-api.acuizen.com';
const HAZARDS_WITH_ID_URL = (id) => {
    return API_URL + '/hazards/' + id;
}
const HAZARDS_CATEGOTY = API_URL + '/hazards-categories'
const HAZARDS = API_URL + '/hazards'
const HAZARD_WITH_ID_URL = (id) => {
    return API_URL + '/hazards-categories/' + id + '/hazards';
}
const HAZARD_CATEGOTY_WITH_ID_URL = (id) => {
    return API_URL + '/hazards-categories/' + id;
}
const HazardList = () => {
    const name = useRef()
    const file = useRef()
    const nameE = useRef()
    const [category, setCategory] = useState([])
    const [hazards, setHazards] = useState([])
    const [hazardCategoty, setHazardCategoty] = useState([])
    const [modal, setModal] = useState(false)
    const [User, setUser] = useState({})
    const [smModal, setSmModal] = useState(false)
    const [cateModal, setCateModal] = useState(false)
    const [id, setID] = useState('')
    const [fileN, setFile] = useState('')

    useEffect(() => {
        // getHazardsCategory()
        handleSelectChange()
    }, [])

    const getHazardsCategory = async () => {
        const response = await API.get(HAZARDS_CATEGOTY);
        if (response.status === 200) {
            let data = []
            response.data.sort((a, b) => (a.order > b.order) ? 1 : -1)
            response.data.map((item) => {

                data.push({ 'label': item.name, 'value': item.id })

            })
            setHazardCategoty(response.data)
            setCategory(data)

        }
    }
    const handleSelectChange = async () => {
        setID('65001d7e2387846ef23e07bb')

        const response = await API.get(HAZARD_WITH_ID_URL('65001d7e2387846ef23e07bb'));
        if (response.status === 200) {

            setHazards(response.data)

        }

    }
    const updatefileUpdate = async (e) => {
        const ReactS3Client = new S3(config);
        const filename = new Date().getTime() + e.target.files[0].name;

        await ReactS3Client.uploadFile(
            e.target.files[0],
            "uploads/hazards/" + filename
        )
            .then((data) => console.log(data))
            .catch((err) => console.error(err));

       
        User.image =filename
        setFile(filename)
    }
    const updateHandler = async () => {
        const ReactS3Client = new S3(config);
        const filename = new Date().getTime() + file.current.files[0].name;

        await ReactS3Client.uploadFile(
            file.current.files[0],
            "uploads/hazards/" + filename
        )
            .then((data) => console.log(data))
            .catch((err) => console.error(err));



        const response = await fetch(
            HAZARDS,
            {
                method: 'POST',
                body: JSON.stringify({

                    name: name.current.value,
                    image: filename,
                    hazardsCategoryId: '65001d7e2387846ef23e07bb'


                }),
                headers: { "Content-type": "application/json; charset=UTF-8" }
            })

        if (response.ok) {
            var e = { value: id }
            handleSelectChange(e);
            setModal(false)
        } else {
            //show error
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )

        }

    }
    const updateHazard = async () => {
       
        const response = await fetch(
            HAZARDS_WITH_ID_URL(User.id),
            {
              method: 'PATCH',
              body: JSON.stringify({
      
                name:nameE.current.value,
                image:User.image
      
      
              }),
              headers: { "Content-type": "application/json; charset=UTF-8" }
            })
      
          if (response.ok) {
      
      
      
            customSwal2.fire(
              'Hazard Updated!',
              '',
              'success'
            )
            
      
          } else {
            //show error
            customSwal2.fire(
              'Please Try Again!',
              '',
              'error'
            )
           
          }

       setSmModal(false)
       handleSelectChange()

    }
    const tableActions = [

        // {
        //   icon: 'visibility',
        //   tooltip: 'View Risk',
        //   onClick: (event, rowData) => {

        //     setUserShow(true);
        //     setUser(rowData);
        //     getOwnerList();
        //     getGroupUsers(rowData)
        //     // Do save operation
        //     // console.log(rowData)
        //     // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
        //   }
        // },
        {
            icon: "modeEdit",
            tooltip: "Edit Risk",
            onClick: (event, rowData) => {
                setSmModal(true);
                setUser(rowData);

            },
        },
        {
            icon: "delete",
            tooltip: "Delete Risk",
            onClick: (event, rowData) => {
                onDelete(rowData.id)

                // Do save operation
                // console.log(rowData)
                // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
            },
        },
    ]
    const columns = [

        {
            field: 'name',
            title: 'Hazard Name',
            sort: true,

        },
        {
            field: 'image',
            title: 'Image',
            sort: true,
            render: (row) => {
                console.log(row)

                var item = row
                return (
                    <div>
                        <div className=" option-btn">
                            <img src={"https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" + item.image} alt='test' style={{ width: 60 }} />
                        </div>
                    </div>
                );
            }

        },



    ]
    const onDelete = async (id1) => {

        customSwal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                const response = await API.delete(HAZARDS_WITH_ID_URL(id1));
                if (response.status === 204) {

                    customSwal2.fire(
                        'Deleted!',
                        '',
                        'success'
                    )

                    var e = { value: id }
                    handleSelectChange(e);
                }

            }
        })

    }

    const onEditTitle = async (e) => {
        const response = await API.post(HAZARDS_CATEGOTY,
            {
                name: e
            });
        if (response.status === 200) {
            getHazardsCategory();
        }

    }
    const onDeleteCategory = (id2) => {
        customSwal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                const response = await API.delete(HAZARD_CATEGOTY_WITH_ID_URL(id2));
                if (response.status === 204) {

                    customSwal2.fire(
                        'Deleted!',
                        '',
                        'success'
                    )

                    getHazardsCategory();
                }

            }
        })
    }
    const rearrangeHandler = () => {
        hazardCategoty.map(async (item, i) => {
            const response = await API.patch(HAZARD_CATEGOTY_WITH_ID_URL(item.id),
                {
                    order: i
                });
            if (response.status === 204) {


            }
        })
        setCateModal(false)
        getHazardsCategory();

    }
    const onDragEnd = (result) => {
        // dropped outside the list
        if (!result.destination) {
            return;
        }

        const items = reorder(
            hazardCategoty,
            result.source.index,
            result.destination.index
        );
        console.log(items)

        setHazardCategoty(items)
    }
    return (<>
        <div className='row'>
            <div className='col-12'>
                <div className='card'>
                    <div className="card-body">
                        {/* <div className="row align-items-center justify-content-center">

                            {console.log(hazardCategoty)}
                            <div className='col-3'>
                                <label>Choose Category</label>
                                <Select labelKey="label"
                                    id="user_description"
                                    onChange={(e) => handleSelectChange(e)}
                                    options={category}
                                    placeholder="Choose ..."
                                />
                            </div>
                            {id === '' ? '' :
                                <div className='col-1'>
                                  
                                    <i style={{ fontSize: 22 }} className="mdi mdi-delete text-warning " onClick={() => onDeleteCategory(id)}></i>

                                </div>
                            }
                            <div className='col-3'>
                                <label>Create Category</label>
                                <Editable alwaysEditing onSubmit={(e) => onEditTitle(e)} className="d-flex" mode="inline" />
                            </div>
                            <div className='col-5'>
                                <i style={{ fontSize: 22 }} className='mdi mdi-arrange-send-backward text-primary' onClick={() => setCateModal(true)}></i>

                            </div>
                        </div> */}
                        <div className='col-12'>
                            <h4 className="card-title" style={{ textAlign: 'center', fontSize: 20 }}>Hazards </h4>
                            <Button className='btn btn-primary' onClick={() => setModal(true)}>Add Hazard</Button>
                            <ThemeProvider theme={defaultMaterialTheme}>
                                <MaterialTable
                                    columns={columns}
                                    data={hazards}
                                    title=""
                                    style={tableStyle}
                                    actions={tableActions}
                                    options={{
                                        actionsColumnIndex: -1,
                                    }}
                                />
                            </ThemeProvider>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <Modal
            show={modal}

            onHide={() => setModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
        >


            <Modal.Body>
                <form className="forms row" >
                    <div className="form-group ">
                        <label htmlFor="user_name" >Name</label>
                        <Form.Control type="text" ref={name} id="user_name" placeholder="Enter User Name" />
                    </div>
                    <div className="form-group ">
                        <label htmlFor="user_name" >Image</label>
                        <Form.Control type="file" ref={file} id="user_name" />
                    </div>





                </form>
            </Modal.Body>

            <Modal.Footer className="flex-wrap">

                <Button variant="light" onClick={() => setModal(false)}>Cancel</Button>
                <Button variant="primary" onClick={updateHandler}>Add</Button>


            </Modal.Footer>
        </Modal>
        <Modal
            show={smModal}

            onHide={() => setSmModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
        >


            <Modal.Body>
                <form className="forms row" >
                    <div className="form-group ">
                        <label htmlFor="user_name" >Name</label>
                        <Form.Control type="text" ref={nameE} id="user_name" placeholder="Enter User Name" defaultValue={User.name} />
                    </div>
                    <div className="form-group ">
                        <label htmlFor="user_name" >Image</label>
                        <Form.Control type="file" ref={file} id="user_name" onChange={(e) => updatefileUpdate(e)} />
                    </div>
                    <div className="form-group ">
                        {fileN === '' ?
                            <img src={"https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" + User.image} alt='test' style={{ width: 60 }} /> :
                            <img src={"https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" + fileN} alt='test' style={{ width: 60 }} />}
                    </div>






                </form>
            </Modal.Body>

            <Modal.Footer className="flex-wrap">

                <Button variant="light" onClick={() => setSmModal(false)}>Cancel</Button>
                <Button variant="primary" onClick={updateHazard}>Update</Button>


            </Modal.Footer>
        </Modal>
        <Modal
            show={cateModal}

            onHide={() => setCateModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
        >
            <Modal.Header>
                <Modal.Title>Rearrange</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <DragDropContext onDragEnd={onDragEnd}>
                    <Droppable droppableId="droppable">
                        {(provided, snapshot) => (
                            <div
                                {...provided.droppableProps}
                                ref={provided.innerRef}
                                style={getListStyle(snapshot.isDraggingOver)}
                            >
                                {hazardCategoty.map((item, index) => (
                                    <Draggable key={item.id} draggableId={item.id} index={index}>
                                        {(provided, snapshot) => (
                                            <div
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                style={getItemStyle(
                                                    snapshot.isDragging,
                                                    provided.draggableProps.style
                                                )}
                                            >
                                                {item.name}
                                            </div>
                                        )}
                                    </Draggable>
                                ))}
                                {provided.placeholder}
                            </div>
                        )}
                    </Droppable>
                </DragDropContext>
            </Modal.Body>

            <Modal.Footer className="flex-wrap">

                <Button variant="light" onClick={() => setCateModal(false)}>Cancel</Button>
                <Button variant="primary" onClick={rearrangeHandler}>Rearrange</Button>


            </Modal.Footer>
        </Modal>
    </>
    )
}

export default HazardList