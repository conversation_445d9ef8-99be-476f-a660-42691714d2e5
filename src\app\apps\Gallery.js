import React, { useState } from 'react';
import Gallery from "react-photo-gallery";
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css'; // This only needs to be imported once in your app

const GalleryPage = (props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);

  const openLightbox = (event, { photo, index }) => {
    setPhotoIndex(index);
    setIsOpen(true);
  };

  const nextImage = () => {
    setPhotoIndex((photoIndex + 1) % props.photos.length);
  };

  const prevImage = () => {
    setPhotoIndex((photoIndex + props.photos.length - 1) % props.photos.length);
  };

  return (
    <div>
      <div>
        <div>
          <div className="card">
            <div className="p-2 body-blue">
              <Gallery photos={props.photos} onClick={openLightbox} />
              {isOpen && (
                <Lightbox
                  mainSrc={props.photos[photoIndex].src}
                  nextSrc={null}  // Disable the next arrow
                  prevSrc={null}  // Disable the previous arrow
                  onCloseRequest={() => setIsOpen(false)}
                  // onMovePrevRequest={prevImage}
                  // onMoveNextRequest={nextImage}
                  reactModalStyle={{ overlay: { zIndex: 9999 } }}
                  enableZoom={true}
                  wrapperClassName="lightbox-wrapper"
                  className={'zoomINzoomOut'}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GalleryPage;
