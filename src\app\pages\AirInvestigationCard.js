import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";

import Select from "react-select";

import { AIR_DRIVERS_WITH_ID_URL, AIR_SURVEYORS_URL, AIR_WITH_ID_URL, AIR_REVIEW_WITH_ID_URL, AIR_COST_ESTIMATOR_URL, USERS_URL, AIR_GM_OPS_URL, AIR_INVESTIGATE_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import { DropzoneArea } from 'material-ui-dropzone';
import ReactTags from "react-tag-autocomplete";
import TagInput from "./TagInput";

const AirInvestigationCard = ({ showModal, setShowModal, data, setData }) => {

    const [showNext, setShowNext] = useState(false);
    const handleReject = () => {

    }

    const handleContinue = () => {
        setShowNext(true)
    }

    const [users, setUsers] = useState([])
    useEffect(() => {
        getUsers()
    }, [])

    const getUsers = async () => {
        const response = await API.get(USERS_URL);
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    const [gmOps, setGmOps] = useState([])
    useEffect(() => {
        getGmOps()
    }, [])

    const getGmOps = async () => {
        const response = await API.post(AIR_GM_OPS_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setGmOps(response.data)
        }
    }



    const handleWitnessSelectChange = (selectedOptions) => {
        setSelectedWitness(selectedOptions);
    };

    const [selectedWitness, setSelectedWitness] = useState([]);
    const [selectedGmOps, setSelectedGmOps] = useState('');

    const [immediateAction, setImmediateAction] = useState(false);
    const [majorAccident, setMajorAccident] = useState(false);
    const [inspectionRequired, setInspectionRequired] = useState(false);
    const [cll, setCll] = useState(false);

    const [drivers, setDrivers] = useState({
        driverDetails: [
            {
                name: "",
                vehicleNo: "",
                licenseNo: "",
                employeeId: ""
            }
        ]
    })

    const [files, setFiles] = useState([]);
    const handleFileChange = (file) => {
        setFiles(file)

    }


    const [description, setDescription] = useState('');
    const [remarks, setRemarks] = useState('');
    const [allComments, setAllComments] = useState(
        data.investigation.investigationTeam.map(person => ({ id: person.value, name: person.label, comments: '' }))
    );

    const [allLiable, setAllLiable] = useState(
        data.personInvolved.map(person => ({ designation: person.designation.name, name: person.name, comments: '' }))
    );

    const handleCommentChange = (id, value) => {
        setAllComments(prevComments => {
            return prevComments.map(comment => {
                if (comment.id === id) {
                    return { ...comment, comments: value };
                } else {
                    return comment;
                }
            });
        });
    }

    const handleLiableChange = (name, value) => {
        setAllLiable(prevComments => {
            return prevComments.map(comment => {
                if (comment.name === name) {
                    return { ...comment, comments: value };
                } else {
                    return comment;
                }
            });
        });
    }
    const steps = [
        {
            label: 'Investigation IR',
            description: (<>




                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">Brief Description</label>
                            <textarea value={description} onChange={(e) => setDescription(e.target.value)} className="form-control"> </textarea>
                        </div>
                    </div>
                </div>

                {data.investigation && data.investigation.investigationTeam.map(person => (
                    <div className='row' key={person.value}>
                        <div className='col'>
                            <div className='form-group'>
                                <label>{person.label} - Remarks</label>
                                <textarea
                                    className="form-control"
                                    value={allComments.find(comment => comment.id === person.value)?.comments || ''}
                                    onChange={(e) => handleCommentChange(person.value, e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                ))}

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">Conclusion Remarks</label>
                            <textarea value={remarks} onChange={(e) => setRemarks(e.target.value)} className="form-control"> </textarea>
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">Liablilities</label>
                            {data.personInvolved && data.personInvolved.map(person => (
                                <div className='row' key={person.name}>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label>{person.name} | {person.designation.name} - Liabilities</label>
                                            <input type="number"
                                                min={0}
                                                className="form-control w-25"
                                                value={allLiable.find(comment => comment.name === person.name)?.comments || ''}
                                                onChange={(e) => handleLiableChange(person.name, e.target.value)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>



                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <DropzoneArea
                                acceptedFiles={[
                                    'application/pdf',
                                    'image/jpeg',
                                    'image/png'

                                ]}
                                dropzoneText={"Drag and drop files / documents / pictures"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                onChange={handleFileChange}
                            />
                        </div>
                    </div>
                </div>
                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label>Submit to Punitive / Correction Action Taker</label>
                            <select onChange={(e) => setSelectedGmOps(e.target.value)} className="form-control">
                                <option value="">Choose Punitive / Correction Action Taker</option>
                                {
                                    gmOps.map(user => {
                                        return (
                                            <option value={user.id}> {user.firstName} </option>
                                        )
                                    })
                                }
                            </select>
                        </div>
                    </div>
                </div>

            </>)
        }
    ]

    const [investigation, setInvestigation] = useState(null)
    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(false);
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_INVESTIGATE_WITH_ID_URL(data.id, data.actionId), {
                investigation: {

                    description: description,
                    remarks: allComments,
                    gmOpsId: selectedGmOps,
                    conclusionRemarks: remarks,
                    liabilities: allLiable
                },
                gmOpsId: selectedGmOps
            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                setIsClicked(false);
                return;  // or handle this error appropriately
            }
            setIsClicked(false);
            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            // Proceed to the next step
            setActiveStep((prevActiveStep) => prevActiveStep + 1);

        } catch (error) {
            setIsClicked(false);
            console.error('An error occurred:', error);

        }
        setIsClicked(false);
    };



    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    IR Information
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div>
                        <div className="col-md-6">
                            <Box>
                                <Stepper activeStep={activeStep} orientation="vertical">
                                    {steps.map((step, index) => (
                                        <Step key={step.label}>
                                            <StepLabel>
                                                {step.label}
                                            </StepLabel>
                                            <StepContent>
                                                <Typography>{step.description}</Typography>
                                                <Box sx={{ mb: 2 }}>
                                                    <div>

                                                        {index === steps.length - 1 ? (
                                                            <>
                                                                <div className='form-group'>
                                                                    {/* <select onChange={(e) => setSelectedReviewer(e.target.value)} className='form-control'>
                                                                <option value={''}>Choose Incident Owner</option>
                                                                {
                                                                    incidentReviewer.map(user => {
                                                                        return (
                                                                            <option value={user.id}>{user.firstName}</option>
                                                                        )
                                                                    })
                                                                }
                                                            </select> */}
                                                                </div>
                                                                <Button
                                                                    variant="light"
                                                                    className='me-2 mt-2'
                                                                    onClick={handleSubmit}
                                                                    disabled={isClicked}
                                                                    sx={{ mt: 1, mr: 1 }}
                                                                >
                                                                    Submit
                                                                </Button>
                                                            </>

                                                        ) : (

                                                            <Button
                                                                variant="light"
                                                                className='me-2 mt-2'
                                                                onClick={handleNext}
                                                                sx={{ mt: 1, mr: 1 }}
                                                            >
                                                                Continue
                                                            </Button>
                                                        )}

                                                        <Button
                                                            disabled={index === 0}
                                                            className='mt-2'
                                                            onClick={handleBack}
                                                            sx={{ mt: 1, mr: 1 }}
                                                        >
                                                            Back
                                                        </Button>
                                                    </div>
                                                </Box>
                                            </StepContent>
                                        </Step>
                                    ))}
                                </Stepper>
                                {activeStep === steps.length && (
                                    <Paper square elevation={0} sx={{ p: 3 }}>
                                        <Typography>Submitted! Action Card will be disappeared from the list!</Typography>

                                    </Paper>
                                )}
                            </Box>
                        </div>
                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirInvestigationCard;