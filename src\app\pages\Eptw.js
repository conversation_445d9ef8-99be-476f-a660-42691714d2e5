import React, { Component, useState, useEffect } from 'react'

import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import CardOverlay from './CardOverlay';
import Permit from './Permit';
import { ACTION_URL, PERMIT_REPORTS } from '../constants';
import API from '../services/API';

// import LineChart from '../dashboard/LineChart';
// import PolarChart from '../dashboard/PolarChart';
// import SatelliteMap from './SatelliteMap';

import AllPermits from './AllPermits';
import ActionCard from './ActionCard';
import AppSwitch from './AppSwitch';
function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`eptw-tabpanel-${index}`}
      aria-labelledby={`eptw-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box >
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `eptw-tab-${index}`,
    'aria-controls': `eptw-tabpanel-${index}`,
  };
}


const Eptw = () => {
  const dates9 = [
    [new Date("2023-07-01").getTime(), 42000000],
    [new Date("2023-07-02").getTime(), 65000000],
    [new Date("2023-07-03").getTime(), 78000000],
    [new Date("2023-07-04").getTime(), 46000000],
    [new Date("2023-07-05").getTime(), 56000000],
    [new Date("2023-07-06").getTime(), 87000000],
    [new Date("2023-07-07").getTime(), 78000000],
    [new Date("2023-07-08").getTime(), 65000000],
    [new Date("2023-07-09").getTime(), 62000000],
    [new Date("2023-07-10").getTime(), 84000000],
  ];
  const [value, setValue] = useState(0);
  const [topLevelValue, setTopLevelValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleTopLevelChange = (event, newValue) => {
    setTopLevelValue(newValue);
  };


  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

  };

  const [actionData, setActionData] = useState([]);
  const [rendered, setRendered] = useState(0)
  useEffect(() => {

    getActionData();
  }, [])
  const getActionData = async () => {



    const response = await API.get(ACTION_URL);
    if (response.status === 200) {
      setActionData(response.data)

    }
  }
  useEffect(() => {

    getActionData()
  }, [
    rendered
  ])


  const getFilteredActions = (applicationType, statusList) => {
    return actionData.filter(action =>
      action.application === applicationType && statusList.includes(action.status)
    );
  }

  return (
    <>

      <CardOverlay>
        <AppSwitch value={{ label: 'ePermit to Work', value: 'eptw' }} />
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={topLevelValue} onChange={handleTopLevelChange} aria-label="observation report table">
              <Tab label="My Actions" {...a11yProps(0)} />
              {/* <Tab label="ePTW Dashboard" {...a11yProps(1)} /> */}
              <Tab label="ePTW All Permit" {...a11yProps(1)} />

            </Tabs>
          </Box>
          <CustomTabPanel value={topLevelValue} index={0}>
            <Box sx={{ width: '100%' }}>

              {/* {getFilteredActions('PermitToWork', ['open']).map(action => (
                <ActionCard action={action} applicationType="PermitToWork" setRendered={setRendered} />
              ))} */}

            </Box>
          </CustomTabPanel>
          {/* <CustomTabPanel value={topLevelValue} index={1}> */}
            {/* <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} /> */}
            {/* <SatelliteMap /> */}
            {/* <div className='row'>


              <div className='mt-5 mb-3'> */}
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              {/* </div> */}
              {/* <div className='col-md-6'>
                <div className='card shadow'>
                  <div className='card-body'>
                    <h6 className='text-center'>YTD % of Issued Permits that were Suspended</h6>
                    <h1 className='text-center my-5'>3.76</h1>
                    <div className='d-flex justify-content-between'>
                      <div>
                        <h4><i className='mdi mdi-target me-2'></i>No Target Set</h4>
                      </div>

                      <div>
                        <h4><i className='mdi mdi-calendar-month'></i><span className='text-primary'> <i className='mdi mdi-arrow-down-thin'></i> 127% </span></h4>
                        <p>YoY as of June 2023</p>
                      </div>
                    </div>

                  </div>
                </div>
              </div> */}
              {/* <div className='col-md-6'>

                <LineChart dates={dates9} header="YTD % of Issued Permits" title="%" />
              </div>

              <div className='row mt-5'>

                <div className='mt-5 mb-3'>
                  <h4 className='text-center'> Types of Issued Permits</h4>
                </div>
                <div className='col-md-6'>

                  <PolarChart />
                </div>
              </div> */}

            {/* </div> */}
          {/* </CustomTabPanel> */}
          <CustomTabPanel value={topLevelValue} index={1}>
            {/* <h3 className='mt-5'>ePermit to Work Reports</h3>
            <Box sx={{ width: '100%' }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={value} onChange={handleChange} aria-label="eptw report table">
                  <Tab label="All Permits" {...a11yProps(0)} />
                  <Tab label="Active" {...a11yProps(1)} />
                  <Tab label="Active: Timed Out" {...a11yProps(2)} />
                  <Tab label="Under Review" {...a11yProps(3)} />
                  <Tab label="Revoked" {...a11yProps(4)} />
                  <Tab label="Archived" {...a11yProps(5)} />

                </Tabs>
              </Box>
              <CustomTabPanel value={value} index={0}> */}
                <AllPermits />
              {/* </CustomTabPanel>
              <CustomTabPanel value={value} index={1}>
                <Permit status={'Active'} />
              </CustomTabPanel>
              <CustomTabPanel value={value} index={2}>
                <Permit status={'Active: Timed Out'} />
              </CustomTabPanel>

              <CustomTabPanel value={value} index={3}>
                <Permit status={'Pending Approval'} />
              </CustomTabPanel>

              <CustomTabPanel value={value} index={4}>
                <Permit status={'Revoked'} />
              </CustomTabPanel>

              <CustomTabPanel value={value} index={5}>
                <Permit status={'Inactive: Timed Out'} />
              </CustomTabPanel>

            </Box> */}
          </CustomTabPanel>

        </Box>



      </CardOverlay>
    </>
  )
}

export default Eptw
