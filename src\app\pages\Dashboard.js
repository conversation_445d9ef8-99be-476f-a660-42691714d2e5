import React, { useState, useEffect, useMemo } from 'react';
import { Line, Bar, Doughnut, Chart } from 'react-chartjs-2';
import { ProgressBar, Dropdown } from 'react-bootstrap';
import Progress from '@delowar/react-circle-progressbar';
// import CardOverlay from './CardOverlay';
import { useSelector } from "react-redux";
import { AIR_URL, AIR_WITH_ID_URL, ACTION_URL, ALL_AIR_REPORTS_URL, STATIC_URL, MY_ACTION_URL } from '../constants';
import AllIncident from './AllIncident';
import IncidentTable from './IncidentTable'
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import CardOverlay from './CardOverlay';
import AirViewCard from './AirViewCard';
import AirInvestigationCard from './AirInvestigationCard';
import API from '../services/API';
import OverdueTask from './OverdueTask';
import Action from './Action';
import DocAction from './DocAction';
import ActionCard from './ActionCard';
import ActionLog from './ActionLog';
import moment from 'moment';
//Doughchart Legend
const Dashboard = () => {
  const [docCount, setDocCount] = useState(0)
  const [airCount, setAirCount] = useState(0)
  const [obsCount, setObsCount] = useState(0)
  const [raCount, setRaCount] = useState(0)
  const [eptwCount, setEptwCount] = useState(0)
  const [action, setAction] = useState([])
  const [date, setDate] = useState([])
  const [total, setTotal] = useState(0)
  const [search, setSearch] = useState([])
  const me = useSelector((state) => state.login.user)

  const isAirGroup = useMemo(() => {
    return me?.roles?.some(item => item.name === 'AIR Group') || false;
  }, [me]);

  const defaultMaterialTheme = createTheme();
  const tableOptions = {
    actionsColumnIndex: -1,
    pageSize: 20,
    actionsCellStyle: {
      padding: '1.125rem 1.375rem',
    },
    headerStyle: {

      padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    },
    rowStyle: {
      // padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    }
  }
  const incidentColumns = [
    {
      title: "IR #",
      defaultSort: 'desc',
      field: "maskId",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      },
      render: rowData => (
        <p onClick={() => viewAir(rowData.id)} className={`maskId ${rowData.created && moment().diff(moment(rowData.created, "DD-MM-YYYY HH:mm"), 'hours') > 12 && rowData.status === 'Stage I: Preliminary Notification' ? 'red' : ''}`}>
          {rowData.maskId}
        </p>
      )
    },
    {
      title: "Description",
      field: "description",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },
    {
      title: "Status",
      field: "status",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    }

  ]

  const tableActions = [
    {
      icon: 'visibility',
      tooltip: 'View',
      onClick: (event, rowData) => {
        // Do save operation
        // console.log(rowData)
        viewAir(rowData.id)
      }
    },

  ]

  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };

  const [data, setData] = useState([]);

  useEffect(() => {
    // getAirData();
    getActions();

  }, [])

  const formatDateTime = (dateTimeString) => {

    const dateTimeParts = dateTimeString.split(' ');
    let dateParts = ''
    const regex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;

    if (regex.test(dateTimeString)) {
      dateParts = dateTimeParts[0].split('/');
    } else {
      dateParts = dateTimeParts[0].split('-');
    }

    const day = parseInt(dateParts[0], 10);
    const month = parseInt(dateParts[1], 10);
    const year = parseInt(dateParts[2], 10);

    // Format day with leading zero if needed
    const formattedDay = (day < 10 ? '0' : '') + day;
    // Get month abbreviation
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const formattedMonth = monthNames[month - 1];
    // Format year
    const formattedYear = year;

    return `${formattedDay} ${formattedMonth}, ${formattedYear}`;
  }
  const getActions = async () => {
    const response = await API.get(ACTION_URL);
    if (response.status === 200) {


      const Doc = response.data
        .filter(i => i.application === 'DOC' && i.status !== 'completed')
        .reverse()
      setDocCount(Doc.length);
      const Air = response.data
        .filter(i => i.application === 'AIR' && i.status !== 'completed')
        .reverse()
      setAirCount(Air.length);
      const Obs = response.data
        .filter(i => i.application === 'Observation' && i.status !== 'completed')
        .reverse()
      setObsCount(Obs.length);
      const ra = response.data
        .filter(i => i.application === 'RA' && i.status !== 'completed')
        .reverse()
      setRaCount(ra.length);
      const eptw = response.data
        .filter(i => i.application === 'PermitToWork' && i.status !== 'completed')
        .reverse()
      setEptwCount(eptw.length);


    }

  }

  const getAirData = async () => {

    const url = `${ALL_AIR_REPORTS_URL}`;
    const response = await API.get(url);
    if (response.status === 200) {
      setData(response.data)
    }
  }
  const [files, setFiles] = useState([]);

  const handleFileChange = (file) => {
    setFiles(file)

  }


  const [showModal, setShowModal] = useState(false)
  const [showInvestigationModal, setShowInvestigationModal] = useState(false)

  const [currentIncident, setCurrentIncident] = useState('')
  const viewAir = async (id) => {
    setCurrentIncident(id);
    getReportIncident(id);

  }

  const [incidentData, setIncidentData] = useState({})
  const getReportIncident = async (id) => {

    const uriString = {
      include: [
        'locationOne',
        'locationTwo',
        'locationThree',
        'locationFour',
        'locationFive',
        'locationSix',
        'lighting',
        'surfaceCondition',
        'surfaceType',
        'workActivityDepartment',
        'workActivity',
        'reporter',
        'workingGroup',
        'weatherCondition',
        'reviewer',
        'drivers',
        'surveyor',
        'estimator',
        'trainee',
        'gmOps',
        'thirdParty',
        'security',
        'costReviewer',
        'financer',
        'dutyEngManager',
        'incidentTypeName'
      ]
    };

    const url = `${AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

    const response = await API.get(url);
    if (response.status === 200) {

      const data = response.data;
      response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []
      setIncidentData(data)
      setShowModal(true)


    }
  }

  function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== tabValue}
        id={`incident-tabpanel-${tabValue}`}
        aria-labelledby={`incident-tab-${tabValue}`}
        {...other}
      >
        {value === tabValue && (
          <Box sx={{ p: 3 }}>
            {children}
          </Box>
        )}
      </div>
    );
  }

  CustomTabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
  };

  const viewInvestigation = async (id) => {
    setCurrentIncident(id);
    getReportIncidentForInvestigation(id);

  }

  const getReportIncidentForInvestigation = async (id) => {

    const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'lighting', 'surfaceCondition', 'surfaceType', 'workActivityDepartment', 'workActivity', 'reporter'] }

    const url = `${AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

    const response = await API.get(url);
    if (response.status === 200) {

      const data = response.data;
      setIncidentData(data)
      setShowInvestigationModal(true)


    }
  }

  const tasks = [
    { id: 'AIR-20230908-07', createdOn: '2023-09-07', overdueDays: 50 },
    { id: 'AIR-20230908-10', createdOn: '2023-09-10', overdueDays: 30 },

  ];

  const TABS = {
    ALL: "ALL",
    IR: "IR",
    DOCUMENT: "DOCUMENT",
    // UNDER_INVESTIGATION: "UNDER_INVESTIGATION",
    // ACTIONS: "ACTIONS"

  };

  const [value, setValue] = useState(0);


  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  useEffect(() => {
    myAction();

  }, [])
  const myAction = async () => {
    const response = await API.get(MY_ACTION_URL);
    if (response.status === 200) {
      setTotal(response.data.length)
      setSearch(response.data)

      const actions = response.data.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));

      const groupedActions = actions.reduce((acc, action) => {
        const [date, time] = action.createdDate.split(' '); // Extracting date and time parts
        const formattedDate = formatDateTime(date);
        if (!acc[formattedDate]) {
          acc[formattedDate] = [];
        }
        acc[formattedDate].push({ ...action, time });
        return acc;
      }, {});


      // setDate(Object.keys(groupedActions).reverse())

      const sortedDates = Object.keys(groupedActions).reverse().sort((a, b) => {
        const dateA = new Date(a);
        const dateB = new Date(b);
        return dateB - dateA; // Sorting in descending order
      });

      setDate(sortedDates)

      setAction(groupedActions)

    }
  }
  const onSearchActivity = (e) => {
    const filter = search.find(item => item.applicationDetails.maskId === e)
    console.log(search)

    // setTotal(filter.length)

    // const actions = filter.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));

    // const groupedActions = actions.reduce((acc, action) => {
    //   const [date, time] = action.createdDate.split(' '); // Extracting date and time parts
    //   const formattedDate = formatDateTime(date);
    //   if (!acc[formattedDate]) {
    //     acc[formattedDate] = [];
    //   }
    //   acc[formattedDate].push({ ...action, time });
    //   return acc;
    // }, {});


    // // setDate(Object.keys(groupedActions).reverse())

    // const sortedDates = Object.keys(groupedActions).reverse().sort((a, b) => {
    //   const dateA = new Date(a);
    //   const dateB = new Date(b);
    //   return dateB - dateA; // Sorting in descending order
    // });

    // setDate(sortedDates)

    // setAction(groupedActions)
  }

  return (

    <>
      <h5 className='mt-4 fw-bold actionTitle'>My Actions</h5>
      <p className='mb-5 actionDesc'>Click on the specific application to see a listing of actions that you need to take.</p>

      <div className='row'>

        <ActionCard count={obsCount} title={'Observation'} url={'/apps/observation'} desc={'Record and track observations seamlessly for improved workplace safety and efficiency.'} color={'#EF4444'} />
        <ActionCard count={airCount} title={'Incident'} url={'/apps/incident'} desc={'Report and manage incidents effectively to mitigate risks and enhance safety measures.'} color={'#60A5FA'} />
        <ActionCard count={eptwCount} title={'ePermit to Work'} url={'/apps/eptw'} desc={'Streamline permit management processes to ensure safety and compliance.'} color={'#FBBF24'} />
        <ActionCard count={docCount} title={'Document'} url={'/apps/document'} desc={'Organize and access documents efficiently for streamlined workflows and compliance.'} color={'#A78BFA'} />
        <ActionCard count={raCount} title={'Integrated Risk Assessment'} url={'/apps/risk'} desc={'Identify, assess, and mitigate risks to enhance decision-making and operational resilience.'} color={'#F472B6'} />
        <ActionCard count={'0'} title={'Knowledge'} url={'/apps/knowledge'} desc={'Centralize knowledge resources for enhanced collaboration and informed decision-making.'} color={'#10B981'} />
        <ActionCard count={'0'} title={'Asset'} url={'/apps/asset'} desc={'Optimize asset utilization and maintenance for improved productivity and cost-efficiency.'} color={'#6677e8'} />


      </div>

      <div className='row'>
        {action.length !== 0 ? <ActionLog data={action} dates={date} total={total} onSearch={onSearchActivity} /> : ''}
      </div>
      {/* <CardOverlay>

      <>
        <div>
          <div className="row">

            <div className="col-12">

            
              <div className="card">
                <div className="card-body">

                  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>


                    <h4 className=''>Actions</h4>

                    <Tabs value={value} onChange={handleChange} aria-label="incident report table">
                      {isAirGroup && <Tab label="All" value={TABS.ALL} />}

                      <Tab label={"IR"+' ('+airCount+')'} value={TABS.IR} />

                      <Tab label={"Document"+' ('+docCount+')'} value={TABS.DOCUMENT} />
                     
                    </Tabs>
                  </Box>


                  <CustomTabPanel value={value} tabValue={TABS.ALL}>
                  

                  </CustomTabPanel>

                  <CustomTabPanel value={value} tabValue={TABS.IR}>
                  
                    {
                      isAirGroup && (
                        <Action application={"AIR"} id={false} />
                      )}
                  </CustomTabPanel>

                  <CustomTabPanel value={value} tabValue={TABS.DOCUMENT}>
                   
                    <DocAction application={"DOC"} id={false} />
                  </CustomTabPanel>

                 

            
                </div>
              </div>
            </div>
          </div>
        </div>

        {(incidentData && showModal) && <AirViewCard showModal={showModal} setShowModal={setShowModal} data={incidentData} setData={setIncidentData} />}
       

      </>



    </CardOverlay> */}
    </>
  );
}


export default Dashboard;