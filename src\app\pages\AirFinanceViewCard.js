import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';

import API from "../services/API";
import { DropzoneArea } from 'material-ui-dropzone';

import { AIR_COST_REVIEWER_URL, AIR_COST_REVIEWER_WITH_ID_URL, AIR_DUTY_MANAGER_ESTIMATION_WITH_ID_URL, AIR_FINANCER_WITH_ID_URL, AIR_FINANCE_URL, STATIC_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';
import Switch from "react-switch";
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import Grid from '@mui/material/Grid'; // Import Grid
import FormControl from '@mui/material/FormControl'; // Import FormControl
import FormLabel from '@mui/material/FormLabel'; // Import FormLabel
import RadioGroup from '@mui/material/RadioGroup'; // Import RadioGroup
import FormControlLabel from '@mui/material/FormControlLabel'; // Import FormControlLabel
import Radio from '@mui/material/Radio'; // Import Radio
import TextField from '@mui/material/TextField'; // Import TextField
import InvoiceComponent from "./InvoiceComponent";
import Typography from '@mui/material/Typography';
import { InputNumber } from 'primereact/inputnumber';
import ThirdPartyForm from "./ThirdPartyForm";
const AirFinanceViewCard = ({ showModal, setShowModal, data, setData }) => {


    const [showLeftPane, setShowLeftPane] = useState(true);

    const [combinedInvoiceItems, setCombinedInvoiceItems] = useState([]);
    const [damagedData, setDamagedData] = useState({})
    const handleUpdateApiData = (updatedApiData) => {
        setApiData(updatedApiData);
    };
    const handleAddCombinedInvoiceItem = (item, title) => {
        setCombinedInvoiceItems([...combinedInvoiceItems, { ...item, title }]);
    };

    const [apiData, setApiData] = useState(data.costActual);
    const TotalCostCalculator = (data) => {
        if (!data) return "0.00";

        // Function to calculate total for each costEstimation
        const calculateTotalForCostEstimation = (costEstimation) => {
            return costEstimation.reduce((total, current) => {
                // Calculate the total for each item inside the "items" array
                const itemsTotal = current.items.reduce((itemTotal, item) => {
                    return itemTotal + (parseFloat(item.price) * parseFloat(item.quantity));
                }, 0);

                // Accumulate the total for this costEstimation category
                return total + itemsTotal;
            }, 0);
        };

        // Calculate the grand total for all items
        const grandTotal = data.reduce((total, currentItem) => {
            // Ensure costEstimation is an array
            const costEstimation = Array.isArray(currentItem.costEstimation) ? currentItem.costEstimation : [];

            // Calculate the total for the costEstimation of the current item
            const itemTotal = calculateTotalForCostEstimation(costEstimation);

            // Accumulate the grand total
            return total + itemTotal;
        }, 0);

        return grandTotal.toFixed(2);
    };
    const [totalIncidentCost, setTotalIncidentCost] = useState(0)



    const [isRecovery, setIsRecovery] = useState(false);
    const [isAcknowledged, setIsAcknowledged] = useState(false);

    const [percentageRecovery, setPercentageRecovery] = useState(0);
    const [insuranceRefNo, setInsuranceRefNo] = useState('');
    const [insuranceClaimAmount, setInsuranceClaimAmount] = useState(0.00);
    const [claimStatus, setClaimStatus] = useState('');
    const [claimPaymentRef, setClaimPaymentRef] = useState('');
    const [claimPaymentAmount, setClaimPaymentAmount] = useState(0.00);
    useEffect(() => {
        setTotalIncidentCost(TotalCostCalculator(data.costActual))

    }, [data.costActual])

    const [thirdParty, setThirdParty] = useState({});
    const updateForms = (forms) => {
        setThirdParty(forms)
    }

    useEffect(() => {
        setThirdParty(data.thirdPartyForm)
        setIsRecovery(data.isRecoveryFromOtherParty)

    }, [data])

    const handleSubmit = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_FINANCER_WITH_ID_URL(data.id, data.actionId), {
                incidentCost: parseFloat(totalIncidentCost),

                isAcknowledgementRecevied: isAcknowledged,
                insuranceRefNo: insuranceRefNo,
                insuranceClaimAmount: insuranceClaimAmount,
                claimStatus: claimStatus,
                claimPaymentRef: claimPaymentRef,
                claimPaymentAmount: claimPaymentAmount,

            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };
    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }

    const handleReturn = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL
            // const response = await API.patch(AIR_FINANCER_RETURN_WITH_ID_URL(data.id, data.actionId), {


            // }

            // );

            // If the patch request fails, no need to proceed further
            // if (response.status !== 204) {
            //     console.error('Failed to patch data. Status:', response.status);
            //     return;  // or handle this error appropriately
            // }

            cogoToast.success(`Action for IR ${data.maskId} Returned`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };

    useEffect(() => {
        if (data?.costActual) {

            setIsAcknowledged(data.isAcknowledgementRecevied)
            setInsuranceRefNo(data.insuranceRefNo)
            setInsuranceClaimAmount(data.insuranceClaimAmount)
            setClaimStatus(data.claimStatus)
            setClaimPaymentRef(data.claimPaymentRef)
            setClaimPaymentAmount(data.claimPaymentAmount)
            setDamagedData(data.costActual)
        }
    }, [data]);
    return (
        <>
            {data &&
                <div className="row">



                    <div className={`col-md-12`}>
                        <Box>

                            <div className="row">
                                <div className="table-responsive">
                                    <label>Actual Cost Estimated</label>

                                    <div className="col-md-12">
                                        <div className="row">
                                            {damagedData?.length > 0 && (
                                                damagedData.map((item, index) => (
                                                    <Accordion key={index} TransitionProps={{ unmountOnExit: true }} className="border mb-3">
                                                        <AccordionSummary
                                                            expandIcon={<ExpandMoreIcon />}
                                                            aria-controls={`panel${index}a-content`}
                                                            id={`panel${index}a-header`}

                                                        >
                                                            <Typography><strong>Equipment #{index + 1}:</strong> {item.category}</Typography>
                                                            <Typography style={{ marginLeft: 'auto', opacity: 0.7 }}>Click to expand</Typography>
                                                        </AccordionSummary>
                                                        <AccordionDetails>
                                                            <Box sx={{ width: '100%' }}>
                                                                <Typography variant="body2" paragraph>
                                                                    Detailed information for <strong>{item.category}</strong>:
                                                                </Typography>
                                                                <Typography variant="body2" paragraph>
                                                                    Uploaded Documents:

                                                                    {item?.files?.map(i => {
                                                                        return (
                                                                            <div>
                                                                                <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                                            </div>
                                                                        )
                                                                    })}

                                                                </Typography>
                                                                <Grid container spacing={2}>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Number:</strong> {item.number}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Damage Type:</strong> {item.damageType}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6}>
                                                                        <Typography><strong>Cost Details:</strong> {item.costDetails}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Work Order Number:</strong> {item.workOrderNumber}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Operational:</strong> {item.operational ? "Yes" : "No"}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6}>

                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <TextField
                                                                            label="Inspection Date"
                                                                            type="date"
                                                                            value={damagedData[index].inspectionDate}
                                                                            style={{ color: 'black' }}
                                                                            disabled={true}
                                                                            InputLabelProps={{ shrink: true }}
                                                                            fullWidth
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={9}>
                                                                        <TextField
                                                                            label="Inspection Remarks"
                                                                            type="text"
                                                                            value={damagedData[index].inspectionRemarks}
                                                                            style={{ color: 'black' }}
                                                                            disabled={true}
                                                                            variant="outlined"
                                                                            fullWidth
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12}>
                                                                        <InvoiceComponent editable={false} costData={damagedData[index].costEstimation} />
                                                                    </Grid>
                                                                </Grid>
                                                            </Box>
                                                        </AccordionDetails>
                                                    </Accordion>
                                                ))
                                            )}
                                        </div>

                                    </div>
                                </div>
                            </div>
                            {/* <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="">Percentage of Recovery </label>
                                            <input type='number' value={percentageRecovery} onChange={(e) => setPercentageRecovery(parseInt(e.target.value))} className='form-control' />
                                        </div>
                                    </div>
                                </div>

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group d-flex align-items-center'>
                                            <label htmlFor="" className='m-0 me-3'>Is Recovery From Other Party?</label>

                                            <Switch onChange={(value) => setIsRecovery(value)} checked={isRecovery} />
                                        </div>
                                    </div>
                                </div> */}
                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <Typography variant="body2" paragraph>
                                            Uploaded Documents By Cost Estimator:

                                            {data.costEstimationFiles?.map(i => {
                                                return (
                                                    <div>
                                                        <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                    </div>
                                                )
                                            })}

                                        </Typography>
                                    </div>
                                </div>
                            </div>


                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <Typography variant="body2" paragraph>
                                            Uploaded Documents by Duty Engineer Manager:

                                            {data.dutyEngineerManagerFiles?.map(i => {
                                                return (
                                                    <div>
                                                        <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                    </div>
                                                )
                                            })}

                                        </Typography>
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <Typography variant="body2" paragraph>
                                            Uploaded Documents by Claims Coordinator:

                                            {data.claimsCoordinatorFiles?.map(i => {
                                                return (
                                                    <div>
                                                        <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                    </div>
                                                )
                                            })}

                                        </Typography>
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <Typography variant="body2" paragraph>
                                            Uploaded Documents by HOD:

                                            {data.hodFiles?.map(i => {
                                                return (
                                                    <div>
                                                        <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                    </div>
                                                )
                                            })}

                                        </Typography>
                                    </div>
                                </div>
                            </div>
                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Total Incident Cost: {totalIncidentCost} LKR</label>


                                    </div>
                                </div>
                            </div>

                            {isRecovery && <div className='row'>
                                <label htmlFor="" className='m-0 me-3'>Recovery Parties</label>

                                <ThirdPartyForm readOnly={true} updateForms={updateForms} values={thirdParty} />




                            </div>}
                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Is Acknowledgement Received?</label>

                                        <Switch disabled onChange={(value) => setIsAcknowledged(value)} checked={isAcknowledged} />
                                    </div>
                                </div>
                            </div>


                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="insuranceRefNo">Insurance Ref No</label>
                                        <input
                                            type='text'
                                            value={insuranceRefNo}
                                            onChange={(e) => setInsuranceRefNo(e.target.value)}
                                            className='form-control'
                                            disabled
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="insuranceClaimAmount">Insurance Claim Amount</label>
                                        <InputNumber
                                            disabled
                                            id="insuranceClaimAmount"
                                            value={insuranceClaimAmount}
                                            onValueChange={(e) => setInsuranceClaimAmount(parseInt(e.value))}
                                            mode="decimal"
                                            min={0}
                                            className='w-100'
                                            placeholder="0.00"
                                            locale="en-US"
                                            minFractionDigits={2}
                                            maxFractionDigits={2}
                                        />

                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="claimStatus">Claim Status</label>
                                        <input
                                            type='text'
                                            value={claimStatus}
                                            onChange={(e) => setClaimStatus(e.target.value)}
                                            className='form-control'
                                            disabled
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="claimPaymentRef">Claim Payment Ref</label>
                                        <input
                                            type='text'
                                            value={claimPaymentRef}
                                            onChange={(e) => setClaimPaymentRef(e.target.value)}
                                            className='form-control'
                                            disabled
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="claimPaymentAmount">Claim Payment Amount</label>
                                        <InputNumber
                                            disabled
                                            id="claimPaymentAmount"
                                            value={claimPaymentAmount}
                                            onValueChange={(e) => setClaimPaymentAmount(parseInt(e.value))}
                                            mode="decimal"
                                            min={0}
                                            className='w-100'
                                            placeholder="0.00"
                                            locale="en-US"
                                            minFractionDigits={2}
                                            maxFractionDigits={2}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="chequeNumber">Cheque Number / Debit note number</label>
                                        <input

                                            type='text'
                                            value={data?.chequeNumber || ''}
                                            disabled
                                            className='form-control'
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="recoveryAmount">Recovery Amount</label>
                                        <input

                                            type='number'
                                            value={data?.recoveryAmount || ''}
                                            disabled
                                            className='form-control'
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="recoveryDate">Recovery Date</label>
                                        <input

                                            type='date'
                                            value={data?.recoveryDate || ''}
                                            disabled
                                            className='form-control'
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="recoveryDate">Payment Mode</label>
                                        <input

                                            type='text'
                                            value={data?.paymentMode || ''}
                                            disabled
                                            className='form-control'
                                        />
                                    </div>
                                </div>
                            </div>
                            {/* <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <DropzoneArea
                                            acceptedFiles={[
                                                'application/pdf',
                                                'image/jpeg',
                                                'image/png'

                                            ]}
                                            dropzoneText={"Drag and drop files / documents / pictures"}
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            onChange={handleFileChange}
                                        />
                                    </div>
                                </div>
                            </div> */}


                        </Box>
                    </div>


                </div>


            }
        </>
    )
}

export default AirFinanceViewCard;