import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Alert } from 'react-bootstrap';
import './MainDashboard.scss';
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import PropTypes from 'prop-types';
import * as Icon from 'feather-icons-react';
import { useSelector } from 'react-redux';
import API from '../services/API';
import { OBSERVATION_REPORT_URL, OBSERVATION_REPORT_BY_OTHERS_URL, ALL_AIR_REPORTS_URL, ALL_PERMITS_URL } from '../constants';
import ObservationAnalytics from '../analytics/ObservationAnalytics';
import IncidentAnalytics from '../analytics/IncidentAnalytics';
import EPermitAnalytics from '../analytics/EPermitAnalytics';
import moment from 'moment';

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};

function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`main-dashboard-tabpanel-${tabValue}`}
      aria-labelledby={`main-dashboard-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  tabValue: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
};

const MainDashboard = () => {
  const user = useSelector((state) => state.login.user);
  const [value, setValue] = useState('OBSERVATION');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Data states for different modules
  const [observationData, setObservationData] = useState([]);
  const [incidentData, setIncidentData] = useState([]);
  const [epermitData, setEpermitData] = useState([]);
  const [documentData, setDocumentData] = useState([]);
  const [riskAssessmentData, setRiskAssessmentData] = useState([]);
  const [knowledgeData, setKnowledgeData] = useState([]);
  const [assetData, setAssetData] = useState([]);

  const TABS = {
    OBSERVATION: "OBSERVATION",
    INCIDENT: "INCIDENT",
    EPERMIT: "EPERMIT",
    DOCUMENT: "DOCUMENT",
    RISK_ASSESSMENT: "RISK_ASSESSMENT",
    KNOWLEDGE: "KNOWLEDGE",
    ASSET: "ASSET"
  };

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  // Fetch observation data
  const fetchObservationData = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        "include": [
          { "relation": "ghsOne" }, 
          { "relation": "workActivityDepartment" }, 
          { "relation": "submitted" }, 
          { "relation": "actions" }
        ]
      };

      // Fetch both user's observations and others' observations
      const [userObsResponse, othersObsResponse] = await Promise.all([
        API.get(`${OBSERVATION_REPORT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`),
        API.get(`${OBSERVATION_REPORT_BY_OTHERS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`)
      ]);

      const processObservationData = (data) => {
        return data.map(item => ({
          ...item,
          'submitted.firstName': item.submitted ? item.submitted.firstName : '',
          'color': setColor(item),
          created: moment(item.created).format('Do MMM YYYY'),
          type: item.type === 'Safe' ? 'Positive' : 
                JSON.parse(item.remarks || '{}').unsafeCondition === true ? "Unsafe Condition" : 
                JSON.parse(item.remarks || '{}').unsafeAct === true ? "Unsafe Act" : 
                item.type === 'Positive' ? 'Positive' : item.type,
          status: setStatus(item),
          assignee: item.actionOwnerId
        }));
      };

      const userObsData = userObsResponse.status === 200 ? processObservationData(userObsResponse.data) : [];
      const othersObsData = othersObsResponse.status === 200 ? processObservationData(othersObsResponse.data) : [];

      // Combine and sort data
      const combinedData = [...userObsData, ...othersObsData].sort((a, b) => {
        let aIdLastPart = parseInt(a.maskId.split('-').pop());
        let bIdLastPart = parseInt(b.maskId.split('-').pop());
        return bIdLastPart - aIdLastPart;
      });

      setObservationData(combinedData);
    } catch (err) {
      console.error('Error fetching observation data:', err);
      setError('Failed to fetch observation data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions for observation data processing
  const setColor = (item) => {
    if (item.type === 'Safe' || item.status === 'At Risk - Closed' || item.status === 'Approved' || item.dueDate === '') {
      return 'None';
    }

    if (moment().isAfter(moment(item.dueDate, 'DD-MM-YYYY'))) {
      return 'Overdue';
    } else if (moment().isSame(moment(item.dueDate, 'DD-MM-YYYY'))) {
      return 'Due Soon';
    } else if (moment().isBefore(moment(item.dueDate, 'DD-MM-YYYY'))) {
      return 'Upcoming';
    }
  };

  const setStatus = (item) => {
    if (item.rectifiedStatus === 'Yes') {
      return 'Reported & Rectified on Spot';
    }
    if (item.type === 'Safe') {
      return 'Reported & Closed';
    }

    let status = '';
    switch (item.status) {
      case 'In Review':
        status = 'Actions Taken - Pending Verification';
        break;
      case 'Returned':
        status = 'Action Reassigned';
        break;
      case 'Initiated':
        status = 'Actions Assigned';
        break;
      case 'Approved':
        status = 'Action Verified - Closed';
        break;
      default:
        status = item.status;
        break;
    }

    return status;
  };

  // Fetch incident data
  const fetchIncidentData = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        include: [
          'locationOne',
          'locationTwo',
          'locationThree',
          'locationFour',
          'locationFive',
          'locationSix',
          'lighting',
          'surfaceCondition',
          'surfaceType',
          'workActivityDepartment',
          'workActivity',
          'reporter',
          'workingGroup',
          'weatherCondition',
          'reviewer',
          'drivers',
          'surveyor',
          'estimator',
          'trainee',
          'gmOps',
          'thirdParty',
          'security',
          'costReviewer',
          'financer',
          'dutyEngManager',
          'incidentTypeName',
        
        ]
      };

      const response = await API.get(`${ALL_AIR_REPORTS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);

      if (response.status === 200) {
        const processedData = response.data
          .filter(i => !i.isArchive)
          .map(rowData => {
            // Parse incidentDate using multiple possible formats
            const date = moment(rowData.incidentDate, [
              "DD-MM-YYYY HH:mm",   // 24-hour format
              "DD-MM-YYYY hh:mm A"  // 12-hour format with AM/PM
            ], true);

            const isValidDate = date.isValid();
            const formattedDate = isValidDate ? date.format("DD-MM-YYYY HH:mm") : rowData.incidentDate || "Invalid Date";

            return {
              ...rowData,
              incidentDate: formattedDate,
              created: formattedDate
            };
          })
          .sort((a, b) => {
            // Sort by maskId in descending order
            let aIdLastPart = parseInt(a.maskId?.split('-').pop() || 0);
            let bIdLastPart = parseInt(b.maskId?.split('-').pop() || 0);
            return bIdLastPart - aIdLastPart;
          });

        setIncidentData(processedData);
      }
    } catch (err) {
      console.error('Error fetching incident data:', err);
      setError('Failed to fetch incident data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchEpermitData = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" }
        ]
      };

      const response = await API.get(`${ALL_PERMITS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);

      if (response.status === 200) {
        const processedData = response.data
          .map(rowData => {
            // Process permit data
            const createdDate = moment(rowData.created, ['DD-MM-YYYY hh:mm A', 'DD-MM-YYYY HH:mm', moment.ISO_8601]);
            const formattedCreated = createdDate.isValid() ? createdDate.format("DD-MM-YYYY HH:mm") : rowData.created;

            return {
              ...rowData,
              created: formattedCreated,
              // Add computed fields for easier filtering
              location: `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''}`,
              closureStatus: rowData.closure ? (rowData.closure.status ? rowData.closure.status : 'N/A') : 'N/A',
              closeoutDate: rowData.closure && rowData.closure.closeoutDate
                ? moment(rowData.closure.closeoutDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A')
                : 'N/A'
            };
          })
          .reverse() // Most recent first
          .sort((a, b) => {
            // Sort by maskId in descending order
            let aIdLastPart = parseInt(a.maskId?.split('-').pop() || 0);
            let bIdLastPart = parseInt(b.maskId?.split('-').pop() || 0);
            return bIdLastPart - aIdLastPart;
          });

        setEpermitData(processedData);
      }
    } catch (err) {
      console.error('Error fetching ePermit data:', err);
      setError('Failed to fetch ePermit data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchDocumentData = async () => {
    // TODO: Implement document data fetching
    setDocumentData([]);
  };

  const fetchRiskAssessmentData = async () => {
    // TODO: Implement risk assessment data fetching
    setRiskAssessmentData([]);
  };

  const fetchKnowledgeData = async () => {
    // TODO: Implement knowledge data fetching
    setKnowledgeData([]);
  };

  const fetchAssetData = async () => {
    // TODO: Implement asset data fetching
    setAssetData([]);
  };

  useEffect(() => {
    // Fetch data based on active tab
    switch (value) {
      case TABS.OBSERVATION:
        fetchObservationData();
        break;
      case TABS.INCIDENT:
        fetchIncidentData();
        break;
      case TABS.EPERMIT:
        fetchEpermitData();
        break;
      case TABS.DOCUMENT:
        fetchDocumentData();
        break;
      case TABS.RISK_ASSESSMENT:
        fetchRiskAssessmentData();
        break;
      case TABS.KNOWLEDGE:
        fetchKnowledgeData();
        break;
      case TABS.ASSET:
        fetchAssetData();
        break;
      default:
        break;
    }
  }, [value]);

  const PlaceholderContent = ({ moduleName, icon: IconComponent }) => (
    <div className="placeholder-content">
      <IconComponent size={64} className="text-muted mb-3" />
      <h3 className="text-muted">{moduleName} Analytics</h3>
      <p className="text-muted">Analytics dashboard for {moduleName.toLowerCase()} will be implemented here.</p>
      <Alert variant="info" className="mt-3">
        <strong>Coming Soon!</strong> This module's analytics dashboard is under development.
      </Alert>
    </div>
  );

  return (
    <Container fluid className="main-dashboard">
      <div className="mb-4">
        <h1 className="text-primary mb-2">Analytics Dashboard</h1>
        <p className="text-muted">Comprehensive analytics and insights across all modules</p>
      </div>

      <Card className="shadow-sm">
        <Card.Body className="p-0">
          <Tabs 
            value={value} 
            onChange={handleChange} 
            aria-label="main dashboard tabs" 
            className="border-bottom"
            variant="scrollable"
            scrollButtons="auto"
          >
            <MTab 
              label={
                <Typography variant="body1" style={customFontStyle}>
                  <Icon.Eye className="me-2" size={16} />
                  Observation <span className='badge bg-primary ms-2'>{observationData.length}</span>
                </Typography>
              } 
              value={TABS.OBSERVATION} 
            />
            
            <MTab 
              label={
                <Typography variant="body1" style={customFontStyle}>
                  <Icon.AlertTriangle className="me-2" size={16} />
                  Incident <span className='badge bg-danger ms-2'>{incidentData.length}</span>
                </Typography>
              } 
              value={TABS.INCIDENT} 
            />
            
            <MTab 
              label={
                <Typography variant="body1" style={customFontStyle}>
                  <Icon.FileText className="me-2" size={16} />
                  ePermit to Work <span className='badge bg-warning ms-2'>{epermitData.length}</span>
                </Typography>
              } 
              value={TABS.EPERMIT} 
            />
            
            <MTab 
              label={
                <Typography variant="body1" style={customFontStyle}>
                  <Icon.File className="me-2" size={16} />
                  Document <span className='badge bg-info ms-2'>{documentData.length}</span>
                </Typography>
              } 
              value={TABS.DOCUMENT} 
            />
            
            <MTab 
              label={
                <Typography variant="body1" style={customFontStyle}>
                  <Icon.Shield className="me-2" size={16} />
                  Risk Assessment <span className='badge bg-secondary ms-2'>{riskAssessmentData.length}</span>
                </Typography>
              } 
              value={TABS.RISK_ASSESSMENT} 
            />
            
            <MTab 
              label={
                <Typography variant="body1" style={customFontStyle}>
                  <Icon.BookOpen className="me-2" size={16} />
                  Knowledge <span className='badge bg-success ms-2'>{knowledgeData.length}</span>
                </Typography>
              } 
              value={TABS.KNOWLEDGE} 
            />
            
            <MTab 
              label={
                <Typography variant="body1" style={customFontStyle}>
                  <Icon.Package className="me-2" size={16} />
                  Asset <span className='badge bg-dark ms-2'>{assetData.length}</span>
                </Typography>
              } 
              value={TABS.ASSET} 
            />
          </Tabs>

          {error && (
            <Alert variant="danger" className="m-3">
              <Icon.AlertCircle className="me-2" size={16} />
              {error}
            </Alert>
          )}

          <CustomTabPanel value={value} tabValue={TABS.OBSERVATION}>
            {loading ? (
              <div className="loading-spinner text-center py-5">
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <p className="mt-3 text-muted">Loading observation data...</p>
              </div>
            ) : (
              <ObservationAnalytics data={observationData} />
            )}
          </CustomTabPanel>

          <CustomTabPanel value={value} tabValue={TABS.INCIDENT}>
            {loading ? (
              <div className="loading-spinner text-center py-5">
                <div className="spinner-border text-danger" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <p className="mt-3 text-muted">Loading incident data...</p>
              </div>
            ) : (
              <IncidentAnalytics data={incidentData} />
            )}
          </CustomTabPanel>

          <CustomTabPanel value={value} tabValue={TABS.EPERMIT}>
            {loading ? (
              <div className="loading-spinner text-center py-5">
                <div className="spinner-border text-warning" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <p className="mt-3 text-muted">Loading ePermit data...</p>
              </div>
            ) : (
              <EPermitAnalytics data={epermitData} />
            )}
          </CustomTabPanel>

          <CustomTabPanel value={value} tabValue={TABS.DOCUMENT}>
            <PlaceholderContent moduleName="Document" icon={Icon.File} />
          </CustomTabPanel>

          <CustomTabPanel value={value} tabValue={TABS.RISK_ASSESSMENT}>
            <PlaceholderContent moduleName="Risk Assessment" icon={Icon.Shield} />
          </CustomTabPanel>

          <CustomTabPanel value={value} tabValue={TABS.KNOWLEDGE}>
            <PlaceholderContent moduleName="Knowledge" icon={Icon.BookOpen} />
          </CustomTabPanel>

          <CustomTabPanel value={value} tabValue={TABS.ASSET}>
            <PlaceholderContent moduleName="Asset" icon={Icon.Package} />
          </CustomTabPanel>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default MainDashboard;
