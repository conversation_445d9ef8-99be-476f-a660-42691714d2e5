import React, { useState, useEffect, useMemo } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Row, Col, Card, Button, Form, Collapse } from 'react-bootstrap';
import Select from 'react-select';
import { Calendar } from 'primereact/calendar';
import * as Icon from 'feather-icons-react';
import moment from 'moment';
import {
  processEPermitStatusData,
  processEPermitClosureStatusData,
  processEPermitTypeData,
  processEPermitLevelData,
  processEPermitLocationData,
  processEPermitProjectData,
  processEPermitMonthlyTrends,
  processEPermitDurationData,
  processEPermitApprovalTimeData,
  getPieChartOptions,
  getColumnChartOptions,
  getLineChartOptions
} from '../utils/chartUtils';

const EPermitAnalytics = ({ data = [] }) => {
  const [filteredData, setFilteredData] = useState(data);
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: [],
    closureStatus: [],
    permitType: [],
    level: [],
    location: [],
    project: [],
    dateRange: { start: null, end: null }
  });
  
  const [chartData, setChartData] = useState({
    status: [],
    closureStatus: [],
    permitType: [],
    level: [],
    location: [],
    project: [],
    monthly: { categories: [], data: [] },
    duration: [],
    approvalTime: { data: [], avgApprovalTime: 0 }
  });

  // Generate filter options from data
  const filterOptions = useMemo(() => {
    if (!data || data.length === 0) return {};
    
    const uniqueValues = (accessor) => {
      const values = data.map(accessor).filter(Boolean);
      return [...new Set(values)].sort().map(value => ({ value, label: value }));
    };

    return {
      status: uniqueValues(item => item.status),
      closureStatus: uniqueValues(item => item.closure?.status),
      permitType: uniqueValues(item => item.permitType),
      level: uniqueValues(item => item.level || item.high_risk?.risk_level),
      location: uniqueValues(item => item.locationThree?.name),
      project: uniqueValues(item => item.locationFour?.name)
    };
  }, [data]);

  // Apply filters to data
  useEffect(() => {
    let filtered = [...data];

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(item => 
        filters.status.some(filter => filter.value === item.status)
      );
    }

    // Apply closure status filter
    if (filters.closureStatus.length > 0) {
      filtered = filtered.filter(item => 
        filters.closureStatus.some(filter => filter.value === item.closure?.status)
      );
    }

    // Apply permit type filter
    if (filters.permitType.length > 0) {
      filtered = filtered.filter(item => 
        filters.permitType.some(filter => filter.value === item.permitType)
      );
    }

    // Apply level filter
    if (filters.level.length > 0) {
      filtered = filtered.filter(item => {
        const level = item.level || item.high_risk?.risk_level;
        return filters.level.some(filter => filter.value === level);
      });
    }

    // Apply location filter
    if (filters.location.length > 0) {
      filtered = filtered.filter(item => 
        filters.location.some(filter => filter.value === item.locationThree?.name)
      );
    }

    // Apply project filter
    if (filters.project.length > 0) {
      filtered = filtered.filter(item => 
        filters.project.some(filter => filter.value === item.locationFour?.name)
      );
    }

    // Apply date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.created, ['DD-MM-YYYY hh:mm A', 'DD-MM-YYYY HH:mm', moment.ISO_8601]);
        if (!itemDate.isValid()) return false;

        const start = filters.dateRange.start ? moment(filters.dateRange.start).startOf('day') : null;
        const end = filters.dateRange.end ? moment(filters.dateRange.end).endOf('day') : null;

        if (start && end) {
          return itemDate.isBetween(start, end, null, '[]');
        } else if (start) {
          return itemDate.isSameOrAfter(start);
        } else if (end) {
          return itemDate.isSameOrBefore(end);
        }
        return true;
      });
    }

    setFilteredData(filtered);
  }, [data, filters]);

  // Update chart data when filtered data changes
  useEffect(() => {
    if (filteredData && filteredData.length >= 0) {
      setChartData({
        status: processEPermitStatusData(filteredData),
        closureStatus: processEPermitClosureStatusData(filteredData),
        permitType: processEPermitTypeData(filteredData),
        level: processEPermitLevelData(filteredData),
        location: processEPermitLocationData(filteredData),
        project: processEPermitProjectData(filteredData),
        monthly: processEPermitMonthlyTrends(filteredData),
        duration: processEPermitDurationData(filteredData),
        approvalTime: processEPermitApprovalTimeData(filteredData)
      });
    }
  }, [filteredData]);

  // Filter handling functions
  const handleFilterChange = (filterType, selectedOptions) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: selectedOptions || []
    }));
  };

  const handleDateRangeChange = (field, date) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: date
      }
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      status: [],
      closureStatus: [],
      permitType: [],
      level: [],
      location: [],
      project: [],
      dateRange: { start: null, end: null }
    });
  };

  const hasActiveFilters = () => {
    return Object.values(filters).some(filter => {
      if (Array.isArray(filter)) return filter.length > 0;
      if (typeof filter === 'object' && filter !== null) {
        return filter.start !== null || filter.end !== null;
      }
      return false;
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'dateRange') {
        if (value.start || value.end) count++;
      } else if (Array.isArray(value) && value.length > 0) {
        count++;
      }
    });
    return count;
  };

  // Custom styles for react-select
  const selectStyles = {
    control: (provided) => ({
      ...provided,
      minHeight: '38px',
      border: '1px solid #ced4da',
      borderRadius: '0.375rem',
      '&:hover': {
        borderColor: '#007bff'
      }
    }),
    multiValue: (provided) => ({
      ...provided,
      backgroundColor: '#ffc107',
      color: 'black'
    }),
    multiValueLabel: (provided) => ({
      ...provided,
      color: 'black'
    }),
    multiValueRemove: (provided) => ({
      ...provided,
      color: 'black',
      '&:hover': {
        backgroundColor: '#e0a800',
        color: 'black'
      }
    })
  };

  // Chart options
  const statusChartOptions = {
    ...getPieChartOptions('ePermit Status Distribution', 'Current status of all permits'),
    series: [{
      name: 'Permits',
      colorByPoint: true,
      data: chartData.status
    }]
  };

  const closureStatusChartOptions = {
    ...getPieChartOptions('Closure Status Distribution', 'Permit closure status analysis'),
    series: [{
      name: 'Permits',
      colorByPoint: true,
      data: chartData.closureStatus
    }]
  };

  const permitTypeChartOptions = {
    ...getPieChartOptions('Permit Type Distribution', 'Distribution by permit types'),
    series: [{
      name: 'Permits',
      colorByPoint: true,
      data: chartData.permitType
    }]
  };

  const levelChartOptions = {
    ...getPieChartOptions('Risk Level Distribution', 'Distribution by risk levels'),
    series: [{
      name: 'Permits',
      colorByPoint: true,
      data: chartData.level
    }]
  };

  const locationChartOptions = {
    ...getColumnChartOptions('Top 10 Locations by Permits', 'Locations with highest permit counts'),
    series: [{
      name: 'Permits',
      data: chartData.location,
      colorByPoint: true
    }]
  };

  const projectChartOptions = {
    ...getColumnChartOptions('Top 10 Projects by Permits', 'Projects with highest permit counts'),
    series: [{
      name: 'Permits',
      data: chartData.project,
      colorByPoint: true
    }]
  };

  const monthlyTrendsOptions = {
    ...getLineChartOptions('Monthly ePermit Trends', 'Permit applications over the last 12 months'),
    xAxis: {
      categories: chartData.monthly.categories
    },
    series: [{
      name: 'Permits',
      data: chartData.monthly.data,
      color: '#ffc107'
    }],
    tooltip: {
      valueSuffix: ' permits'
    }
  };

  const durationChartOptions = {
    ...getPieChartOptions('Permit Duration Analysis', 'Distribution of permit durations'),
    series: [{
      name: 'Permits',
      colorByPoint: true,
      data: chartData.duration
    }]
  };

  const approvalTimeChartOptions = {
    ...getPieChartOptions('Approval Time Analysis', `Average approval time: ${chartData.approvalTime.avgApprovalTime} hours`),
    series: [{
      name: 'Permits',
      colorByPoint: true,
      data: chartData.approvalTime.data
    }]
  };

  const cardStyle = {
    marginBottom: '20px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    border: 'none'
  };

  const cardHeaderStyle = {
    backgroundColor: '#f8f9fa',
    borderBottom: '1px solid #dee2e6',
    padding: '15px 20px',
    fontSize: '16px',
    fontWeight: '600',
    color: '#495057'
  };

  return (
    <div className="epermit-analytics">
      <div className="mb-4">
        <h2 className="text-warning">ePermit to Work Analytics Dashboard</h2>
        <p className="text-muted">Comprehensive analysis of permit data with interactive charts</p>
      </div>

      {/* Summary Statistics */}
      <Row className="mb-4">
        <Col lg={12}>
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Summary Statistics
            </Card.Header>
            <Card.Body className="summary-stats">
              <Row>
                <Col md={3} className="text-center">
                  <h3 className="text-warning">{filteredData.length}</h3>
                  <p className="text-muted">Total Permits</p>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="text-success">
                    {chartData.status.find(item => item.name === 'Approved')?.y || 0}
                  </h3>
                  <p className="text-muted">Approved Permits</p>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="text-info">
                    {chartData.status.find(item => item.name === 'Pending')?.y || 0}
                  </h3>
                  <p className="text-muted">Pending Permits</p>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="text-primary">
                    {chartData.approvalTime.avgApprovalTime}h
                  </h3>
                  <p className="text-muted">Avg Approval Time</p>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Filters Section */}
      <Card style={cardStyle} className="mb-4">
        <Card.Header 
          style={{...cardHeaderStyle, cursor: 'pointer'}} 
          onClick={() => setFiltersOpen(!filtersOpen)}
        >
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <Icon.Filter size={18} className="me-2" />
              <span>Filters</span>
              {hasActiveFilters() && (
                <span className="badge bg-warning text-dark ms-2 position-relative">
                  {getActiveFiltersCount()}
                  <span className="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle">
                    <span className="visually-hidden">Active filters</span>
                  </span>
                </span>
              )}
            </div>
            <div className="d-flex align-items-center">
              {hasActiveFilters() && (
                <Button 
                  variant="outline-secondary" 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    clearAllFilters();
                  }}
                  className="me-2"
                >
                  <Icon.X size={14} className="me-1" />
                  Clear All
                </Button>
              )}
              <Icon.ChevronDown 
                size={18} 
                className={`transition-transform ${filtersOpen ? 'rotate-180' : ''}`}
                style={{ 
                  transform: filtersOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s ease'
                }}
              />
            </div>
          </div>
        </Card.Header>
        <Collapse in={filtersOpen}>
          <Card.Body className="filters-section">
            <Row>
              <Col md={2} className="mb-3">
                <Form.Label>Status</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.status || []}
                  value={filters.status}
                  onChange={(selected) => handleFilterChange('status', selected)}
                  placeholder="Select status..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Closure Status</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.closureStatus || []}
                  value={filters.closureStatus}
                  onChange={(selected) => handleFilterChange('closureStatus', selected)}
                  placeholder="Select closure status..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Permit Type</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.permitType || []}
                  value={filters.permitType}
                  onChange={(selected) => handleFilterChange('permitType', selected)}
                  placeholder="Select permit type..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Risk Level</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.level || []}
                  value={filters.level}
                  onChange={(selected) => handleFilterChange('level', selected)}
                  placeholder="Select level..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Location</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.location || []}
                  value={filters.location}
                  onChange={(selected) => handleFilterChange('location', selected)}
                  placeholder="Select location..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Project</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.project || []}
                  value={filters.project}
                  onChange={(selected) => handleFilterChange('project', selected)}
                  placeholder="Select project..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Date From</Form.Label>
                <Calendar
                  value={filters.dateRange.start}
                  onChange={(e) => handleDateRangeChange('start', e.value)}
                  placeholder="Select start date"
                  dateFormat="dd/mm/yy"
                  showIcon
                  className="w-100"
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Date To</Form.Label>
                <Calendar
                  value={filters.dateRange.end}
                  onChange={(e) => handleDateRangeChange('end', e.value)}
                  placeholder="Select end date"
                  dateFormat="dd/mm/yy"
                  showIcon
                  className="w-100"
                />
              </Col>
            </Row>
            <Row>
              <Col>
                <div className="filter-summary">
                  <div className="text-muted small">
                    Showing <strong>{filteredData.length}</strong> of <strong>{data.length}</strong> permits
                    {hasActiveFilters() && (
                      <span className="text-warning"> ({getActiveFiltersCount()} filter{getActiveFiltersCount() !== 1 ? 's' : ''} applied)</span>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Collapse>
      </Card>

      <Row>
        {/* Status Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Status Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={statusChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Closure Status Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Closure Status Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={closureStatusChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Permit Type Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Permit Type Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={permitTypeChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Risk Level Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Risk Level Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={levelChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Monthly Trends */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Monthly Trends
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={monthlyTrendsOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Duration Analysis */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Permit Duration Analysis
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={durationChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Approval Time Analysis */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Approval Time Analysis
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={approvalTimeChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Top Locations */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Locations by Permits
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={locationChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Top Projects */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Projects by Permits
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={projectChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default EPermitAnalytics;
