// @ts-nocheck
import React, { useState, useRef } from 'react';

import $ from "jquery";
import { Modal, Button, Form, Accordion, OverlayTrigger, Tooltip } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
import Swal from 'sweetalert2';
import { Typeahead } from 'react-bootstrap-typeahead';
import { GMS1_URL, GET_TEAM_MEMBERS_RISK, RISK_WITH_ID_URL, HAZARDS_CATEGOTY, RISK_UPDATES, RISK_UPDATE_WITH_ID_URL } from "../constants";
import { useEffect } from 'react';
import Select from 'react-select'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector, useDispatch } from 'react-redux'

import API from '../services/API';
import Switch from "react-switch";
import { useHistory, useParams, useLocation } from 'react-router-dom';
import SignatureCanvas from 'react-signature-canvas'
import DatePicker from "react-datepicker";
import moment from 'moment';
import AWS from 'aws-sdk';


import "react-datepicker/dist/react-datepicker.css";
// @ts-ignore
import S3 from "react-aws-s3";
import { Buffer } from "buffer";
import pdfMake, { async } from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import Logo from '../../assets/images/logo.png'
import axios from 'axios';


// Register the fonts
pdfMake.vfs = pdfFonts.pdfMake.vfs;

Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
window.jQuery = $;
// @ts-ignore
window.$ = $;
const config = {
  bucketName: "sagt",
  region: "ap-southeast-1",
  accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY,
  secretAccessKey: process.env.REACT_APP_AWS_SECRET_KEY,
};
const customSwal = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-danger',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',

  },
  buttonsStyling: false
})

const s3 = new AWS.S3({
  accessKeyId: '********************',
  secretAccessKey: 'iuo0OyvIb5Fvwlq3t0uStZP9oUvmrLfggSj5YsOs',
});



const ViewRisk = (props) => {
  const history = useHistory()
  const description = useRef();
  const department = useRef();
  const location = useLocation()
  const title = useRef();
  const sign = useRef();
  const signRef = useRef();
  const sign1 = useRef();
  const params = useParams()
  const review = useRef();
  const change = useRef();
  const reasonchange = useRef();
  const reference = useRef();
  const initial = useRef();
  const approve = useRef();
  const user = useSelector((state) => state.login.user)

  const defaultMaterialTheme = createTheme();
  const [departActivity, setDepartActivity] = useState([])
  const [mdSign, setMdSign] = useState(false)
  const [selectedTypeDepart, setSelectedDepart] = useState([]);
  const [selectedTypeActivity, setSelectedActivity] = useState([]);
  const [depart, setDepart] = useState([])
  const [activity, setActivity] = useState([])
  const dispatch = useDispatch()
  const [mem, setMember] = useState('')
  const [likelyhood, setLikelyhood] = useState(0)
  const [personnel, setPersonnel] = useState(0)
  const [property, setProperty] = useState(0)
  const [environment, setEnvironment] = useState(0)
  const [serviceloss, setServiceLoss] = useState(0)
  const [likelyhood1, setLikelyhood1] = useState(0)
  const [personnel1, setPersonnel1] = useState(0)
  const [property1, setProperty1] = useState(0)
  const [environment1, setEnvironment1] = useState(0)
  const [serviceloss1, setServiceLoss1] = useState(0)
  const [RPN, setRPN] = useState(0)
  const [color, setColor] = useState('')
  const [mdShow, setMdShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [attachmentShow, setAttachmentShow] = useState(false);
  const [viewAttachment, setViewAttachment] = useState([])
  const [crew, setCrew] = useState([]);
  const [meet, setMeet] = useState([])
  const [pdf, setPdf] = useState([])
  const [loader, setLoader] = useState(true)
  const [update, setUpdate] = useState([])
  const [selectedAttenteesValue, setSelectedAttenteesValue] = useState([]);
  const [selectedTypeValue, setSelectedTypeValue] = useState([]);
  const [selectedLeaderValue, setSelectedLeaderValue] = useState([]);
  const [selectedMemberValue, setSelectedMemberValue] = useState([]);
  const [selectedAssignValue, setSelectedAssignValue] = useState({});
  const [dataUrl, setDataUrl] = useState(null);
  const [green, setGreen] = useState(false)


  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };
  const columns = [

    {
      field: 'date',
      title: 'Date',


    },
    {
      field: 'reasonreview',
      title: 'Reason for Review',


    },
    {
      field: 'changes',
      title: 'Changes',

      // formatter: (cell, row) => {

      //   return (
      //     <>{row.contractor.name}</>
      //   );
      // }

    },
    {
      field: 'reasonchange',
      title: 'Reason for Changes',


    },


    {
      field: 'initiatedBy',
      title: 'Initiated By',


    },
    {
      field: 'approvedBy',
      title: 'Approved By',


    },

    {
      field: 'reference',
      title: 'Reference',


    },


  ];
  const [task, setTask] = useState([
    [{ "type": 'textbox', "label": "Sub-Activity Description", "value": "" },
    { 'type': 'hazards', 'option': [{ 'allhazards': [] }, { 'selected': [] }] },
    { "type": 'textbox1', 'variant': 'consequence', 'option': [{ 'type': 'textarea', "label": '', 'value': '' }], 'button': true },

    {
      "type": "identified",
      "severity": [
        { "type": 'select1', "label": 'Personnel', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Property', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Environment', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Service loss', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
      ],
      "likelyhood": [
        { "type": 'select1', "label": 'Personnel', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Property', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Environment', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Service loss', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
      ],
      "risk": [

        { "type": 'personnel', "label": '', 'color': '' },
        { "type": 'property', "label": '', 'color': '' },
        { "type": 'environment', "label": '', 'color': '' },
        { "type": 'service', "label": '', 'color': '' },
      ]
    },
    { "type": 'textbox1', 'variant': 'current_control', 'option': [{ 'type': 'textarea', "label": 'Current Control', 'value': '' }], 'button': true },
    {
      "type": "initial",
      "severity": [
        { "type": 'select1', "label": 'Personnel', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Property', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Environment', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Service loss', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
      ],
      "likelyhood": [
        { "type": 'select1', "label": 'Personnel', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Property', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Environment', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
        { "type": 'select1', "label": 'Service loss', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
      ],
      "risk": [

        { "type": 'personnel', "label": '', 'color': '' },
        { "type": 'property', "label": '', 'color': '' },
        { "type": 'environment', "label": '', 'color': '' },
        { "type": 'service', "label": '', 'color': '' },
      ]
    },
    { "type": 'text', "label": 'is this Risk Level Acceptable ?' },

    {
      "type": 'checkbox1', 'variant': 'additional', 'values':
        [{ "label": "Yes ", "selected": true }, { "label": "No", "selected": false, }],
      'option': [
        {
          'type': 'textbox1', 'variant': 'additional_control', 'option': [
            {
              'label': 'Addition Control Proposed', 'type': 'textbox1', 'value': '', 'option': [
                { 'label': 'Responsibility', 'value': '', 'type': 'text' }, { 'label': 'Date', 'type': 'date', 'value': '' }]
            }]
        },
        { 'label': 'Add Control', 'type': 'button', 'value': 'control' },

        {
          "type": "rpn",
          "severity": [

            { "type": 'select1', "label": 'Personnel', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
            { "type": 'select1', "label": 'Property', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
            { "type": 'select1', "label": 'Environment', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
            { "type": 'select1', "label": 'Service loss', 'value': [{ label: '1', value: '1' }, { label: '2', value: '2' }, { label: '3', value: '3' }, { label: '4', value: '4' }, { label: '5', value: '5' }], 'selected': {}, 'val': 0 },
          ],
          "likelyhood": [
            { "type": 'select1', "label": 'Personnel', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
            { "type": 'select1', "label": 'Property', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
            { "type": 'select1', "label": 'Environment', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },
            { "type": 'select1', "label": 'Service loss', 'value': [{ label: 'A', value: 'A' }, { label: 'B', value: 'B' }, { label: 'C', value: 'C' }, { label: 'D', value: 'D' }, { label: 'E', value: 'E' }], 'selected': {}, 'val': 0 },

          ],
          "risk": [

            { "type": 'personnel', "label": '', 'color': '' },
            { "type": 'property', "label": '', 'color': '' },
            { "type": 'environment', "label": '', 'color': '' },
            { "type": 'service', "label": '', 'color': '' },
          ]
        },

      ]
    },









    ]
  ])
  const addTask1 = []

  useEffect(() => {

    getCrewList();
    getRisk();
    getUpdates();
    getWorkActivity();



  }, [])
  async function getImageDataUrl(imageUrl) {
    try {
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const blob = await response.blob();
      const reader = new FileReader();

      return new Promise((resolve, reject) => {
        reader.onload = () => {
          const dataUrl = reader.result;
          resolve(dataUrl);
        };
        reader.onerror = (error) => {
          reject(error);
        };

        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error converting image to Data URL:', error);
      throw error;
    }
  }


  const getWorkActivity = async () => {
    const uriString = { include: ["ghsTwos"] };

    const url = `${GMS1_URL}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {

      setDepartActivity(response.data)
      const depart = response.data.map(item => {
        return { value: item.id, label: item.name }
      })
      setDepart(depart)
    }
  };

  const userAgree = async () => {


    const filename = new Date().getTime() + "member_sign.png";

    if (!signRef.current.isEmpty()) {
      const ReactS3Client = new S3(config);

      await ReactS3Client.uploadFile(
        dataURItoFile(
          signRef.current.getTrimmedCanvas().toDataURL("image/png"),
          filename
        ),
        "uploads/risk_sign/" + filename
      )
        .then((data) => console.log(data))
        .catch((err) => console.error(err));
    }

    const m = meet



    const updatedCartItems = m.teamMemberInvolved.map(item => {
      if (item.id === mem.id) {
        return { ...item, sign: filename, date: moment().format('YYYY-MM-DD HH:mm') }; // Increment quantity
      }
      return item;
    });

    m.teamMemberInvolved = updatedCartItems

    setMeet(m)
    const response = await fetch(
      RISK_WITH_ID_URL(meet.id),
      {
        method: 'PATCH',
        body: JSON.stringify({

          teamMemberInvolved: meet.teamMemberInvolved


        }),
        headers: { "Content-type": "application/json; charset=UTF-8" }
      })

    if (response.ok) {



      customSwal2.fire(
        'Risk Assessment Updated!',
        '',
        'success'
      )
      setIsLoading(false)

    } else {
      //show error
      customSwal2.fire(
        'Please Try Again!',
        '',
        'error'
      )
      setIsLoading(false)
    }
    setMdSign(false)
    getRisk();

  }

  const getUpdates = async () => {
    const response = await API.get(RISK_UPDATE_WITH_ID_URL(location.state.id));
    if (response.status === 200) {

      setUpdate(response.data)


    }
  }
  const getCrewList = async () => {
    const response = await API.post(GET_TEAM_MEMBERS_RISK, {
      locationOneId: "",
      locationTwoId: "",
      locationThreeId: "",
      locationFourId: "",
    });
    if (response.status === 200) {
      let data = [];
      response.data.map((item) => {
        data.push({ label: item.firstName, value: item.id });
      });
      setCrew(data);
    }
  }

  const getRisk = async () => {
    const response = await API.get(RISK_WITH_ID_URL(location.state.id));
    if (response.status === 200) {

      setMeet(response.data)
      forPdfDownload(response.data)
      setLoader(false)
      const check = response.data.teamMemberInvolved.map(item => {
        return item && item.sign === 'No'

      })

      if (check.includes(true)) {

      } else {
        setGreen(true)
      }
    }
  }

  const forPdfDownload = (data) => {
    console.log(data)
    const convert = data.task.map((item, i) => {
      filterHazards(item).map(async (activeHazards) => {
        const urls = s3.getSignedUrl('getObject', {
          Bucket: 'mpower-s3',
          Key: 'uploads/hazards/' + activeHazards.image,
        });

        const imageDataUrl = await getImageDataUrl(urls);

        activeHazards.dataUrl = imageDataUrl



      })
      item[2].option.map(item1 => {
        item1.option.files.map(async item2 => {
          const urls = s3.getSignedUrl('getObject', {
            Bucket: 'sagt',
            Key: 'uploads/consequence/' + item2,
          });

          const imageDataUrl = await getImageDataUrl(urls);
          item1.option.dataURI = []
          item1.option.dataURI.push(imageDataUrl)

        })

      })
      item[3].option.map(item1 => {
        item1.option.files.map(async item2 => {
          const urls = s3.getSignedUrl('getObject', {
            Bucket: 'sagt',
            Key: 'uploads/current_control/' + item2,
          });

          const imageDataUrl = await getImageDataUrl(urls);
          item1.option.dataURI = []
          item1.option.dataURI.push(imageDataUrl)

        })

      })

      const consequence = item[2].option.map((item) => {
        return item.current_type;
      });
      const arr = removeDuplicate(consequence)
      console.log(arr)

      item[2].current = [];

      arr.map((currentType) => {
        const filteredOptions = item[2].option.filter(
          (item1) => item1.current_type === currentType
        );
        item[2].current.push({ value: currentType, data: filteredOptions });
      });



      return item

    })

    data.task = convert

    setPdf(data)
    // data.currentcontrol.map(item=>{
    //   item.map(item1=>{
    //     item1.files.map(async item2=>{

    //       const urls =  s3.getSignedUrl('getObject', {
    //         Bucket: 'sagt',
    //         Key: 'uploads/current_control/'+item2,
    //       });

    //      const imageDataUrl = await getImageDataUrl(urls);
    //                console.log(imageDataUrl);

    //     })
    //   })
    // })
    // data.task.map(item=>{
    //   item[1].option[1].selected.map(async item1=>{
    //     const urls =  s3.getSignedUrl('getObject', {
    //       Bucket: 'mpower-s3',
    //       Key: 'uploads/hazards/'+item1.image,
    //     });

    //    const imageDataUrl = await getImageDataUrl(urls);
    //      console.log(imageDataUrl);

    //   })

    // })
    // console.log(convert)
  }
  const removeDuplicate = (data) => {
    return data.filter((value, index) => data.indexOf(value) === index);
  }
  const onDeleteFileUpload = (j, i, k) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            // delete item1.option[k]
            item1.option.splice(k, 1);

            //     item1.option.map((item2, kk) => {
            //         if (kk === k) {
            //            console.log(item2)
            //         }
            //     })
          }
        });
      }

      return item;
    });
    setTask(text);
  };

  const dataURItoFile = (dataURI, filename) => {
    var byteString = atob(dataURI.split(",")[1]);
    // separate out the mime component
    var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
    // write the bytes of the string to an ArrayBuffer
    var ab = new ArrayBuffer(byteString.length);
    var dw = new DataView(ab);
    for (var i = 0; i < byteString.length; i++) {
      dw.setUint8(i, byteString.charCodeAt(i));
    }

    // write the ArrayBuffer to a blob, and you're done
    return new File([ab], filename, { type: mimeString });
  };
  const updateHandler = async () => {
    const ReactS3Client = new S3(config);
    const filename = new Date().getTime() + 'edit_sign.png';

    await ReactS3Client.uploadFile(
      dataURItoFile(sign1.current.getTrimmedCanvas().toDataURL("image/png"), filename),
      "uploads/risk_sign/" + filename
    )
      .then((data) => console.log(data))
      .catch((err) => console.error(err));
    // const formData = new FormData();
    // formData.append('file', dataURItoFile(sign1.current.getTrimmedCanvas().toDataURL("image/png"), 'captin_sign.png'))

    // const fileResponse = await fetch(FILE_URL, { method: 'POST', body: formData })
    // if (fileResponse.ok) {
    //     const data = await fileResponse.json();
    const response = await fetch(
      RISK_UPDATES,
      {
        method: 'POST',
        body: JSON.stringify({

          reasonreview: review.current.value,
          changes: change.current.value,
          reasonchange: reasonchange.current.value,
          initiatedBy: initial.current.value,
          approvedBy: approve.current.value,
          reference: reference.current.value,
          sign: filename,
          riskAssessmentId: location.state.id,


        }),
        headers: { "Content-type": "application/json; charset=UTF-8" }
      })
    if (response.ok) {



      createUserHandler();
    } else {
      //show error
      customSwal2.fire(
        'Please Try Again!',
        '',
        'error'
      )
      setIsLoading(false)
    }

  }

  const createUserHandler = async () => {
    // @ts-ignore
    setIsLoading(true)

    const response = await fetch(
      RISK_WITH_ID_URL(location.state.id),
      {
        method: 'PATCH',
        body: JSON.stringify({

          activity: meet.activity,
          type: meet.type,
          member: meet.member,
          department: meet.department,
          task: meet.task,
          teamMemberInvolved: meet.teamMemberInvolved


        }),
        headers: { "Content-type": "application/json; charset=UTF-8" }
      })

    if (response.ok) {



      customSwal2.fire(
        'Risk Assessment Updated!',
        '',
        'success'
      )
      setIsLoading(false)

    } else {
      //show error
      customSwal2.fire(
        'Please Try Again!',
        '',
        'error'
      )
      setIsLoading(false)
    }

    setMdShow(false)
    getUpdates();

  }

  const getCalculated = (val) => {
    var col = ''
    switch (val) {
      case 'A1':
        col = '#8cc14b'
        break;
      case 'A2':
        col = '#8cc14b'
        break;
      case 'A3':
        col = '#ffef00'
        break;
      case 'A4':
        col = '#ffef00'
        break;
      case 'A5':
        col = '#ffef00'
        break;
      case 'B1':
        col = '#8cc14b'
        break;
      case 'B2':
        col = '#ffef00'
        break;
      case 'B3':
        col = '#ffef00'
        break;
      case 'B4':
        col = '#ffba00'
        break;
      case 'B5':
        col = '#ffba00'
        break;
      case 'C1':
        col = '#ffef00'
        break;
      case 'C2':
        col = '#ffef00'
        break;
      case 'C3':
        col = '#ffba00'
        break;
      case 'C4':
        col = '#ffba00'
        break;
      case 'C5':
        col = '#ff1900'
        break;
      case 'D1':
        col = '#ffef00'
        break;
      case 'D2':
        col = '#ffba00'
        break;
      case 'D3':
        col = '#ffba00'
        break;
      case 'D4':
        col = '#ff1900'
        break;
      case 'D5':
        col = '#ff1900'
        break;
      case 'E1':
        col = '#ffef00'
        break;
      case 'E2':
        col = '#ffba00'
        break;
      case 'E3':
        col = '#ff1900'
        break;
      case 'E4':
        col = '#ff1900'
        break;
      case 'E5':
        col = '#ff1900'
        break;
      default:
        col = ''
        break;
    }



    return col
  }


  const handleTypeChange = (selectedOptions) => {

    const m = meet
    m.type = selectedOptions
    setMeet(m)
    setSelectedTypeValue(selectedOptions)
  }
  const handleLeaderChange = (selectedOptions) => {
    const m = meet
    m.leader = selectedOptions
    setMeet(m)
    setSelectedLeaderValue(selectedOptions)
  }
  const handleMemberChange = (selectedOptions) => {
    const m = meet
    m.member = selectedOptions
    // m.teamMemberInvolved
    // const existingObject = m.teamMemberInvolved.find(obj => obj.id === newObject.id);
    // const sign1 =selectedOptions.map((item)=>{
    //   return {label:item.label,sign:'No'}
    // })
    setMeet(m)
    setSelectedMemberValue(selectedOptions)
  }
  const handleChange = (selectedOptions) => {
    setSelectedAttenteesValue(selectedOptions)
  }
  const handleSelectChange = (e, j, i, k, name, variant) => {



    //  if (name === 'Personnel') {

    if (variant === 'severity') {
      var likelyhood = 0
      const t = meet;
      const text = t.task.map((item, ii) => {
        if (i === ii) {

          item.map((item1, jj) => {
            if (jj === j) {
              likelyhood = item1.likelyhood[k].val
              item1.severity[k].selected = e
              item1.severity[k].val = e.value
              item1.risk[k].label = likelyhood + '' + e.value
              item1.risk[k].color = getCalculated(likelyhood + '' + e.value)


            }


          })
        }

        return item
      })
      setTask(text)
    } else {
      var likelyhood = 0
      const t = meet;
      const text = t.task.map((item, ii) => {
        if (i === ii) {

          item.map((item1, jj) => {
            if (jj === j) {
              likelyhood = item1.severity[k].val
              item1.likelyhood[k].selected = e
              item1.likelyhood[k].val = e.value
              item1.risk[k].label = e.value + '' + likelyhood
              item1.risk[k].color = getCalculated(e.value + '' + likelyhood)


            }


          })
        }

        return item
      })
      setTask(text)
    }




  }
  const onDeleteFiles = async (j, i, k, f) => {
    const t = meet;
    let text = []

    text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map(async (item2, kk) => {
              if (kk === k) {
                item2.files.splice(f, 1)

              }
            });
          }
        });
      }

      return item;
    });






    setTask(text);
  }
  const handleSelectChange1 = (e, j, i, k, name, variant) => {

    if (variant === 'severity') {
      var likelyhood = 0
      const t = meet;
      const text = t.task.map((item, ii) => {
        if (i === ii) {
          likelyhood = item[j + 3].option[k].val
          item.map((item1, jj) => {
            if (jj === j) {
              item1.option[k].selected = e
              item1.option[k].val = e.value


            }
            if (item1.type === 'initialrisk') {
              item1.option[k].label = likelyhood + '' + e.value
              item1.option[k].color = getCalculated(likelyhood + '' + e.value)
            }

          })
        }

        return item
      })
      setTask(text)
    } else {
      var likelyhood = 0
      const t = meet
      const text = t.task.map((item, ii) => {
        if (i === ii) {
          likelyhood = item[j - 3].option[k].val
          item.map((item1, jj) => {
            if (jj === j) {
              item1.option[k].selected = e
              item1.option[k].val = e.value


            }
            if (item1.type === 'initialrisk') {
              item1.option[k].label = e.value + '' + likelyhood
              item1.option[k].color = getCalculated(e.value + '' + likelyhood)
            }

          })
        }

        return item
      })
      setTask(text)
    }

  }
  const handleSelectRPNChange = (e, j, i, k, l, name, variant) => {

    if (variant === 'severity') {


      var likelyhood = 0
      const t = meet
      const text = t.task.map((item, ii) => {
        if (i === ii) {
          item.map((item1, jj) => {
            if (jj === j) {

              item1.option.map((item2, kk) => {
                if (kk === k) {

                  item2.severity.map((item3, ll) => {
                    if (ll === l) {

                      likelyhood = item2.likelyhood[l].val
                      item3.selected = e
                      item3.val = e.value
                      item2.risk[l].label = likelyhood + '' + e.value
                      item2.risk[l].color = getCalculated(likelyhood + '' + e.value)



                    }


                  })
                }


              })

            }

          })
        }

        return item
      })


      setTask(text)
    } else {
      var likelyhood = 0
      const t = meet
      const text = t.task.map((item, ii) => {
        if (i === ii) {
          item.map((item1, jj) => {
            if (jj === j) {

              item1.option.map((item2, kk) => {
                if (kk === k) {

                  item2.likelyhood.map((item3, ll) => {
                    if (ll === l) {
                      likelyhood = item2.severity[l].val
                      item3.selected = e
                      item3.val = e.value
                      item2.risk[l].label = e.value + '' + likelyhood
                      item2.risk[l].color = getCalculated(e.value + '' + likelyhood)


                    }


                  })
                }



              })

            }

          })
        }

        return item
      })
      setTask(text)
    }

  }
  const addTask = async () => {

    const uriString = { include: ['hazards'] }

    const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
    const response = await API.get(url);
    if (response.status === 200) {
      response.data.sort((a, b) => (a.order > b.order) ? 1 : -1)
      const newT = addTask1.map((item1, j) => {
        if (item1.type === 'hazards') {

          item1.option[0].allhazards = response.data
        }
        return item1
      })
      const t = meet;
      t.additional.push('Yes')
      t.additionalDates.push([{ 'control': '', 'name': '', 'date': '' }])
      const text = t.task.push(newT)


      setTask(text)

    }



    // setTask((prev) => ([...prev, addTask1]))

  }
  const handleSelectInChange = (e, k, j, i) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.value = e
              }

            })
          }

        })
      }

      return item
    })
    setTask(text)
  }
  const onchangeSelectOpt = (e, k, j, i) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.selected = e
              }

            })
          }

        })
      }

      return item
    })
    setTask(text)
  }
  const onchangeSelect = (e, j, i) => {

    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.selected = e
          }

        })
      }

      return item
    })
    setTask(text)

  }
  const onClickButton1 = (e, j, i, k) => {

    e.preventDefault();
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {

            item1.option[k].option.push({ 'label': 'Addition Control Proposed', 'type': 'textbox1', 'value': '', 'option': [{ 'label': 'Responsibility', 'value': '', 'type': 'text' }, { 'label': 'Date', 'type': 'date', 'value': '' }] })


            // item[j - 1].option.push({ 'type': 'textbox1', 'option': [{ 'label': 'Addition Control Proposed', 'type': 'textbox1', 'value': '', 'option': [{ 'label': 'Responsibility', 'value': '', 'type': 'text' }, { 'label': 'Date', 'type': 'date', 'value': '' }] }] })

          }

        })
      }

      return item
    })
    t.additionalDates[i].push({ 'control': '', 'name': '', 'date': '' })
    setTask(text)

  }

  const onchangeBox1Text = (e, j, i, k, l) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.map((item3, ll) => {
                  if (ll === l) {
                    item3.value = e.target.value
                  }
                })
              }
            })

          }
        })
      }

      return item
    })
    t.additionalDates[i][l].control = e.target.value
    setTask(text)
  }
  const onDeleteTextBox = (j, i, k) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            // delete item1.option[k]
            item1.option.splice(k, 1)
            //     item1.option.map((item2, kk) => {
            //         if (kk === k) {
            //            console.log(item2)
            //         }
            //     })
          }

        })
      }

      return item
    })
    setTask(text)
  }
  const onDeleteTextBox1 = (j, i, k, l) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            // delete item1.option[k]

            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.splice(l, 1)
              }
            })
          }

        })
      }

      return item
    })
    t.additionalDates[i].splice(l, 1)
    setTask(text)
  }
  const onDeleteTask = (i) => {

    const t = task;
    const text = t.map((item, ii) => {
      if (i == ii) {
        t.splice(i, 1)
      }

      return item
    })

    setTask(text)
  }
  const onchangeBox2Text = (e, j, i, k, l, m) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.map((item3, ll) => {
                  if (ll === l) {
                    item3.option.map((item4, mm) => {
                      if (mm === m) {
                        item4.value = e.target.value
                      }
                    })
                  }
                })
              }
            })

          }
        })
      }

      return item
    })
    t.additionalDates[i][l].name = e.target.value
    setTask(text)
  }
  const handleDateChange = (e, j, i, k, l, m) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.map((item3, ll) => {
                  if (ll === l) {
                    item3.option.map((item4, mm) => {
                      if (mm === m) {
                        item4.value = e
                      }
                    })
                  }
                })
              }
            })

          }
        })
      }

      return item
    })
    t.additionalDates[i][l].date = e
    setTask(text)
  }
  const onClickButton = (e, j, i) => {


    e.preventDefault();
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {

        if (item[j].variant === 'consequence') {
          item[j].option.push({ "type": 'textarea', "label": '', 'value': '' })
        } else if (item[j].variant === 'current_control') {
          item[j].option.push({ "type": 'textarea', "label": '', 'value': '' })
        } else if (item[j].value === 'control') {

          item[j - 1].option.push({ 'type': 'textbox1', 'option': [{ 'label': 'Addition Control Proposed', 'type': 'textbox1', 'value': '', 'option': [{ 'label': 'Responsibility', 'value': '', 'type': 'text' }, { 'label': 'Date', 'type': 'date', 'value': '' }] }] })
        }
        // item.map((item1, jj) => {
        //     if (jj === j) {
        //         item1.selected = e
        //     }

        // })
      }

      return item
    })
    setTask(text)

  }
  const onchangeText = (e, j, i) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.value = e.target.value
          }

        })
      }

      return item
    })
    setTask(text)
  }
  const onchangeText1 = (e, j, i, k) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.value = e.target.value
              }
            })
          }

        })
      }

      return item
    })
    setTask(text)
  }

  const toggleButton = (e, j, i, v) => {
    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {


            item1.values.map((item2) => {
              item2.selected = false
            })
            item1.values[v].selected = true
            t.additional[i] = item1.values[v].label
          }

        })
      }

      return item
    })
    setTask(text)
  }

  const handleAssignChange = (e) => {
    const m = meet

    m.userId = e.value

    setMeet(m)
    setSelectedAssignValue(e)
  }
  const onChangeActivity = (e) => {

    const m = meet

    m.activity = e.target.value

    setMeet(m)
  }
  const onChangeDepartment = (e) => {

    const m = meet

    m.department = e.target.value

    setMeet(m)
  }
  const onchangefiles = async (e, j, i, k) => {
    const t = meet;
    let text = []
    const ReactS3Client = new S3(config);
    const filename = new Date().getTime() + e.target.files[0].name;

    await ReactS3Client.uploadFile(e.target.files[0]
      ,
      "uploads/current_control/" + filename
    )
      .then((data) => {
        text = t.task.map((item, ii) => {
          if (i === ii) {
            item.map((item1, jj) => {
              if (jj === j) {
                item1.option.map(async (item2, kk) => {
                  if (kk === k) {
                    item2.files.push(filename)

                  }
                });
              }
            });
          }

          return item;
        });


      })
      .catch((err) => console.error(err));





    setTask(text);
  }
  const onClickHazard = (e, i, j, c, h, ca, ha) => {


    const t = meet;
    const text = t.task.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option[0].allhazards.map((item2, k) => {
              if (k === c) {
                item2.hazards.map((item3, m) => {
                  if (m === h) {
                    if (item3.active === true) {
                      delete (item3.active)
                    } else {
                      item3.active = true
                    }

                  }
                })

              }
            })
          }
        })
      }
      return item
    })

    setTask(text)
  }
  const handleDepartChange = (option) => {
    const m = meet
    m.department = option
    setMeet(m)

    setSelectedDepart(option)
    setActivity([])
    const active = departActivity.filter(item => item.id === option.value);

    const final = active[0].ghsTwos.map(item => {
      return { label: item.name, value: item.id }
    })

    setActivity(final)

  }
  const handleActivityChange = (selectedOptions) => {
    const m = meet
    m.activity = selectedOptions
    setMeet(m)

    setSelectedActivity(selectedOptions)
  };

  const filterHazards = (item) => {

    const hazards = item[1].option[0].allhazards.flatMap((item1) => item1.hazards.filter((item2) => item2.active))

    return hazards

  }
  const generateLandPdf = () => {
    var dd = {
      content: [
        { image: Logo, style: 'logoImg', width: 50 },
        { text: 'Risk Assessment', style: 'header' },
        {
          //   layout: 'headerLineOnly',
          table: {
            // headers are automatically repeated if the table spans over multiple pages 770
            // you can declare how many rows should be treated as headers

            widths: ['*', '*', '*', '*'],

            body: [
              [
                { columns: [{ text: 'Department:', bold: true, }] },
                { columns: [{ text: pdf.department }] },
                { columns: [{ text: 'Work Activity:', bold: true, }] },
                { columns: [{ text: pdf.activity }] }


              ],
              [
                { columns: [{ text: 'Type:', bold: true, }] },
                { columns: [{ text: pdf.type.label }] },
                { columns: [{ text: 'Leader:', bold: true, }] },
                { columns: [{ text: pdf.captain }] }


              ],
              [
                { columns: [{ text: 'RA ID:', bold: true, }] },
                { columns: [{ text: pdf.meetid }] },
                { columns: [{ text: 'Date of first Release:', bold: true, }] },
                { columns: [{ text: pdf.date }] }


              ],
              [
                { columns: [{ text: 'Updated Date:', bold: true, }] },
                { columns: [{ text: pdf.updatedDate }] },
                { columns: [{ text: 'Date of Next Review Date:', bold: true, }] },
                { columns: [{ text: pdf.nextdate }] }


              ],
              [
                { columns: [{ text: 'Team Members:', bold: true }], },
                {
                  columns: [{
                    text: pdf.teamMemberInvolved.map((item) => {
                      return item && item.label + " - "
                    })
                  }], colSpan: 3
                },
                {},
                {}
              ]


            ]
          }
        }, { text: '', margin: [0, 2] },

        {
          //   layout: 'headerLineOnly',
          table: {
            // headers are automatically repeated if the table spans over multiple pages 770

            headerRows: 2,


            body: [
              [
                { text: 'HAZARD IDENTIFICATION', bold: true, alignment: 'center', colSpan: 4 }, {}, {}, {},
                { text: 'RISK EVALUATION', bold: true, alignment: 'center', colSpan: 4 }, {}, {}, {},
                { text: 'RISK CONTROL', bold: true, alignment: 'center', colSpan: 7 }, {}, {}, {}, {}, {}, {}

              ],
              [
                { text: 'Ref', bold: true, alignment: 'center' },
                { text: 'Sub-Activity', bold: true, alignment: 'center', },
                { text: 'Hazard', bold: true, alignment: 'center', },
                { text: 'Consequence', bold: true, alignment: 'center', },
                { text: 'Current Controls', bold: true, alignment: 'center' },
                { text: 'S', bold: true, alignment: 'center', alignment: 'center' },
                { text: 'L', bold: true, alignment: 'center', alignment: 'center' },
                { text: 'RPN', bold: true, alignment: 'center', },
                { text: 'Additional Controls', bold: true, alignment: 'center' },
                { text: 'S', bold: true, alignment: 'center', alignment: 'center' },
                { text: 'L', bold: true, alignment: 'center', alignment: 'center' },
                { text: 'RPN', bold: true, alignment: 'center' },
                { text: 'Implementation Person', bold: true, alignment: 'center' },
                { text: 'Due Date', bold: true, alignment: 'center', },
                { text: 'Remarks', bold: true, alignment: 'center', },

              ], ...renderRows(pdf.task)


            ]
          }
        },
        { text: 'Recommendations of RA Team ', style: 'subheader' },
        {
          //   layout: 'headerLineOnly',
          table: {
            widths: ['*'],
            body: [
              [
                { columns: [{ text: '1.' + pdf.recommendation.label, fillColor: pdf.recommendation.value === "0" ? "#8cc14b" : pdf.recommendation.value === "1" ? "#ffef00" : "#ff1900" }] },
              ]
            ],

          },
          layout: 'noBorders'
        },
        {
          //   layout: 'headerLineOnly',
          table: {
            widths: ['*'],
            body: [
              [
                { columns: [{ text: '2.' + pdf.additionRecommendation.label }] },
              ]
            ],

          },
          layout: 'noBorders'
        },
        {
          //   layout: 'headerLineOnly',
          table: {
            widths: ['*'],
            body: [
              [
                { columns: [{ text: '3.' + pdf.thirdAdd }] },
              ]
            ],

          },
          layout: 'noBorders'
        },

      ],
      pageSize: 'A3',
      pageOrientation: 'landscape',
      pageMargins: [15, 15, 15, 20],
      footer: function (currentPage, pageCount) { return { alignment: 'right', margin: [5, 0], text: 'Page ' + currentPage.toString() + ' of ' + pageCount + ' pages(s)' } },
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          margin: [0, 0, 0, 10],
          alignment: 'center'
        },
        subheader: {
          fontSize: 16,
          bold: true,
          margin: [0, 10, 0, 5]
        },
        tableExample: {
          margin: [0, 5, 0, 15]
        },
        tableHeader: {
          bold: true,
          fontSize: 13,
          color: 'black'
        },
        logoImg: {
          alignment: 'center',
          width: 60
        },
      },
      defaultStyle: {
        // alignment: 'justify'
      }

    }


    pdfMake.createPdf(dd).download(`${meet.meetid + '-' + moment().format('YYYYMMDD HH:mm')}.pdf`);

  }
  function renderRows(row) {

    let result = []
    row.forEach((item, index) => {
      result.push(
        [
          { text: index + 1, alignment: 'center' },
          { text: item[0].value, alignment: 'center' },
          {
            ol:
              item[1].option[1].selected.map(it => {
                return [it.name]
              })

          },

          item[2].current.map((it) => (
            [{ text: it.value, bold: true, decoration: 'underline' },
            { ol: it.data.map((innerIt) => ({ text: innerIt.value })) }]

          ))
          ,
          {
            ol: item[3].option.map(it => {
              return [it.value]
            })
          },
          { text: item[4].severity[0].val, alignment: 'center' },
          { text: item[4].likelyhood[0].val, alignment: 'center' },
          { text: item[4].risk[0].label, alignment: 'center', fillColor: item[4].risk[0].color },

          {
            ol: item[6].option[0].option.map(it => {
              if (it.value === "") {
                return "Nil"
              } else {
                return [it.value]
              }
            })
          },
          { text: item[6].option[2].severity[0].val === 0 ? "Nil" : item[6].option[2].severity[0].val, alignment: 'center' },
          { text: item[6].option[2].likelyhood[0].val === 0 ? "Nil" : item[6].option[2].likelyhood[0].val, alignment: 'center' },
          { text: item[6].option[2].risk[0].label === "" ? 'Nil' : item[6].option[2].risk[0].label, alignment: 'center', fillColor: item[6].option[2].risk[0].label === "" ? "" : item[6].option[2].risk[0].color },
          {
            ol: pdf.additionalDates[index].map(it => {
              if (it.name === "") {
                return "Nil"
              } else {
                if (it.name.label) {
                  return [it.name.label]
                } else {
                  return [it.name]
                }
              }
            })
          },
          {
            ol: pdf.additionalDates[index].map(it => {
              if (it.date === "") {
                return "Nil"
              } else {
                return [moment(it.date).format('YYYY-MM-DD')]
              }

            })
          },
          { text: '', alignment: 'center' },

        ]
      )
    })
    return result
  }

  const generatePdf = async () => {

    const documentDefinition = {
      pageMargins: [40, 40, 40, 25],
      footer: function (currentPage, pageCount) {
        return {
          columns: [
            { text: pdf.meetid, alignment: 'left', margin: [10, 0], fontSize: 8 },
            { text: moment().format('YYYY-MM-DD HH:mm'), alignment: 'center', margin: [10, 0], fontSize: 8 },
            { text: 'Page ' + currentPage.toString() + ' of ' + pageCount, alignment: 'right', margin: [0, 0, 10, 0], fontSize: 8 }
          ],
          margin: [40, 0]
        };
      },
      header: (currentPage, pageCount) => {
        if (currentPage === 1) {
          return null; // No header on the first page
        }

        return [

          { image: Logo, width: 30, height: 30, alignment: 'right' }

        ];
      },
      content: [
        { image: Logo, style: 'logoImg' },
        { text: 'Risk Assessment', style: 'header' },
        { text: pdf.meetid + ' : ' + pdf.activity, style: 'subheader' },
        {
          style: 'tableExample',
          table: {
            widths: [240, 240],
            body: [
              [{ text: 'Initiated By', margin: [2, 2, 2, 2] }, { text: pdf.department }],
              [{ text: 'Type', margin: [2, 2, 2, 2] }, { text: pdf.type.label }],
              [{ text: 'Team Leader', margin: [2, 2, 2, 2] }, { text: pdf.captain }],
              [{ text: 'Date of first Release', margin: [2, 2, 2, 2], }, { text: pdf.date }],
              [{ text: 'Date of Update', margin: [2, 2, 2, 2], }, { text: meet.updatedDate, }],
              [{ text: 'Date of Next Review', margin: [2, 2, 2, 2], }, { text: pdf.nextdate }]
            ]
          },



        },
        { text: '', pageBreak: 'after' },

        pdf.task.map((item, i) => {
          return ([

            // { text: (i + 1) + '.' + item[0].value, style: 'subheader' },
            {
              text: 'Sub Activity ' + (i + 1) + ': ' + item[0].value, alignment: 'left', fontSize: 14,
              bold: true,
              margin: [0, 20, 0, 0]
            },
            {
              style: 'tableExample',
              table: {
                widths: [518],
                body: [
                  [{

                    fillColor: '#dddddd',
                    text: 'Hazards identified by the Risk Assessment Team',
                    alignment: 'center'
                  },

                  ],

                ],



              },
              layout: 'noBorders'
            },



            {
              style: 'tableExample',
              table: {
                widths: [250, 250],
                body: filterHazards(item).map((activeHazards) =>

                  [

                    { text: activeHazards.name, style: 'hazardName' },
                    {
                      image: activeHazards.dataUrl, style: 'hazardlogoImg', fit: [40, 40],
                    },
                  ]

                )
              },

            },

            {
              style: 'tableExample',
              table: {
                widths: [518],
                body: [
                  [{

                    fillColor: '#dddddd',
                    text: 'Potential Consequences',
                    alignment: 'center'
                  },

                  ],

                ],



              },
              layout: 'noBorders'
            },
            item[2].option.map((item1, i) => {
              return [
                { text: (i + 1) + '.' + item1.value },
                ...item1.option.dataURI ? [

                  {
                    style: 'tableExample',
                    table: {
                      widths: [518],
                      body: [
                        [{


                          width: 'auto',
                          image: item1.option.dataURI[0], // Replace with actual image data URL
                          fit: [100, 100],
                          margin: [10, 10, 10, 10],
                          alignment: 'center'
                        },

                        ],

                      ],
                    },
                    layout: 'noBorders'
                  }

                ] : []
              ]
            }),

            {
              style: 'tableExample',
              table: {
                widths: [518],
                body: [
                  [{

                    fillColor: '#dddddd',
                    text: 'Current Controls',
                    alignment: 'center'
                  },

                  ],

                ],



              },
              layout: 'noBorders'
            },
            item[3].option.map((item1, i) => {
              return [
                {
                  text: (i + 1) + '.' + item1.current_type + ' - ' + item1.value
                },
                ...item1.option.dataURI ? [
                  {
                    style: 'tableExample',
                    table: {
                      widths: [518],
                      body: [
                        [{


                          width: 'auto',
                          image: item1.option.dataURI[0], // Replace with actual image data URL
                          fit: [140, 140],
                          margin: [10, 10, 10, 10],
                          alignment: 'center'
                        },

                        ],

                      ],
                    },
                    layout: 'noBorders'
                  }
                ] : []

              ]
            })
            ,

            {
              style: 'tableExample',
              table: {
                widths: [518],
                body: [
                  [{

                    fillColor: '#dddddd',
                    text: 'Sub Activity Risk Assessment',
                    alignment: 'center'
                  },

                  ],

                ],



              },
              layout: 'noBorders'
            },
            {
              style: 'tableExample',
              table: {
                widths: [162, 162, 162],
                body: [
                  [{

                    fillColor: '#F2F2F2',
                    text: 'Severity',
                    alignment: 'center'
                  },
                  {

                    fillColor: '#F2F2F2',
                    text: 'Likelyhood',
                    alignment: 'center'
                  },
                  {

                    fillColor: '#F2F2F2',
                    text: 'Risk',
                    alignment: 'center'
                  }],
                  [{ text: item[4].severity[0].selected.label, alignment: 'center' }, { text: item[4].likelyhood[0].selected.label, alignment: 'center' }, { text: item[4].risk[0].label + '-' + (item[4].risk[0].color === '#8cc14b' ? 'Low' : item[4].risk[0].color === '#ffef00' ? 'Medium' : item[4].risk[0].color === '#ff1900' ? 'High' : ''), fillColor: item[4].risk[0].color, alignment: 'center' }]
                ],



              },

            },


            {
              style: 'tableYes',
              table: {
                widths: [255, 255],
                body: [
                  [{ text: `Is the Risk Level for Sub Activity ${i + 1} acceptable ?`, fillColor: '#dddddd' },

                  (item[6].values[0].selected) ? { text: 'Yes', width: 100, alignment: 'center', color: '#1A3298' } : { text: 'No', width: 100, alignment: 'center', color: '#ff1900' }],

                ]
              },
              layout: 'noBorders'


            },

            item[6].values[1].selected ? [
              { text: '', pageBreak: 'after' },
              {
                style: 'tableExample',
                table: {
                  widths: [518],
                  body: [
                    [{

                      fillColor: '#dddddd',
                      text: 'Addition Control Proposed',
                      alignment: 'center'
                    },

                    ],

                  ],



                },
                layout: 'noBorders'
              },

              item[6].option[0].option.map((tem, i) => {
                return ([
                  { text: (i + 1) + '.' + tem.current_type + ' - ' + tem.value, margin: [0, 10, 0, 10] },
                  {
                    columns: [
                      {
                        text: 'Responsibility :',
                        margin: [0, 10, 0, 10]
                      },
                      {
                        text: tem.option[0].value,
                        margin: [0, 10, 0, 10]
                      }
                    ]
                  },
                  {
                    columns: [
                      {
                        text: 'Date :',
                        margin: [0, 10, 0, 10]
                      },
                      {
                        text: moment(tem.option[1].value).format('YYYY-MM-DD HH:mm'),
                        margin: [0, 10, 0, 10]
                      }
                    ]
                  }
                ])
              }),

              {
                style: 'tableExample',
                table: {
                  widths: [518],
                  body: [
                    [{

                      fillColor: '#dddddd',
                      text: 'Residual Risk Assessment',
                      alignment: 'center'
                    },

                    ],

                  ],



                },
                layout: 'noBorders'
              },
              {
                style: 'tableExample',
                table: {
                  widths: [162, 162, 162],
                  body: [
                    [{

                      fillColor: '#F2F2F2',
                      text: 'Severity',
                      alignment: 'center'
                    },
                    {

                      fillColor: '#F2F2F2',
                      text: 'Likelyhood',
                      alignment: 'center'
                    },
                    {

                      fillColor: '#F2F2F2',
                      text: 'Risk',
                      alignment: 'center'
                    }],
                    [{ text: item[6].option[2].severity[0].selected.label, alignment: 'center' }, { text: item[6].option[2].likelyhood[0].selected.label, alignment: 'center' }, { text: item[6].option[2].risk[0].label + '-' + (item[6].option[2].risk[0].color === '#8cc14b' ? 'Low' : item[6].option[2].risk[0].color === '#ffef00' ? 'Medium' : item[6].option[2].risk[0].color === '#ff1900' ? 'High' : ''), fillColor: item[6].option[2].risk[0].color, alignment: 'center' }]
                  ],



                },

              },


            ]

              : '',

            // {
            //   canvas: [
            //     {
            //       type: 'line',
            //       x1: 0,
            //       y1: 0,
            //       x2: 516,
            //       y2: 0,
            //       lineWidth: 1,
            //       lineColor: 'black',

            //     }
            //   ]     // Enable rendering of HTML
            // },
            { text: '', pageBreak: 'after' },
          ]
          )


        },

        ),
        update.length !== 0 ? [

          {
            style: 'tableExample',
            table: {
              widths: [518],
              body: [
                [{

                  fillColor: '#dddddd',
                  text: 'Reviews,Changes and Updates',
                  alignment: 'center',
                  padding: [10, 10, 10, 10]
                },

                ],

              ],



            },
            layout: 'noBorders'
          },
          {
            style: 'tableExample',
            table: {
              widths: ['*', '*', '*', '*'],
              body: [
                [
                  { text: 'Date', style: 'tableHeader', layout: 'headerLayout' },
                  { text: 'Reason for Review', style: 'tableHeader', layout: 'headerLayout' },
                  { text: 'Changes', style: 'tableHeader', layout: 'headerLayout' },
                  { text: 'Approved By', style: 'tableHeader', layout: 'headerLayout' },
                  // { text: 'I confirm that the following team members were invovled in the development of this risk assessment', style: 'tableHeader' }
                ],

                ...update.map((m) => [
                  { text: m.date },
                  { text: m.reasonreview },
                  { text: m.changes },
                  { text: m.approvedBy }

                ]),


              ]
            },
          },
          { text: '', pageBreak: 'after' },
        ] : '',
        {
          style: 'tableExample',
          table: {
            widths: [518],
            body: [
              [{

                fillColor: '#dddddd',
                text: 'Overall recommendation of the RA Team',
                alignment: 'center',
                padding: [10, 10, 10, 10]
              },

              ],

            ],



          },
          layout: 'noBorders'
        },
        {
          style: 'tableExample',
          table: {
            widths: [518],
            body: [
              [{


                text: pdf.recommendation.label,
                alignment: 'center',
                padding: [10, 10, 10, 10]
              },

              ],

            ],



          },
          layout: 'noBorders'
        },

        {
          style: 'tableExample',
          table: {
            widths: [518],
            body: [
              [{

                fillColor: '#dddddd',
                text: `This Risk Assessment is generated electronically, accurate as of ${moment().format('YYYY-MM-DD HH:mm')} and does not require a physical signature. The risk assessment reflects the professional judgement of the Team Leader and Members listed below. `,
                alignment: 'center',
                padding: [10, 10, 10, 10]
              },

              ],

            ],



          },
          layout: 'noBorders'
        },
        {
          style: 'tableExample',
          table: {
            widths: ['*', '*', '*', '*'],
            body: [
              [
                { text: 'Name', style: 'tableHeader', layout: 'headerLayout' },
                { text: 'Department', style: 'tableHeader', layout: 'headerLayout' },
                { text: 'Role', style: 'tableHeader', layout: 'headerLayout' },
                { text: 'Date', style: 'tableHeader', layout: 'headerLayout' },
                // { text: 'I confirm that the following team members were invovled in the development of this risk assessment', style: 'tableHeader' }
              ],
              [
                { text: meet.captain },
                {},
                { text: 'Leader' },
                { text: meet.date }
              ],
              ...meet.teamMemberInvolved.map((item) => [
                { text: item.label },
                {},
                { text: 'Member' },
                { text: item.date }

              ]),


            ]
          },
        }, {
          text: 'Feedback, if any, on the Risk Assessment can be directed to the RA Team Leader.'
        },



      ],

      styles: {
        tableHeader: {
          bold: true,
          fontSize: 12,
          fillColor: '#F2F2F2',
          alignment: 'center'
        },
        logoImg: {
          alignment: 'center',
          width: 100
        },
        hazardName: {
          alignment: 'center',
          verticalAlignment: 'middle',
          margin: [0, 10, 0, 10]
        },
        hazardlogoImg: {
          height: 50,
          width: 50,
          alignment: 'center',
          verticalAlignment: 'middle'
        },
        header: {
          fontSize: 14,
          bold: true,
          margin: [0, 20, 0, 10],
          alignment: 'center'
        },
        subheader: {
          fontSize: 13,
          bold: true,
          margin: [0, 10, 0, 2]
        },
        subsubheader: {
          fontSize: 12,
          bold: true,
          margin: [0, 20, 0, 15],
          alignment: 'center'
        },
        tableYes: {
          margin: [0, 20, 0, 20],
          alignment: 'center'
        },
        tableExample: {
          margin: [0, 10, 0, 10],
        }
      },
      defaultStyle: {
        // alignment: 'justify'

        lineHeight: 1.2
      },
      layout: {
        headerLayout: {
          vLineWidth: () => 1,
          hAlign: 'center',
          paddingTop: () => 5,
          paddingBottom: () => 5
        },
      },

    }
    console.log(documentDefinition)
    pdfMake.createPdf(documentDefinition).download(`${meet.meetid + '-' + moment().format('YYYYMMDD HH:mm')}.pdf`);
  }
  const checkAllUsers = () => {

    const check = meet.teamMemberInvolved.map(item => {
      return item && item.sign === 'No'

    })

    if (check.includes(true)) {
      customSwal2.fire(
        'Printing disabled',
        'The RA needs to be affirmed by all team members before it can be printed!',
        'warning'
      )

    } else {
      generateLandPdf()
    }
  }
  return (
    <>
      {loader === false ?
        <div>

          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-body">

                  <h4 className="card-title">View RA </h4>
                  <div className='col-12' style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {/* <i className="fa fa-download " style={{ fontSize: 30, color: '#d62828', }} onClick={() => generatePdf()}></i> */}
                    <i className="fa fa-download" style={{ fontSize: 30, borderRadius: 50, boxShadow: '0px 0px 10px 3px #dadada', padding: 10, cursor: 'pointer', color: green ? 'green' : '#d62828', }} onClick={() => checkAllUsers()}></i>
                  </div>
                  <div className="row">
                    <div className="col-12">

                      <form className="forms">
                        <div className="col-6">
                          <div className="form-group required">
                            <label htmlFor="user_category">Type of RA</label>
                            <h4>{meet.type.label}</h4>

                            {/* <Select
                              labelKey="label"
                              id="user_description"
                              onChange={handleTypeChange}
                              options={[
                                { label: "Generic RA", value: "Generic RA" },
                                {
                                  label: "Task Specific",
                                  value: "Task Specific",
                                },
                              ]}
                              value={meet.type}
                              placeholder="Type..."
                            /> */}
                          </div>
                        </div>

                        <div className="row">
                          <div className="col-6">
                            <div className="form-group required">
                              <label htmlFor="user_name">
                                Department
                              </label>
                              <h4>{meet.department}</h4>
                              {/* <Select
                                  labelKey="label"
                                  id="user_description"
                                  onChange={handleDepartChange}
                                  options={depart}
                                  placeholder="Type..."
                                  defaultValue={{ 'label': meet.department, 'value': meet.department }}
                                /> */}
                            </div>
                          </div>

                          <div className="col-6">
                            <div className="form-group required">
                              <label htmlFor="user_name">Process / Work Activity</label>
                              <h4>{meet.activity}</h4>
                              {/* <Select
                                  labelKey="label"
                                  id="user_description"
                                  onChange={handleActivityChange}
                                  options={activity}
                                  placeholder="Type..."
                                  defaultValue={{ 'label': meet.activity, 'value': meet.activity }}
                                /> */}
                              {/* <textarea className='form-control' ref={department} id="user_name"></textarea> */}
                            </div>
                          </div>
                        </div>




                        <div className="row">
                          <div className="col-12">
                            <div className="form-group required">
                              <label htmlFor="user_category">
                                RA Team Members
                              </label>
                              <ol>
                                {meet.member.map(item => {
                                  return (
                                    <li><h4>{item.label}</h4></li>
                                  )
                                })}
                              </ol>
                              {/* <Select
                                labelKey="label"
                                id="user_description"
                               
                                options={crew}
                                isMulti={true}
                                placeholder="Choose Members.."
                                value={meet.member}
                              /> */}
                            </div>
                          </div>
                        </div>
                      </form>
                      {/* {console.log(task)} */}
                      <Accordion>

                        <div className='tasks'>
                          {meet.task.map((item1, i) => {
                            return (
                              <Accordion.Item
                                eventKey={i}
                                className="mt-3"
                                style={{
                                  boxShadow: "0px 1px 15px 3px #bcbfc452",
                                  position: "relative",
                                }}
                              >
                                {/* <i className='mdi mdi-delete' onClick={() => onDeleteTask(i)} style={{ position: 'absolute', top: -12, right: -11, fontSize: '25px', color: 'red', zIndex: '999' }}></i> */}
                                <Accordion.Header>
                                  {"Sub Activity " +
                                    (i + 1) +
                                    " : " +
                                    item1[0].value}
                                </Accordion.Header>
                                <Accordion.Body>
                                  <div
                                    className="col-12 add-task"
                                    style={{
                                      padding: 40,
                                      marginTop: 20,
                                      boxShadow:
                                        "rgb(0 0 0 / 20%) 0px 0px 10px 0px",
                                    }}
                                  >
                                    {/* <h3 style={{ textAlign: 'center' }}></h3> */}
                                    {item1.map((item, j) => {
                                      if (item.type === "textbox") {
                                        return (
                                          <div className="form-group required">
                                            <label htmlFor="user_name">
                                              {item.label}
                                            </label>
                                            <h5>{item.value}</h5>
                                            {/* <Form.Control
                                              type="text"
                                              onChange={(e) =>
                                                onchangeText(e, j, i)
                                              }
                                              value=
                                            /> */}

                                            {item.option ?
                                              <div className="form-group">
                                                {item.option.images.length !== 0 && (
                                                  <div
                                                    className="row mt-3"
                                                    style={{
                                                      padding: 10,
                                                      border: '1px dashed',
                                                      borderRadius: 10

                                                    }}
                                                  >
                                                    {item.option.images.map(
                                                      (files, f) => {
                                                        return (
                                                          <div className="col-4">
                                                            <div
                                                              className=" boxShadow p-3 "
                                                              style={{
                                                                position:
                                                                  "relative",
                                                                height: 100


                                                              }}
                                                            >
                                                              <img
                                                                style={{

                                                                  maxHeight:
                                                                    "100%",
                                                                  maxWidth: "100%"
                                                                }}
                                                                src={
                                                                  "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/activity/" +
                                                                  files.name
                                                                }
                                                                alt="test"
                                                              />

                                                            </div>
                                                          </div>
                                                        );
                                                      }
                                                    )}
                                                  </div>
                                                )}
                                              </div> : ''
                                            }
                                          </div>
                                        );
                                      } else if (item.variant === "consequence") {
                                        return (
                                          <div
                                            className="row mb-5"
                                            style={{
                                              padding: 20,
                                              boxShadow:
                                                "0px 0px 10px 4px #0000001f",
                                            }}
                                          >
                                            <div
                                              className="form-group mt-5 mb-3"
                                              style={{ textAlign: "center" }}
                                            >
                                              <label
                                                htmlFor="user_name"
                                                style={{ fontSize: 20 }}
                                              >
                                                Consequence
                                              </label>
                                            </div>
                                            <div
                                              className="form-group mt-3"
                                              style={{ textAlign: "center" }}
                                            >
                                              <label
                                                htmlFor="user_name"
                                                style={{ fontSize: 15 }}
                                              >
                                                Identify the potential consequence on Safety, Environment, Financial, Security and Community / Brand Exposure due to presence of above hazards.
                                              </label>
                                            </div>

                                            <div className="textbox1">
                                              {item.option.map((opt, k) => {
                                                return (
                                                  <div className="form-group">
                                                    <h4 htmlFor="user_name">
                                                      {k + 1}{"."} {opt.current_type} {'-'} {opt.value}
                                                    </h4>
                                                    {/* <Form.Control
                                                      type="text"
                                                      onChange={(e) =>
                                                        onchangeText1(e, j, i, k)
                                                      }
                                                      value={opt.value}
                                                    />
                                                    <i
                                                      className="mdi mdi-delete"
                                                      onClick={() =>
                                                        onDeleteTextBox(j, i, k)
                                                      }
                                                    ></i> */}
                                                    {opt.option.type === 'file' ?
                                                      <div className="form-group">
                                                        {opt.option.files.length !== 0 && (
                                                          <div
                                                            className="row"
                                                            style={{

                                                              padding: 10,
                                                            }}
                                                          >
                                                            {opt.option.files.map(
                                                              (files, f) => {
                                                                return (
                                                                  <div
                                                                    className="col-12 d-flex align-items-center justify-content-center"
                                                                    style={{
                                                                      position:
                                                                        "relative",

                                                                    }}
                                                                  >
                                                                    <img
                                                                      style={{
                                                                        maxWidth:
                                                                          "100%",
                                                                        maxHeight:
                                                                          "100%",
                                                                      }}
                                                                      src={
                                                                        "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/consequence/" +
                                                                        files
                                                                      }
                                                                      alt="test"
                                                                    />

                                                                  </div>
                                                                );
                                                              }
                                                            )}
                                                          </div>
                                                        )}

                                                      </div>

                                                      : ''}
                                                  </div>
                                                );
                                              })}

                                            </div>
                                            {/* <div
                                              className="form-group"
                                              style={{ textAlign: "center" }}
                                            >
                                              <button
                                                className="btn btn-primary"
                                                onClick={(e) =>
                                                  onClickButton(e, j, i)
                                                }
                                              >
                                                <i className="mdi mdi-plus"></i>
                                              </button>
                                            </div> */}
                                          </div>
                                        );
                                      } else if (
                                        item.variant === "current_control"
                                      ) {
                                        return (
                                          <div
                                            className="row mb-5 mt-5"
                                            style={{
                                              padding: 20,
                                              boxShadow:
                                                "0px 0px 10px 4px #0000001f",
                                            }}
                                          >
                                            <div
                                              className="form-group mt-5 mb-3"
                                              style={{ textAlign: "center" }}
                                            >
                                              <label
                                                htmlFor="user_name"
                                                style={{ fontSize: 20 }}
                                              >
                                                Current Controls
                                              </label>
                                            </div>
                                            <div
                                              className="form-group mt-1 mb-3"
                                              style={{ textAlign: "center" }}
                                            >
                                              <label
                                                htmlFor="user_name"
                                                style={{ fontSize: 15 }}
                                              >
                                                Existing measures in place to
                                                mitigate or manage the above
                                                identified hazards and potential
                                                consequences.
                                              </label>
                                            </div>

                                            <div className="textbox1">
                                              {item.option.map((opt, k) => {

                                                if (opt.type === "textarea") {
                                                  return (
                                                    <div className="form-group">
                                                      <h4 htmlFor="user_name">{k + 1}{"."} {opt.current_type} {'-'} {opt.value}</h4>
                                                      {/* <Form.Control
                                                        type="text"
                                                        onChange={(e) =>
                                                          onchangeText1(
                                                            e,
                                                            j,
                                                            i,
                                                            k
                                                          )
                                                        }
                                                        value={opt.value}
                                                      />
                                                      <i
                                                        className="mdi mdi-delete"
                                                        onClick={() =>
                                                          onDeleteFileUpload(j, i, k)
                                                        }
                                                      ></i> */}
                                                      {opt.option.type === 'file' ?
                                                        <div className="form-group">
                                                          {opt.option.files.length !== 0 && (
                                                            <div
                                                              className="row"
                                                              style={{

                                                                padding: 10,
                                                              }}
                                                            >
                                                              {opt.option.files.map(
                                                                (files, f) => {
                                                                  return (
                                                                    <div
                                                                      className="col-12 d-flex align-items-center justify-content-center"
                                                                      style={{
                                                                        position:
                                                                          "relative",

                                                                      }}
                                                                    >
                                                                      <img
                                                                        style={{
                                                                          maxWidth:
                                                                            "100%",
                                                                          maxHeight:
                                                                            "100%",
                                                                        }}
                                                                        src={
                                                                          "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/current_control/" +
                                                                          files
                                                                        }
                                                                        alt="test"
                                                                      />

                                                                    </div>
                                                                  );
                                                                }
                                                              )}
                                                            </div>
                                                          )}

                                                        </div>

                                                        : ''}
                                                    </div>
                                                  );
                                                } else if (opt.type === "file") {
                                                  return (
                                                    <></>
                                                  );
                                                }
                                              })}
                                            </div>
                                            {/* <div
                                              className="form-group"
                                              style={{ textAlign: "center" }}
                                            >
                                              <button
                                                className="btn btn-primary"
                                                onClick={(e) =>
                                                  onClickButton(e, j, i)
                                                }
                                              >
                                                <i className="mdi mdi-plus"></i>
                                              </button>
                                            </div> */}
                                          </div>
                                        );
                                      } else if (item.type === "textarea") {
                                        return (
                                          <div className="form-group">
                                            <label htmlFor="user_name">
                                              {item.label}
                                            </label>
                                            <textarea
                                              onChange={(e) =>
                                                onchangeText(e, j, i)
                                              }
                                              className="form-control"
                                            >
                                              {item.value}
                                            </textarea>
                                          </div>
                                        );
                                      } else if (item.type === "checkbox1") {
                                        return (
                                          <>
                                            <div
                                              className="col-12"
                                              style={{
                                                padding: 10,
                                                textAlign: "center",
                                              }}
                                            >
                                              <div
                                                className="form-group mt-5 mb-3"
                                                style={{ textAlign: "center" }}
                                              >
                                                <label
                                                  htmlFor="user_name"
                                                  style={{ fontSize: 20 }}
                                                >
                                                  is this Risk Level Acceptable ?
                                                </label>
                                              </div>
                                              <div className="box-outer mb-4">
                                                {item.values.map((val, v) => {
                                                  return (
                                                    <label
                                                      htmlFor="user_name mt-3"

                                                      className={
                                                        val.selected
                                                          ? "box_select active"
                                                          : "box_select "
                                                      }
                                                    >
                                                      {val.label}{" "}
                                                    </label>
                                                  );
                                                })}
                                              </div>
                                            </div>

                                            {item.values[1].selected === true ? (
                                              <div className="row">
                                                <div
                                                  className="col-12"
                                                  style={{ padding: 0 }}
                                                >
                                                  <>
                                                    {item.option.map((opt, k) => {
                                                      return (
                                                        <>
                                                          {opt.type ===
                                                            "textbox1" ? (
                                                            <div
                                                              className="textbox1 mb-4"
                                                              style={{
                                                                padding: "20px",
                                                                boxShadow:
                                                                  "0px 0px 10px 4px #0000001f",
                                                              }}
                                                            >
                                                              <h5 className='mb-4 text-center'>Proposed Addition Control </h5>
                                                              {opt.option.map(
                                                                (opt1, l) => {
                                                                  return (
                                                                    <div className="row mb-4">
                                                                      <div className="form-group">
                                                                        <h4 htmlFor="user_name">
                                                                          {l + 1}. {opt1.current_type} {'-'}
                                                                          {
                                                                            opt1.value
                                                                          }
                                                                        </h4>
                                                                        {/* <Form.Control
                                                                          type="text"
                                                                          onChange={(
                                                                            e
                                                                          ) =>
                                                                            onchangeBox1Text(
                                                                              e,
                                                                              j,
                                                                              i,
                                                                              k,
                                                                              l
                                                                            )
                                                                          }
                                                                          value={
                                                                            opt1.value
                                                                          }
                                                                        /> */}
                                                                        {/* <i
                                                                          className="mdi mdi-delete"
                                                                          onClick={() =>
                                                                            onDeleteTextBox1(
                                                                              j,
                                                                              i,
                                                                              k,
                                                                              l
                                                                            )
                                                                          }
                                                                        ></i> */}
                                                                      </div>
                                                                      {opt1.option.map(
                                                                        (
                                                                          opt,
                                                                          m
                                                                        ) => {
                                                                          return (
                                                                            <>
                                                                              {opt.type ===
                                                                                "date" ? (
                                                                                <div className="col-6">
                                                                                  <label htmlFor="user_name">
                                                                                    {
                                                                                      opt.label
                                                                                    }
                                                                                  </label>
                                                                                  <h5>{moment(opt.value).format('YYYY-MM-DD')}</h5>
                                                                                  {/* <DatePicker
                                                                                    // selected={opt.value}
                                                                                    className="form-control"
                                                                                    dateFormat={'yyyy-MM-dd'}
                                                                                    onChange={(e) => handleDateChange(e, j, i, k, l, m)} //
                                                                                    value={moment(opt.value).format('YYYY-MM-DD')}
                                                                                  // value={opt.value}
                                                                                  /> */}
                                                                                </div>
                                                                              ) : (
                                                                                <div className="col-6">
                                                                                  <label htmlFor="user_name">
                                                                                    {
                                                                                      opt.label
                                                                                    }
                                                                                  </label>
                                                                                  <h5>
                                                                                    {opt.value.label ?
                                                                                      opt.value.label : opt.value
                                                                                    }</h5>
                                                                                  {/* <Form.Control
                                                                                    type="text"
                                                                                    onChange={(
                                                                                      e
                                                                                    ) =>
                                                                                      onchangeBox2Text(
                                                                                        e,
                                                                                        j,
                                                                                        i,
                                                                                        k,
                                                                                        l,
                                                                                        m
                                                                                      )
                                                                                    }
                                                                                    value={
                                                                                      opt.value
                                                                                    }
                                                                                  /> */}
                                                                                </div>
                                                                              )}
                                                                            </>
                                                                          );
                                                                        }
                                                                      )}
                                                                      {/* <div
                                                                        className="form-group"
                                                                        style={{
                                                                          textAlign:
                                                                            "center",
                                                                        }}
                                                                      >
                                                                        <button
                                                                          className="btn btn-primary"
                                                                          onClick={(
                                                                            e
                                                                          ) =>
                                                                            onClickButton1(
                                                                              e,
                                                                              j,
                                                                              i,
                                                                              k,
                                                                              l
                                                                            )
                                                                          }
                                                                        >
                                                                          <span className="mdi mdi-plus"></span>
                                                                        </button>
                                                                      </div> */}
                                                                    </div>
                                                                  );
                                                                }
                                                              )}
                                                            </div>
                                                          ) : opt.type ===
                                                            "rpn" ? (
                                                            <div
                                                              className="row"
                                                              style={{
                                                                display: "flex",
                                                                justifyContent:
                                                                  "space-between",
                                                                margin: 0,
                                                                paddingBottom: 10,
                                                                boxShadow:
                                                                  "0px 0px 10px 4px #0000001f",
                                                              }}
                                                            >
                                                              <div
                                                                className="form-group mt-3 mb-3"
                                                                style={{
                                                                  textAlign:
                                                                    "center",
                                                                }}
                                                              >
                                                                <p
                                                                  htmlFor="user_name"
                                                                  style={{
                                                                    fontSize: 20,
                                                                  }}
                                                                >
                                                                  RESIDUAL RISK
                                                                  ASSESSMENT
                                                                </p>
                                                                <span
                                                                  htmlFor="user_name"
                                                                  style={{
                                                                    fontSize: 12,
                                                                  }}
                                                                >
                                                                  (Expected risk
                                                                  based on the
                                                                  implementation
                                                                  of the
                                                                  identified
                                                                  additional
                                                                  controls)
                                                                </span>
                                                              </div>

                                                              <div className="col-4">
                                                                <div
                                                                  className="form-group mt-2 mb-1"
                                                                  style={{
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                >
                                                                  <label
                                                                    className="d-flex justify-content-between"
                                                                    htmlFor="user_name"
                                                                    style={{
                                                                      fontSize: 20,
                                                                    }}
                                                                  >
                                                                    Severity
                                                                    <div className="likelyhood_hover severity" >
                                                                      <i className="mdi mdi-information-outline"></i>
                                                                      <img s src={require('../../assets/images/severity.jpg')} alt="tet" />
                                                                    </div>
                                                                  </label>
                                                                  <p style={{ lineHeight: '18px' }}>   Degree of harm or
                                                                    impact that could
                                                                    result from a
                                                                    hazardous event or
                                                                    situation.</p>
                                                                </div>

                                                                {opt.severity.map(
                                                                  (opt2, l) => {
                                                                    if (
                                                                      opt2.type ===
                                                                      "select1"
                                                                    ) {
                                                                      return (
                                                                        <div className="col-12">
                                                                          <div className="form-group">
                                                                            <Select
                                                                              labelKey="label"
                                                                              id="user_description"

                                                                              value={opt2.selected}

                                                                              placeholder="Choose ..."
                                                                            />
                                                                          </div>
                                                                        </div>
                                                                      );
                                                                    }
                                                                  }
                                                                )}
                                                              </div>
                                                              <div className="col-4">
                                                                <div
                                                                  className="form-group mt-2 mb-1"
                                                                  style={{
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                >
                                                                  <label
                                                                    className="d-flex justify-content-between"
                                                                    htmlFor="user_name"
                                                                    style={{
                                                                      fontSize: 20,
                                                                    }}
                                                                  >
                                                                    Likelihood
                                                                    <div className="likelyhood_hover" >
                                                                      <i className="mdi mdi-information-outline"></i>
                                                                      <img s src={require('../../assets/images/likelyhood.jpg')} alt="tet" />
                                                                    </div>

                                                                  </label>
                                                                  <p style={{ lineHeight: '18px' }}>   Degree of harm or
                                                                    impact that could
                                                                    result from a
                                                                    hazardous event or
                                                                    situation.</p>
                                                                </div>

                                                                {opt.likelyhood.map(
                                                                  (opt2, l) => {
                                                                    if (
                                                                      opt2.type ===
                                                                      "select1"
                                                                    ) {
                                                                      return (
                                                                        <div className="col-12">
                                                                          <div className="form-group">
                                                                            <Select
                                                                              labelKey="label"
                                                                              id="user_description"

                                                                              value={opt2.selected}

                                                                              placeholder="Choose ..."
                                                                            />
                                                                          </div>
                                                                        </div>
                                                                      );
                                                                    }
                                                                  }
                                                                )}
                                                              </div>
                                                              <div className="col-4 ">
                                                                <div
                                                                  className="form-group  mb-1"
                                                                  style={{
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                >
                                                                  <label
                                                                    htmlFor="user_name"
                                                                    style={{
                                                                      fontSize: 20,
                                                                    }}
                                                                  >
                                                                    Residual Risk
                                                                  </label>
                                                                </div>

                                                                <>
                                                                  {opt.risk.map(
                                                                    (opt, k) => {
                                                                      return (
                                                                        <div className="col-12">
                                                                          <label
                                                                            className={`${opt.color ===
                                                                              ""
                                                                              ? ""
                                                                              : "black"
                                                                              }`}
                                                                            style={{
                                                                              padding: 10,
                                                                              background:
                                                                                opt.color,
                                                                              textAlign:
                                                                                "center",
                                                                              boxShadow:
                                                                                "0px 0px 13px 3px #efefef",

                                                                              borderRadius: 5,
                                                                              display:
                                                                                "flex",
                                                                              height: 40,
                                                                              justifyContent:
                                                                                "center",
                                                                              marginTop: 50
                                                                            }}
                                                                          >
                                                                            {opt.label} - {opt.color === '#8cc14b' ? 'Low' : opt.color === '#ffef00' ? 'Medium' : 'High'}
                                                                          </label>
                                                                        </div>
                                                                      );
                                                                    }
                                                                  )}
                                                                </>
                                                              </div>
                                                            </div>
                                                          ) : (
                                                            ""
                                                          )}
                                                        </>
                                                      );
                                                    })}
                                                  </>
                                                </div>
                                              </div>
                                            ) : (
                                              ""
                                            )}
                                          </>
                                        );
                                      } else if (item.type === "checkbox2") {
                                        return (
                                          <div className="">
                                            <div
                                              className="col-6"
                                              style={{ padding: 10 }}
                                            >
                                              <label
                                                htmlFor="user_name"
                                                style={{ paddingBottom: 10 }}
                                              >
                                                {item.label}{" "}
                                              </label>
                                              <Form.Group className="form-group">
                                                <Switch
                                                  onColor="#86d3ff"
                                                  onHandleColor="#2693e6"
                                                  handleDiameter={30}
                                                  uncheckedIcon={false}
                                                  checkedIcon={false}
                                                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                                  height={20}
                                                  width={48}
                                                  className="react-switch"
                                                  id="material-switch"
                                                  onChange={(e) =>
                                                    onchangeSelect(e, j, i)
                                                  }
                                                  checked={item.selected}
                                                />
                                              </Form.Group>
                                            </div>

                                            {item.selected === true ? (
                                              <div className="col-6">
                                                {item.option.map((item2, k) => {
                                                  return (
                                                    <>
                                                      {" "}
                                                      <label htmlFor="user_name">
                                                        {item2.label}{" "}
                                                      </label>
                                                      <Select
                                                        labelKey="label"
                                                        id="user_description"
                                                        onChange={(e) =>
                                                          handleSelectInChange(
                                                            e,
                                                            k,
                                                            j,
                                                            i
                                                          )
                                                        }
                                                        options={crew}
                                                        placeholder="Choose ..."
                                                      />
                                                    </>
                                                  );
                                                })}
                                              </div>
                                            ) : (
                                              ""
                                            )}
                                          </div>
                                        );
                                      } else if (item.type === "hazards") {
                                        return (
                                          <>
                                            <div
                                              className="form-group mt-5 mb-3"
                                              style={{ textAlign: "center" }}
                                            >
                                              <label
                                                htmlFor="user_name"
                                                style={{ fontSize: 20 }}
                                              >
                                                Hazards Identification
                                              </label>
                                            </div>
                                            <div className="form-group">
                                              <div className="row">
                                                {/* <div
                                                  className="col-6"
                                                  style={{
                                                    height: 500,
                                                    overflowY: "auto",
                                                  }}
                                                >
                                                  <label htmlFor="user_name">
                                                    Select potentials hazards
                                                    applicable for this
                                                    sub-activity
                                                  </label>
                                                  {item.option[0].allhazards
                                                    .length >= 0 ? (
                                                    <Accordion>
                                                      {item.option[0].allhazards.map(
                                                        (cate, c) => {
                                                          return (
                                                            <Accordion.Item
                                                              eventKey={c}
                                                            >
                                                              <Accordion.Header
                                                                className="accord"
                                                                style={{
                                                                  padding: 10,
                                                                }}
                                                              >
                                                                {cate.name}
                                                              </Accordion.Header>
                                                              <Accordion.Body
                                                                style={{
                                                                  paddingTop: 10,
                                                                }}
                                                              >
                                                                {cate.hazards ? (
                                                                  <div className="row">
                                                                    {cate.hazards.map(
                                                                      (ha, h) => {
                                                                        return (
                                                                          <div
                                                                            className="col-6"
                                                                            onClick={(
                                                                              e
                                                                            ) =>
                                                                              onClickHazard(
                                                                                e,
                                                                                i,
                                                                                j,
                                                                                c,
                                                                                h,
                                                                                cate,
                                                                                ha
                                                                              )
                                                                            }
                                                                          >
                                                                            <div
                                                                              className="row m-2 align-items-center justify-content-center"
                                                                              style={
                                                                                ha.active ===
                                                                                  true
                                                                                  ? {
                                                                                    boxShadow:
                                                                                      "0px 0px 12px 4px #d6d4d4",
                                                                                    border:
                                                                                      "2px solid red",
                                                                                    cursor:
                                                                                      "pointer",
                                                                                  }
                                                                                  : {
                                                                                    boxShadow:
                                                                                      "0px 0px 12px 4px #d6d4d4",
                                                                                    border:
                                                                                      "2px solid #fff",
                                                                                    cursor:
                                                                                      "pointer",
                                                                                  }
                                                                              }
                                                                            >
                                                                              <div className="col-4 p-0">
                                                                                <img
                                                                                  src={
                                                                                    "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                    ha.image
                                                                                  }
                                                                                  style={{
                                                                                    height: 60,
                                                                                  }}
                                                                                  alt="sample"
                                                                                />
                                                                              </div>
                                                                              <div className="col-8">
                                                                                <p>
                                                                                  {" "}
                                                                                  {
                                                                                    ha.name
                                                                                  }{" "}
                                                                                </p>
                                                                              </div>
                                                                            </div>
                                                                          </div>
                                                                        );
                                                                      }
                                                                    )}
                                                                  </div>
                                                                ) : (
                                                                  ""
                                                                )}
                                                              </Accordion.Body>
                                                            </Accordion.Item>
                                                          );
                                                        }
                                                      )}
                                                    </Accordion>
                                                  ) : (
                                                    ""
                                                  )}
                                                </div> */}
                                                <div
                                                  className="col-12"
                                                  style={{
                                                    background: "#f5f5f5",
                                                    padding: 15,

                                                    overflowY: "auto",
                                                  }}
                                                >
                                                  <label htmlFor="user_name">
                                                    Identified Hazards
                                                  </label>
                                                  {item.option[0].allhazards
                                                    .length >= 0 ? (
                                                    <div className="row">
                                                      {item.option[0].allhazards.map(
                                                        (cate, c) => {
                                                          return (
                                                            <>
                                                              {cate.hazards ? (
                                                                <>
                                                                  {cate.hazards.map(
                                                                    (ha, h) => {
                                                                      return (
                                                                        <>
                                                                          {ha.active ===
                                                                            true ? (
                                                                            <div
                                                                              className="col-6"
                                                                              style={{
                                                                                marginBottom: 16,
                                                                              }}
                                                                            >
                                                                              <div
                                                                                className="d-flex"
                                                                                style={{
                                                                                  boxShadow:
                                                                                    "0px 0px 12px 4px #d6d4d4",
                                                                                }}
                                                                              >
                                                                                <div
                                                                                  className=""
                                                                                  style={{
                                                                                    background:
                                                                                      "#fff",
                                                                                  }}
                                                                                >
                                                                                  <img
                                                                                    src={
                                                                                      "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                      ha.image
                                                                                    }
                                                                                    style={{
                                                                                      height: 60,
                                                                                      width: 60,
                                                                                    }}
                                                                                    alt="sample"
                                                                                  />
                                                                                </div>
                                                                                <div
                                                                                  className="d-flex align-items-center"
                                                                                  style={{
                                                                                    position:
                                                                                      "relative",
                                                                                    width:
                                                                                      "100%",
                                                                                    paddingLeft: 10,
                                                                                    background:
                                                                                      "#fff",
                                                                                  }}
                                                                                >
                                                                                  <p>
                                                                                    {" "}
                                                                                    {
                                                                                      ha.name
                                                                                    }{" "}
                                                                                  </p>
                                                                                  {/* <span
                                                                                    style={{
                                                                                      position:
                                                                                        "absolute",
                                                                                      right: 0,
                                                                                      top: "-7px",
                                                                                      fontStyle:
                                                                                        "italic",
                                                                                      fontSize: 12,
                                                                                      color:
                                                                                        "red",
                                                                                      cursor:
                                                                                        "pointer",
                                                                                    }}
                                                                                  >
                                                                                    <i
                                                                                      className="mdi mdi-close"
                                                                                      onClick={(
                                                                                        e
                                                                                      ) =>
                                                                                        onClickHazard(
                                                                                          e,
                                                                                          i,
                                                                                          j,
                                                                                          c,
                                                                                          h,
                                                                                          cate,
                                                                                          ha
                                                                                        )
                                                                                      }
                                                                                    ></i>
                                                                                  </span> */}
                                                                                </div>
                                                                              </div>
                                                                            </div>
                                                                          ) : (
                                                                            ""
                                                                          )}
                                                                        </>
                                                                      );
                                                                    }
                                                                  )}{" "}
                                                                </>
                                                              ) : (
                                                                ""
                                                              )}
                                                            </>
                                                          );
                                                        }
                                                      )}
                                                    </div>
                                                  ) : (
                                                    ""
                                                  )}
                                                </div>

                                              </div>
                                            </div>
                                          </>
                                        );
                                      } else if (item.type === "initial") {
                                        return (
                                          <div className="row">
                                            <div
                                              className="col-12"
                                              style={{
                                                padding: 20,
                                                boxShadow:
                                                  "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                                              }}
                                            >
                                              <div className="row">
                                                <div className="col-12">
                                                  <div
                                                    className="form-group mt-2 mb-1"
                                                    style={{
                                                      textAlign: "center",
                                                    }}
                                                  >
                                                    <p
                                                      htmlFor="user_name"
                                                      style={{ fontSize: 20 }}
                                                    >
                                                      RISK ASSESSMENT OF THIS SUB-ACTIVITY
                                                    </p>
                                                    <span
                                                      style={{ fontSize: 12 }}
                                                    >
                                                      (Risk level based on the
                                                      presence of the above
                                                      identified controls)
                                                    </span>
                                                  </div>
                                                </div>
                                              </div>
                                              <div className="row">
                                                <div className="col-4">
                                                  <div
                                                    className="form-group mt-2 mb-1"
                                                    style={{
                                                      textAlign: "center",
                                                    }}
                                                  >
                                                    <label
                                                      className="d-flex justify-content-between"
                                                      htmlFor="user_name"
                                                      style={{ fontSize: 20 }}
                                                    >
                                                      Severity

                                                      <div className="likelyhood_hover severity" >
                                                        <i className="mdi mdi-information-outline"></i>
                                                        <img s src={require('../../assets/images/severity.jpg')} alt="tet" />
                                                      </div>
                                                    </label>
                                                    <p style={{ lineHeight: '18px' }}>   Degree of harm or
                                                      impact that could
                                                      result from a
                                                      hazardous event or
                                                      situation.</p>
                                                  </div>

                                                  <div
                                                    className="row"
                                                    style={{
                                                      display: "flex",
                                                      justifyContent:
                                                        "space-between",
                                                    }}
                                                  >
                                                    {item.severity.map(
                                                      (opt, k) => {
                                                        if (
                                                          opt.type === "select1"
                                                        ) {
                                                          return (
                                                            <div className="col-12">
                                                              <div className="form-group">
                                                                <Select
                                                                  labelKey="label"
                                                                  id="user_description"


                                                                  value={opt.selected}
                                                                  placeholder="Choose ..."
                                                                />
                                                              </div>
                                                            </div>
                                                          );
                                                        }
                                                      }
                                                    )}
                                                  </div>
                                                </div>
                                                <div className="col-4">
                                                  <div
                                                    className="form-group mt-2 mb-1"
                                                    style={{
                                                      textAlign: "center",
                                                    }}
                                                  >
                                                    <label
                                                      className="d-flex justify-content-between"
                                                      htmlFor="user_name"
                                                      style={{ fontSize: 20 }}
                                                    >
                                                      Likelihood
                                                      <div className="likelyhood_hover" >
                                                        <i className="mdi mdi-information-outline"></i>
                                                        <img s src={require('../../assets/images/likelyhood.jpg')} alt="tet" />
                                                      </div>

                                                      {/* <OverlayTrigger
                                                                                    placement={"left"}
                                                                                    overlay={
                                                                                      <Tooltip id={`tooltip`} style={{height:100,width:100}}>
                                                                                      
                                                                                      </Tooltip>
                                                                                    }
                                                                                  >
                                                                                    <i className="mdi mdi-information-outline"></i>
                                                                                  </OverlayTrigger> */}
                                                    </label>
                                                    <p style={{ lineHeight: '18px' }}> Frequency with which a
                                                      hazardous event or
                                                      situation could
                                                      happen.</p>
                                                  </div>

                                                  <div
                                                    className="row"
                                                    style={{
                                                      display: "flex",
                                                      justifyContent:
                                                        "space-between",
                                                    }}
                                                  >
                                                    {item.likelyhood.map(
                                                      (opt, k) => {
                                                        if (
                                                          opt.type === "select1"
                                                        ) {
                                                          return (
                                                            <div className="col-12">
                                                              <div className="form-group">
                                                                <Select
                                                                  labelKey="label"
                                                                  id="user_description"

                                                                  value={opt.selected}

                                                                  placeholder="Choose ..."
                                                                />
                                                              </div>
                                                            </div>
                                                          );
                                                        }
                                                      }
                                                    )}
                                                  </div>
                                                </div>
                                                <div className="col-4 ">
                                                  <div
                                                    className="form-group "
                                                    style={{
                                                      textAlign: "center",
                                                      marginBottom: 6,
                                                    }}
                                                  >
                                                    <label
                                                      htmlFor="user_name"
                                                      style={{ fontSize: 20 }}
                                                    >
                                                      Risk Level
                                                    </label>
                                                  </div>
                                                  <div
                                                    className="row"
                                                    style={{
                                                      display: "flex",
                                                      justifyContent:
                                                        "space-between",
                                                    }}
                                                  >
                                                    {item.risk.map((opt, k) => {
                                                      return (
                                                        <div className="col-12">
                                                          <label
                                                            className={`${opt.color === ""
                                                              ? ""
                                                              : "black"
                                                              }`}
                                                            style={{
                                                              padding: 10,
                                                              background:
                                                                opt.color,
                                                              textAlign: "center",
                                                              boxShadow:
                                                                "0px 0px 13px 3px #e7e4e4",

                                                              borderRadius: 5,
                                                              display: "flex",
                                                              height: 40,
                                                              justifyContent:
                                                                "center",
                                                              marginTop: 44
                                                            }}
                                                          >
                                                            {opt.label} - {opt.color === '#8cc14b' ? 'Low' : opt.color === '#ffef00' ? 'Medium' : 'High'}
                                                          </label>
                                                        </div>
                                                      );
                                                    })}
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        );
                                      }
                                    })}
                                  </div>
                                </Accordion.Body>
                              </Accordion.Item>
                            )
                          })}
                        </div>
                      </Accordion>
                      {/* <div className='col-12 text-center' style={{ padding: 20 }}>
                                                <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); addTask() }}> Add Sub Activity</button></div> */}
                    </div>
                  </div>

                  <div className="col-12 mt-4 mb-4" style={{
                    padding: 20,
                    boxShadow:
                      "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                  }}>
                    <h5 style={{ textAlign: 'center' }} className="mt-2 mb-3 ">Overall recommendation of the RA Team</h5>
                    {meet.recommendation ?
                      <p className={'text-center'}>{meet.recommendation.label}</p> : ''}

                    {meet.additionRecommendation ?
                      <p className={'text-center'}>{meet.additionRecommendation.label}</p> : ''}

                    {meet.thirdAdd ?
                      <p className={'text-center'}>{meet.thirdAdd}</p> : ''}
                    <h6>considering the hazards and risk associated with this work activity the RA request the following Other risk permits to be approve</h6>
                    {meet.eptwHighRisk ?
                      <ol>
                        {meet.eptwHighRisk.map(item => (
                          <li>{item.name}</li>

                        ))}
                      </ol>
                      : ''}

                  </div>
                  <div className="col-12 mt-4 mb-4" style={{
                    padding: 20,
                    boxShadow:
                      "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                  }}>
                    <h5 style={{ textAlign: 'center' }} className="mt-2 mb-3">Team Leader Declaration</h5>
                    <p style={{ textAlign: 'center' }}>I affirm my position as the Team Leader for this Risk Assessment. The eventual outcome signifies our collective professional judgment, reached through consensus and utilizing our team's fullest capabilities.</p>


                    <div className="row mt-4">
                      <div className="col-12 text-center">
                        {meet.sign ?
                          <img src={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/" + meet.sign} alt='' />
                          : ''}
                        <p> {meet.captain}</p>
                      </div>

                    </div>
                  </div>
                  {meet.teamMemberInvolved.length !== 0 ?
                    <div className="col-12 mt-4 mb-4" style={{
                      padding: 20,
                      boxShadow:
                        "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                    }}>
                      <h5 style={{ textAlign: 'center' }} className="mt-2 mb-3">Team Members Declaration</h5>
                      <p style={{ textAlign: 'center' }}>I confirm my participation in this Risk Assessment as a team member. The outcome reflects our shared professional judgment to the best of our abilities through consensus.</p>
                      <table className="table ">
                        <thead>
                          <th>Name</th>
                          <th>Department</th>
                          <th>Sign / Date of Affimation</th>
                        </thead>
                        <tbody>
                          {meet.teamMemberInvolved.map((item) => {
                            if (item) {
                              return (<tr>
                                <td>{item.label}</td>
                                <td>{item.department}</td>

                                <td>{item.sign === 'No' ? 'Pending' : <div className='d-flex flex-column align-items-start'> <img src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/' + item.sign} style={{ maxWidth: '100%', width: 'unset', height: '130px' }} /><span>{item.date}</span></div>}</td>
                                {/* {user.id === item.id ?
                                <td>
                                  {item.sign === 'No' ? <p onClick={() => { setMdSign(true); setMember(item) }} className='userAgree'>Agree</p> : <div className='d-flex flex-column align-items-start'> <img src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/' + item.sign} style={{ maxWidth: '100%', width: 'unset', height: 'unset' }} /><span>{item.date}</span></div>}
                                </td> : <td>{item.sign === 'No' ? 'Pending' : <div className='d-flex flex-column align-items-start'> <img src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/' + item.sign} style={{ maxWidth: '100%', width: 'unset', height: 'unset' }} /><span>{item.date}</span></div>}</td>} */}

                              </tr>)
                            }
                          })}
                        </tbody>



                      </table>
                    </div>
                    : ''}
                  {/* {meet.teamMemberInvolved.length !== 0 ?
                    <div className="col-12 mt-4 mb-4" style={{
                      padding: 20,
                      boxShadow:
                        "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                    }}>
                      <h5 style={{ textAlign: 'center' }}>I confirm that the following team members were invovled in the development of this risk assessment</h5>
                      <div className="col-12 mt-3 mb-3" style={{ padding: 10 }}>
                        <table className="table">
                          <thead className="thead-dark">
                            <th>Team Member</th>
                            <th>Department</th>
                            <th>I confirm my participation in this risk assessment as a team member and the collective output reflects my professional judgment to the best of my abilities.</th>
                          </thead>
                          <tbody>

                            {meet.teamMemberInvolved.map((item) => {
                              return (<tr>
                                <td>{item.label}</td>
                                <td>{''}</td>
                                {user.id === item.id ? <td>
                                  {item.sign === 'No' ? <p onClick={() => userAgree(item)} style={{ padding: 10, background: '#d62828', color: '#fff' }}>Agree</p> : item.date}
                                </td> : <td>Not Yet Done</td>}
                                
                              </tr>)
                            })}
                            <tr>
                              <td>{meet.captain}</td>
                              <td></td>
                              <td> {meet.sign ?
                                <img style={{ width: 150, height: 50 }} src={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/" + meet.sign} alt='' />
                                : ''
                                  
                              }</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    : ''} */}



                </div>
              </div>
            </div>
          </div>
        </div>
        : ''}


      <div className='col-12'>
        <div className="card">
          <div className="card-body">
            {/* <h4 className="card-title" style={{ textAlign: 'center', fontSize: 20 }}>Reviews,Changes and Updates</h4> */}
            {/* <SagoTable searchText={"Search Update"} columns={columns} data={update} /> */}
            <ThemeProvider theme={defaultMaterialTheme}>
              <MaterialTable
                columns={columns}
                data={update}
                title="Reviews,Changes and Updates"
                style={tableStyle}
                options={{
                  actionsColumnIndex: -1
                }}
                actions={[{
                  icon: 'visibility',
                  tooltip: 'View Risk',
                  onClick: (event, rowData) => {

                    setAttachmentShow(true)
                    setViewAttachment(rowData.attachment)
                    // Do save operation
                    // console.log(rowData)
                    // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
                  }
                },]}



              />
            </ThemeProvider>
          </div>
        </div>
      </div>
      <Modal
        show={attachmentShow}
        size={'md'}
        onHide={() => setAttachmentShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Header closeButton>
          Attachment
        </Modal.Header>
        <Modal.Body>
          <div
            className="row mt-3"
            style={{
              padding: 10,

              borderRadius: 10

            }}
          >
            {viewAttachment.length != 0 &&
              viewAttachment.map((files, f) => {
                return (
                  <div
                    className="col-3 d-flex align-items-center justify-content-center"
                    style={{
                      position:
                        "relative",

                    }}
                  >
                    {files.type.match('image') ? <>
                      <img
                        style={{

                          maxHeight:
                            "100%",
                          maxWidth: "100%"
                        }}
                        src={
                          "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" +
                          files.name}
                        alt="test"
                      />

                    </> : files.type === 'application/pdf' ? <>
                      <a target='_blank' href={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" + files.name}>
                        <i class="fa fa-file-pdf-o fa-3x" aria-hidden="true"></i>
                      </a>

                    </> : <>
                      <a target='_blank' href={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" + files.name}>
                        <i class="fa fa-file-excel-o fa-3x" aria-hidden="true"></i>
                      </a>

                    </>}
                  </div>
                );
              }
              )}
          </div>
        </Modal.Body>
      </Modal>
      <Modal
        show={mdSign}
        onHide={() => setMdSign(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Header closeButton>
          Please Sign..
        </Modal.Header>
        <Modal.Body>
          <div className="row mt-4">
            <div className="col-12 text-center">
              <SignatureCanvas
                penColor="#1F3BB3"
                canvasProps={{
                  width: 350,
                  height: 100,
                  className: "sigCanvas",
                  style: {
                    boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                  },
                }}
                ref={signRef}


              />  <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
              <p> {user.firstName}</p>
            </div>

          </div>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">


          <Button variant="primary" onClick={userAgree}>
            Done
          </Button>

        </Modal.Footer>
      </Modal>
      <Modal
        show={mdShow}
        size={'lg'}
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >

        <Modal.Body>
          <form className="forms row" >
            <div className="form-group col-6">
              <label htmlFor="user_name" >Reason For Review</label>
              <textarea className='form-control' ref={review} ></textarea>
            </div>
            <div className="form-group col-6">
              <label htmlFor="user_name" >Changes</label>
              <textarea className='form-control' ref={change} ></textarea>
            </div>
            <div className="form-group col-6">
              <label htmlFor="user_category" >Reason for Change</label>
              <textarea className='form-control' ref={reasonchange} ></textarea>
            </div>
            <div className="form-group col-6">
              <label htmlFor="user_category" >Initiated By</label>
              <textarea className='form-control' ref={initial} ></textarea>
            </div>
            <div className="form-group col-6">
              <label htmlFor="user_category" >Approved By</label>
              <textarea className='form-control' ref={approve} ></textarea>
            </div>
            <div className="form-group col-6">
              <label htmlFor="user_category" >Reference</label>
              <textarea className='form-control' ref={reference} ></textarea>
            </div>
            <div className='' style={{ textAlign: 'center' }}>
              <p>I confim that the risk assessment has been reviewed  as above and changes (if any) have been made as shown here. This change has also been authroized by the relevant authority</p>
            </div>
            <div className='col-12' style={{ textAlign: 'center' }}>
              <SignatureCanvas penColor='#1F3BB3'
                canvasProps={{ width: 500, height: 200, className: 'sigCanvas', style: { boxShadow: '0px 0px 10px 3px rgb(189 189 189)' } }} ref={sign1} style={{}} />
              <div className='col-12'>
                <button className='btn btn-primary' onClick={(e) => { e.preventDefault(); sign1.current.clear() }} >Clear</button>
              </div>

            </div>



          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">
          {
            isLoading ? '' : (
              <>
                <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>
                <Button variant="primary" onClick={updateHandler}>Update</Button>
              </>
            )
          }

        </Modal.Footer>
      </Modal>

    </>
  )
}
export const Column = (props) => {

  return (
    <div className="border p-4 col-sm-6" style={{ background: '#f5f5f5' }}>
      <div><h6 className="card-title">{props.column.tittle}</h6></div>
      <Droppable droppableId={props.column.id}>
        {provided => (
          <div className="kanbanHeight"
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {props.tasks.map((task, index) =>
              <Task key={task.id} task={task} index={index} />)}
            {provided.placeholder}
          </div>
        )}
      </Droppable >
    </div>
  )

}
export const Task = (props) => {

  return (<Draggable draggableId={props.task.id} index={props.index}>
    {(provided) => (
      <div className="mt-1 board-portlet"
        {...provided.draggableProps}
        {...provided.dragHandleProps}
        ref={provided.innerRef}
      >
        <div className="card-body p-3 bg-white">
          <div className="media">
            <div className="media-body">
              <div className="d-flex">
                <img src={props.task.imgURL} alt="profile" className="img-sm me-3" />
                <div>
                  <h6 className="mb-1">{props.task.name}</h6>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )}
  </Draggable>
  )

}
export default ViewRisk;
