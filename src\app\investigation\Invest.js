import React, { useEffect, useState } from 'react'
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

import { InputTextarea } from 'primereact/inputtextarea';

import { GMS1_URL, GET_USER_ROLE_BY_MODE, HAZARDS_CATEGOTY, GET_ALL_USER } from '../constants';
import API from '../services/API';
function Invest() {
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [visible, setVisible] = useState(false)
    const [riskTable, setRiskTable] = useState(false)
    const [hazards, setHazards] = useState([])
    const [task, setTask] = useState([
        { type: 'activity', name: '', images: [] },
        { type: 'consequence', option: [{ value: "", files: [], current_type: '', additional: '' }] },
        { type: 'hazards', selected: [], mention: '', describe: '' },
        { type: 'preventive_control', identified: [{ value: "", type: "" }], unidentified: [{ value: "" }] },
        { type: 'mitigative_control', identified: [{ value: "", type: "" }], unidentified: [{ value: "" }] },
        { type: 'record', data: [{ title: '', describe: '', date: '', filename: '' }] },
        { type: 'activeStep', step: 0 },
        { type: 'stage', level: ['Incdient Details', 'Consequences', 'Precursors & Hazardous Conditions', 'Preventive Controls', 'Mitigative Controls', 'Investigation Records'] },
        { type: 'completed_stage', level: [] }
    ])
    const severity = [
        { "value": "1", "label": "Negligible (1)" },
        { "value": "2", "label": "Minor (2)" },
        { "value": "3", "label": "Moderate (3)" },
        { "value": "4", "label": "Major (4)" },
        { "value": "5", "label": "Catastrophic (5)" }
    ]
    const likelyhood = [
        { label: "Rare (1)", value: "1" },
        { label: "Unlikely (2)", value: "2" },
        { label: "Possible (3)", value: "3" },
        { label: "Likely (4)", value: "4" },
        { label: "Almost Certain (5)", value: "5" },
    ]
    const impactOn = [
        { 'label': 'Personnel', 'value': 'Personnel' },
        { 'label': 'Property', 'value': 'Property' },
        { 'label': 'Environment', 'value': 'Environment' },
        { 'label': 'Service Loss', 'value': 'Service Loss' },
    ]
    const control = [
        { 'label': 'No Control', 'value': 'No Control' },
        { 'label': 'Engineering', 'value': 'Engineering' },
        { 'label': 'Administrative', 'value': 'Administrative' },
        { 'label': 'PPE', 'value': 'PPE' }

    ]
    const severityData = [
        { id: '5', severity: '5 Catastrophic' },
        { id: '4', severity: '4 Major' },
        { id: '3', severity: '3 Moderate' },
        { id: '2', severity: '2 Minor' },
        { id: '1', severity: '1 Insignificant' },
    ];
    const levelData = [
        { level: '1 (A)', descriptor: 'Rare', detailedDescription: '' },
        { level: '2 (B)', descriptor: 'Unlikely', detailedDescription: '' },
        { level: '3 (C)', descriptor: 'Possible', detailedDescription: '' },
        { level: '4 (D)', descriptor: 'Likely', detailedDescription: '' },
        { level: '5 (E)', descriptor: 'Almost Certain', detailedDescription: '' },
    ];
    const tableData = [
        { id: '5', severity: 'Catastrophic', rare: 5, unlikely: 10, possible: 15, likely: 20, almostCertain: 25 },
        { id: '4', severity: 'Major', rare: 4, unlikely: 8, possible: 12, likely: 16, almostCertain: 20 },
        { id: '3', severity: 'Moderate', rare: 3, unlikely: 6, possible: 9, likely: 12, almostCertain: 15 },
        { id: '2', severity: 'Minor', rare: 2, unlikely: 4, possible: 6, likely: 8, almostCertain: 10 },
        { id: '1', severity: 'Insignificant', rare: 1, unlikely: 2, possible: 3, likely: 4, almostCertain: 5 },
    ];

    useEffect(() => {
        getHazardList()
    }, [])
    const getHazardList = async () => {
        const uriString = { include: ["hazards"] };
        const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const data = response.data.filter((item) => item.name !== 'Hazard-Based')
            setHazards(data);
        }
    }
    const cellClassName = (value) => {
        if (value === 1 || value === 2 || value === 3 || value === 4) return 'cell-green';
        if (value === 15 || value === 20 || value === 25 || value === 16) return 'cell-red';
        return 'cell-yellow';
    };
    const cellStyle = (data, field) => cellClassName(data[field]);

    const rowClassName = (data) => {
        switch (data.level[0]) {
            case '1':
                return 'level-1';
            case '2':
                return 'level-2';
            case '3':
                return 'level-3';
            case '4':
                return 'level-4';
            case '5':
                return 'level-5';
            default:
                return '';
        }
    };

    const handleNext = () => {

        task[8].level.push(task[6].step)
        task[8].level = [...new Set(task[8].level)]
        if (task[6].step === task[7].level.length - 1) {
            setVisible(false)

        } else {
            const t = task;
            const text = t.map((item, i) => {
                if (item.type === 'activeStep') {
                    item.step = item.step + 1

                }
                return item
            })
            setTask(text)

        }


    };
    const handleBack = () => {

        task[8].level = task[8].level.filter(item1 => item1 !== task[6].step);
        const t = task;
        const text = t.map((item, i) => {
            if (item.type === 'activeStep') {
                item.step = item.step - 1
            }
            if (item.type === 'completed_stage') {
                item.level.pop(item.step)
            }

            return item
        })
        setTask(text)
    };
    const footerTemplate = (
        <div className="d-flex justify-content-between">
            <div>
                <Button
                    className='me-2'
                    outlined
                    label="Back"
                    onClick={handleBack}
                    disabled={task[6] && task[6].step !== undefined && task[6].step === 0}
                />
            </div>
            <div>
                <Button className='me-2' outlined label="Cancel" />
                <Button className='me-2' outlined label="Save Progress" />
                <Button
                    label={task[6] && task[6].step !== undefined && task[6].step === task[7].level.length - 1 ? "Finish" : "Next"}
                    onClick={handleNext}
                />
            </div>
        </div>
    );
    const headerTemplate = (
        <div className="d-flex flex-column">
            <div className='col-12 mb-3'>
                <p>#INC-230912-79</p>
                <h6>Data Collection for Incident Investigation</h6>
            </div>
            {console.log(task)}

            {task[6] ? (
                <Stepper activeStep={task[6].step} className='mb-4'>
                    {task[7].level.map((label, index) => (
                        <Step key={index}>
                            <StepLabel className='step-label d-flex flex-column'>{label}</StepLabel>
                        </Step>
                    ))}
                </Stepper>
            ) : (
                <p>Loading stepper data...</p>
            )}
        </div>
    );


    return (
        <>
            <div onClick={() => setVisible(true)}>Start Invest</div>

            <Dialog visible={visible} header={headerTemplate} modal footer={footerTemplate} style={{ width: '70rem' }} onHide={() => setVisible(false)}>

                {task[6].step === 0 && <>

                    <div className='p-4 mt-4'>
                        <div className='col-12'>
                            <div className="d-flex flex-column col-12">
                                <label htmlFor="username" className='mb-2'>Describe the Incident/ Risk Event</label>
                                <InputTextarea className='d-flex' rows={3} />
                            </div>
                        </div>
                        <div className='col-12 mt-3'>
                            <label htmlFor="username" className='mb-2'>Upload Incident images</label>
                            <div className="mb-3">
                                <DropzoneArea
                                    acceptedFiles={[
                                        'image/jpeg',
                                        'image/png'
                                    ]}
                                    dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                    filesLimit={5}
                                    maxFileSize={104857600}

                                    showPreviewsInDropzone={false}
                                    showPreviews={true}
                                    dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                />
                            </div>
                        </div>

                        <div className='col-12 mt-3'>
                            <label htmlFor="username" className='mb-2'>Uploaded</label>

                        </div>

                    </div>

                </>}

                {task[6].step === 1 && <>

                    {task[1].option.map((con, i) => {
                        return (<>

                            <div class="row mt-4 mb-4 align-items-end">


                                <div className='col-3'>
                                    <p className=''>Impact on</p>
                                    <Dropdown options={impactOn} value={con.current_type} className={`d-flex`} />
                                </div>
                                <div className='col-8'>
                                    <p>Mention the consequence of the Incident</p>
                                    <InputText style={{ width: '100%' }} value={con.value} />
                                </div>
                                <div className='col-1 text-center'>
                                    <i className="pi pi-trash mb-3" ></i>
                                </div>
                            </div>

                        </>

                        )
                    })}

                    <Button outlined label="Add Consequence" />



                    <div className="d-flex flex-column col-12 mt-4">
                        <p htmlFor="username" className='mb-2'>Additional Comments on the Consequences</p>
                        <InputTextarea className='d-flex' rows={2} />
                    </div>



                </>}

                {task[6].step === 2 && <>
                    <div className='row mb-4'>
                        <div className='col-5'>
                            <p htmlFor="username" className='mb-2'>Mention what led to the Incident (precursors)</p>
                            <InputTextarea style={{ width: '100%' }} rows={2} />
                        </div>
                        <div className='col-5'>
                            <p htmlFor="username" className='mb-2'>Describe the hazardous situation of the Incident</p>
                            <InputTextarea style={{ width: '100%' }} rows={2} />
                        </div>


                    </div>


                    <Accordion defaultActiveKey={'0'}>
                        <Accordion.Item eventKey="0">
                            <Accordion.Header >
                                <h6>Hazardous Conditions</h6>
                                <p>Hazardous work conditions that resulted in this incident.</p>
                            </Accordion.Header>
                            <Accordion.Body>
                                <div class="d-flex" style={{ border: '1px solid #E5E7EB' }}>

                                    <TabView
                                        activeIndex={activeTabIndex}
                                        onTabChange={(e) => setActiveTabIndex(e.index)}
                                        orientation="left"
                                        className='d-flex hazTabs'
                                    >
                                        {hazards.map((haz, h) => {
                                            return (
                                                <TabPanel header={haz.name} className='tabsHead'>
                                                    <div className='row'>
                                                        {haz.hazards.map((ha, j) => {
                                                            return (
                                                                <div className='col-4 mb-3'>
                                                                    <div className={`d-flex align-items-center hazClick ${task[2].selected.some(hazard => hazard.id === ha.id) ? 'active' : ''}`} >
                                                                        <div className='col-2'>
                                                                            {task[2].selected.some(hazard => hazard.id === ha.id) ?
                                                                                <Checkbox name='haz' checked></Checkbox>
                                                                                :
                                                                                <Checkbox name='haz'></Checkbox>
                                                                            }
                                                                        </div>
                                                                        <div className='col-2'>
                                                                            <img
                                                                                src={
                                                                                    "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                    ha.image
                                                                                }
                                                                                style={{
                                                                                    height: 40,
                                                                                }}
                                                                                alt="sample"
                                                                            />
                                                                        </div>
                                                                        <div className='col-8 ms-3'>
                                                                            <p className='m-0'>{ha.name}</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            )
                                                        })}
                                                    </div>
                                                </TabPanel>
                                            )
                                        })}
                                    </TabView>
                                </div>
                            </Accordion.Body>
                        </Accordion.Item>
                    </Accordion>
                    <h6 className='mt-4 mb-3'>Hazards Identified</h6>
                    <div className='row'>
                        {task[2].selected.map((item) => {
                            return (
                                <div className='col-3 mb-3'>
                                    <div className='d-flex justify-content-between align-items-center p-2' style={{ border: '1px solid #E5E7EB', borderRadius: 8 }}>
                                        <img
                                            src={
                                                "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                item.image
                                            }
                                            style={{
                                                height: 40,
                                            }}
                                            alt="sample"
                                        />
                                        <p>{item.name}</p>
                                        <i className='pi pi-times' ></i>
                                    </div>


                                </div>
                            )

                        })}

                    </div>


                </>}

                {task[6].step === 3 && <>

                    <h4>Identified Preventive Controls</h4>

                    {task[3].identified.map((con, i) => {
                        return (<>

                            <div class="row mt-4 mb-4 align-items-end">


                                <div className='col-1'>
                                    {'PC ' + (i + 1)}
                                </div>
                                <div className='col-5'>
                                    <p>Mention the preventive controls taken</p>
                                    <InputText style={{ width: '100%' }} value={con.value} />
                                </div>
                                <div className='col-5'>
                                    <div className="d-flex  justify-content-evenly">
                                        <div className="d-flex align-items-center me-3 boxEff boxEffective">
                                            <RadioButton value="Effective" />
                                            <label className="ms-2">Effective</label>
                                        </div>
                                        <div className="flex align-items-center boxEff boxUnEffective">
                                            <RadioButton value="NonEffective" />
                                            <label className="ms-2">NonEffective</label>
                                        </div>
                                    </div>
                                </div>
                                <div className='col-1 text-center'>
                                    <i className="pi pi-trash mb-3" ></i>
                                </div>
                            </div>

                        </>

                        )
                    })}
                    <div className='row'>
                        <div className='col-1'></div>
                        <div className='col-4'>
                            <Button outlined label="Add More" />
                        </div>
                    </div>

                    <h4 className='mt-4'>UnIdentified Preventive Controls</h4>

                    {task[3].unidentified.map((con, i) => {
                        return (<>

                            <div class="row mt-4 mb-4 align-items-end">


                                <div className='col-1'>
                                    {'UPC ' + (i + 1)}
                                </div>
                                <div className='col-10'>
                                    <p>Describe the unidentified preventive control</p>
                                    <InputText style={{ width: '100%' }} value={con.value} />
                                </div>

                                <div className='col-1 text-center'>
                                    <i className="pi pi-trash mb-3" ></i>
                                </div>
                            </div>

                        </>

                        )
                    })}

                    <div className='row'>
                        <div className='col-1'></div>
                        <div className='col-4'>
                            <Button outlined label="Add More" />
                        </div>
                    </div>

                </>}

                {task[6].step === 4 && <>

                    <h4>Identified Mitigative Controls</h4>

                    {task[3].identified.map((con, i) => {
                        return (<>

                            <div class="row mt-4 mb-4 align-items-end">


                                <div className='col-1'>
                                    {'MC ' + (i + 1)}
                                </div>
                                <div className='col-5'>
                                    <p>Mention the mitigative controls taken</p>
                                    <InputText style={{ width: '100%' }} value={con.value} />
                                </div>
                                <div className='col-5'>
                                    <div className="d-flex  justify-content-evenly">
                                        <div className="d-flex align-items-center me-3 boxEff boxEffective">
                                            <RadioButton value="Effective" />
                                            <label className="ms-2">Effective</label>
                                        </div>
                                        <div className="flex align-items-center boxEff boxUnEffective">
                                            <RadioButton value="NonEffective" />
                                            <label className="ms-2">NonEffective</label>
                                        </div>
                                    </div>
                                </div>
                                <div className='col-1 text-center'>
                                    <i className="pi pi-trash mb-3" ></i>
                                </div>
                            </div>

                        </>

                        )
                    })}
                    <div className='row'>
                        <div className='col-1'></div>
                        <div className='col-4'>
                            <Button outlined label="Add More" />
                        </div>
                    </div>

                    <h4 className='mt-4'>Unidentified Mitigative Controls</h4>

                    {task[3].unidentified.map((con, i) => {
                        return (<>

                            <div class="row mt-4 mb-4 align-items-end">


                                <div className='col-1'>
                                    {'UMC ' + (i + 1)}
                                </div>
                                <div className='col-10'>
                                    <p>Describe the unidentified mitigative control</p>
                                    <InputText style={{ width: '100%' }} value={con.value} />
                                </div>

                                <div className='col-1 text-center'>
                                    <i className="pi pi-trash mb-3" ></i>
                                </div>
                            </div>

                        </>

                        )
                    })}

                    <div className='row'>
                        <div className='col-1'></div>
                        <div className='col-4'>
                            <Button outlined label="Add More" />
                        </div>
                    </div>

                </>}

                {task[6].step === 5 && <>

                    <h4>Investigation Records</h4>

                    <DataTable value={[]} className="table-bordered">
                        <Column field="" header="Title"></Column>
                        <Column field="" header="Description"></Column>
                        <Column header="Date of upload"></Column>
                        <Column header="Documents"></Column>
                        <Column header=""></Column>
                        
                    </DataTable>


                    <h4 className='mt-4'>Add Investigation Record</h4>

                    <div className='p-4 mt-4 row'>
                        <div className='col-4'>
                            <label htmlFor="username" className='mb-2'>Title</label>
                            <InputText style={{ width: '100%' }} />
                        </div>
                        <div className='col-8'>
                            <label htmlFor="username" className='mb-2'>Brief Description</label>
                            <InputText style={{ width: '100%' }} />

                        </div>
                        <div className='col-12 mt-3'>
                            <label htmlFor="username" className='mb-2'>Upload Incident images</label>
                            <div className="mb-3">
                                <DropzoneArea
                                    acceptedFiles={[
                                        'image/jpeg',
                                        'image/png'
                                    ]}
                                    dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                    filesLimit={1}
                                    maxFileSize={104857600}

                                    showPreviewsInDropzone={false}
                                    showPreviews={true}
                                    dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                />
                            </div>
                        </div>

                        <div className='col-12 mt-3'>
                            <label htmlFor="username" className='mb-2'>Uploaded</label>

                        </div>

                        <div className='col-4'>
                            <Button outlined label="Add Investigation Record" />
                        </div>

                    </div>








                </>}


            </Dialog>

        </>
    )
}

export default Invest