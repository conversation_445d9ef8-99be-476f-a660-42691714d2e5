import React, { useEffect, useRef, useState } from 'react';
import API from '../services/API';
import { ACTION_URL, AIR_WITH_ID_URL, STATIC_URL, RA_ACTION_WITH_ID } from '../constants';

import moment from 'moment';
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

import { Modal,Button } from 'react-bootstrap'
import SignatureCanvas from 'react-signature-canvas'
import $ from "jquery";
import S3 from "react-aws-s3";
import { useSelector } from 'react-redux';
import Swal from 'sweetalert2';
Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
window.jQuery = $;
// @ts-ignore
window.$ = $;
const config = {
  bucketName: "sagt",
  region: "ap-southeast-1",
  accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY,
  secretAccessKey: process.env.REACT_APP_AWS_SECRET_KEY,
};

const customSwal2 = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-primary',
  
    },
    buttonsStyling: false
  })
const Action = (props) => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const signRef = useRef()
    const [actions, setActions] = useState([]);
    const [incidentData, setIncidentData] = useState({});
    const [modalState, setModalState] = useState({ type: null, isOpen: false, actionId: null });
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        actionType: { value: null, matchMode: FilterMatchMode.IN },
        createdDate: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }] },
        'actionSubmittedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
    });
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [Search, setSearch] = useState([])
    const [maskId, setMaskId] = useState([])
    const [names, setNames] = useState([])
    const [dates, setDates] = useState([])
    const [showModal, setShowModal] = useState(false)
    const [showItem, setShowItem] = useState([])
    useEffect(() => {
        getActions();
    }, [modalState.isOpen])

    const getActions = async () => {
        const response = await API.get(ACTION_URL);
        if (response.status === 200) {

            if (props.id) {
                const actions = response.data

                    .filter(i => i.application === props.application && (i.objectId === props.id) && i.status !== 'completed')
                    .reverse()
                setActions(actions);
                setSearch(actions)

                const modifiedArray = actions.map(item => ({
                    name: item.applicationDetails.maskId ? item.applicationDetails.maskId : item.applicationDetails.docId,
                    value: item.applicationDetails.maskId ? item.applicationDetails.maskId : item.applicationDetails.docId // This adds the 
                }));
                let pp = modifiedArray.filter((ele, ind) => ind === modifiedArray.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setMaskId(pp)
                const name = actions.map(item => ({

                    name: item.actionSubmittedBy.firstName,
                    value: item.actionSubmittedBy.firstName // This adds the 
                }));
                let pp1 = name.filter((ele, ind) => ind === name.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setNames(pp1)
                const date = actions.map(item => ({

                    name: item.createdDate,
                    value: item.createdDate  // This adds the 
                }));
                let pp2 = date.filter((ele, ind) => ind === date.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setDates(pp2)
            } else {
                const actions = response.data.filter(i => i.application === 'RA' && i.status !== 'completed').reverse()

                setActions(actions);
                setSearch(actions);

                const modifiedArray = actions.map(item => ({
                    name: item.applicationDetails.meetid,
                    value: item.applicationDetails.meetid // This adds the parameter with the value you specified
                }));
                let pp = modifiedArray.filter((ele, ind) => ind === modifiedArray.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setMaskId(pp)
                const name = actions.map(item => ({
                    name: item.actionSubmittedBy.firstName,
                    value: item.actionSubmittedBy.firstName // This adds the 
                }));
                let pp1 = name.filter((ele, ind) => ind === name.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                console.log(pp1)
                setNames(pp1)
                const date = actions.map(item => ({

                    name: item.createdDate,
                    value: item.createdDate  // This adds the 
                }));
                let pp2 = date.filter((ele, ind) => ind === date.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setDates(pp2)
            }

        }
    }

    const getSubmittedBy = (action) => {
        return action.actionSubmittedBy?.firstName || '';
    };


    const openActionCard = async (action) => {
        // await getReportIncident(action.objectId, action.id);
        // setModalState({ type: action.actionType, isOpen: true, actionId: action.id });
        setShowItem(action);
        setShowModal(true)
    };



    const getReportIncident = async (id, actionId) => {

        const uriString = {
            include: [
                'locationOne',
                'locationTwo',
                'locationThree',
                'locationFour',
                'locationFive',
                'locationSix',
                'lighting',
                'surfaceCondition',
                'surfaceType',
                'workActivityDepartment',
                'workActivity',
                'reporter',
                'workingGroup',
                'weatherCondition',
                'reviewer',
                'drivers',
                'surveyor',
                'estimator',
                'trainee',
                'gmOps',
                'thirdParty',
                'security',
                'costReviewer',
                'financer',
                'dutyEngManager',
                'incidentTypeName'
            ]
        };

        const url = `${AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;

            data.evidence = response.data.evidence ? response.data.evidence.map(i => {
                return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
            }) : []

            data.actionId = actionId;
            setIncidentData(data)

        }
    }
    const actionTypeToDisplay = {
        'air_reviewer': 'IR - Review',
        'air_investigator': 'IR - Initiate Investigation',
        'air_cost_estimator': 'IR - Estimate Cost',
        'air_gmops_review': 'IR - Punitive / Correction Action Taker Review',
        'air_trainee': 'IR - Trainee Actions',
        'air_duty_manager': 'IR - Duty Engineer Manager',
        'air_cost_reviewer': 'IR - Finance Settlement',
        'air_finance': 'IR - Claims Coordinator',
        'air_surveyor': 'IR - Survey',
        'air_medical_officer': 'IR - Medical Report',
        'air_medical_approve': 'IR - Medical Report Approve',
        'air_engineer': 'IR - Engineer Review',
        'take_investigation_actions': 'IR - Take Actions',
        'verify_actions': 'IR - Verify Actions',
        'retake_actions': 'IR - Retake Actions',
        'air_report_work': 'IR - Medical Report Work',
        'air_hod_review': 'IR - HOD Review',
        'air_hod_finance': 'IR - HOD Review Before Finance Submission'
        // Add other actionType to display string mappings as needed
    };

    // const modalMapping = {
    //     'air_reviewer': AirReviewerCard,
    //     'air_investigator': AirInvestigationCard,
    //     'air_cost_estimator': AirCostEstimatorCard,
    //     'air_gmops_review': AirGmOpsCard,
    //     'air_trainee': AirTraineeCard,
    //     'air_duty_manager': AirDutyManagerCard,
    //     'air_cost_reviewer': AirCostReviewerCard,
    //     'air_finance': AirFinancerCard,
    //     'air_surveyor': AirSurveryorCard,
    //     'air_medical_officer': AirMedicalCard,
    //     'air_medical_approve': AirMedicalApproveCard,
    //     'air_engineer': AirEngineerCard,
    //     'take_investigation_actions': TakeActionModal,
    //     'verify_actions': VerifyActionModal,
    //     'retake_actions': TakeActionModal,
    //     'air_report_work': AirMedicalWorkReportCard,
    //     'air_hod_review': AirHodCard,
    //     'air_hod_finance': AirHodFinanceReviewCard
    //     // Add other actionType to modal mappings as needed
    // };
    // const renderModal = () => {
    //     if (!modalState.isOpen) return null;
    //     const ModalComponent = modalMapping[modalState.type];
    //     let data = incidentData;
    //     if (modalState.type === 'take_investigation_actions' || modalState.type === 'verify_actions' || modalState.type === 'retake_actions') {
    //         data = actions.find(i => i.id === modalState.actionId)
    //     }


    //     return ModalComponent ? <ModalComponent showModal={modalState.isOpen} applicationType={data.application} setShowModal={(isOpen) => setModalState({ ...modalState, isOpen })} setData={setIncidentData} data={data} /> : null;
    // };

    const onDateSearch = () => {
        const [from, to] = [startDate, endDate];
        if (from === null && to === null) return true;
        if (from !== null && to === null) return true;
        if (from === null && to !== null) return true;
        const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
        const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

        //  console.log(start,end)
        const searchData = Search.filter(item => isBetweenDateRange(item.createdDate, start, end))

        setActions(searchData)

        // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
    }
    const isBetweenDateRange = (dateString, date1, date2) => {
        // Parse the date strings using Moment.js
        const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

        // Check if the parsed date is between date1 and date2
        return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
    }

    // const columns = [

    //     {
    //         field: 'applicationDetails.maskId',
    //         title: 'ID',
    //         render: (row) => {
    //             return <div className='maskid' onClick={() => openActionCard(row)}><span className='pending'></span>{row.applicationDetails.maskId}</div>
    //         }


    //     },

    //     {
    //         field: '',
    //         title: 'Description',
    //         render: (row) => {
    //             return actionTypeToDisplay[row.actionType] || row.actionType;
    //         }


    //     },
    //     {
    //         field: 'createdDate',
    //         title: 'Submitted on',



    //     },


    //     {
    //         field: '',
    //         title: 'Submitted By',
    //         render: (row) => {
    //             return getSubmittedBy(row);
    //         }


    //     },


    // ];
    // const tableActions = [

    //     {
    //         icon: 'visibility',
    //         tooltip: 'View Incident',
    //         onClick: (event, rowData) => {

    //             openActionCard(rowData)
    //             // Do save operation
    //             // console.log(rowData)
    //             // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
    //         }
    //     },
    // ]

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
                {/* <div className="">
                    <span className='me-3'>Month Filter :</span>
                    <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
                    <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

                    <Button icon="pi pi-search " className='me-3' rounded text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} />
                    <Button icon="pi pi-times " rounded text raised severity="danger" aria-label="Cancel" onClick={() => { setActions(Search); setStartDate(null); setEndDate(null) }} />

                </div> */}
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span>
            </div>
        );
    };

    const header = renderHeader();
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };
    const descBodyTemplate = (row) => {
        return 'Confirm my participation in this Risk Assessment as a team member';
    }
    const nameBodyTemplate = (row) => {
        return String(row.actionSubmittedBy.firstName);
    }
    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openActionCard(row)}><span className='pending'></span>{row.applicationDetails.meetid}</div>;
    }
    const submitBodyTemplate = (row) => {
        return getSubmittedBy(row);
    }
    const maskIdFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">ID Picker</div>
                <MultiSelect value={options.value} options={maskId} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const descFilterTemplate = (options) => {
        return (
            <div className='d-flex justify-content-end'>
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={options.value} onChange={(e) => options.filterCallback(e.value)} placeholder="Search" />
                </span>
            </div>
        )
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const nameFilterTemplate = (options) => {
        console.log(options)
        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Name</div>
                <MultiSelect value={options.value} options={names} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const dateFilterTemplate = (options) => {

        // return (

        //     <React.Fragment>
        //         <div className="mb-3 font-bold">Date</div>
        //         <MultiSelect value={options.value} options={dates} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
        //     </React.Fragment>
        // );
        return <Calendar value={options.value} onChange={(e) => options.filterCallback(e.value, options.index)} dateFormat="mm/dd/yy" placeholder="mm/dd/yyyy" mask="99/99/9999" />;
    }

    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
          dw.setUint8(i, byteString.charCodeAt(i));
        }
    
        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
      };

    const onConfirm = async () => {

        
        const filename = new Date().getTime() + "member_sign.png";

        if (!signRef.current.isEmpty()) {
          const ReactS3Client = new S3(config);
    
          await ReactS3Client.uploadFile(
            dataURItoFile(
              signRef.current.getTrimmedCanvas().toDataURL("image/png"),
              filename
            ),
            "uploads/risk_sign/" + filename
          )
            .then((data) => console.log(data))
            .catch((err) => console.error(err));
        }else {
            customSwal2.fire(
                'Please Sign ..!',
                '',
                'warning'
              )
        }

     


        const updatedCartItems = showItem.applicationDetails.teamMemberInvolved.map(item => {
            if (item.id === user.id) {
              return { ...item, sign: filename, date: moment().format('YYYY-MM-DD HH:mm') }; // Increment quantity
            }
            return item;
          });
      
    //   updatedCartItems
      
    if (!signRef.current.isEmpty()) {

        const response = await API.patch(RA_ACTION_WITH_ID(showItem.applicationDetails.id), {
            actionId: showItem.id,
            teamMemberInvolved:updatedCartItems,

        })
        if (response.status === 204) {
            customSwal2.fire(
                'Risk Assessment Updated!',
                '',
                'success'
              )
              getActions();
              setShowModal(false)
             
    }
}

    }

    return (

        <>


            <DataTable value={actions} paginator rows={10} globalFilterFields={["applicationDetails.maskId", "createdDate", "actionSubmittedBy.firstName"]} header={header} filters={filters} onFilter={(e) => { if (e.filters['actionSubmittedBy.firstName'] !== undefined) { e.filters['actionSubmittedBy.firstName'].matchMode = 'in'; setFilters(e.filters) } else { setFilters(e.filters) } }}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                <Column field="applicationDetails.meetid" header="ID" body={idBodyTemplate} sortable style={{ width: '15%' }} filterElement={maskIdFilterTemplate} ></Column>
                
                <Column field="applicationDetails.activity" header="Process/Activity" sortable style={{ width: '25%' }}  ></Column>
                
                <Column field="applicationDetails.department" header="Department" sortable style={{ width: '15%' }}  ></Column>

                <Column field='actionType' header="Description" body={descBodyTemplate} style={{ width: '25%' }}></Column>

                <Column field="createdDate" header="Submitted On" ></Column>

                <Column field="applicationDetails.captain" header="Ra Leader" filter showFilterMatchModes={false} filterElement={nameFilterTemplate} sortable filterPlaceholder="Search" ></Column>

            </DataTable>
            <Modal
                show={showModal}
                size="md"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0"> Please Confirm</h2>
                        <div className="text-center mt-0">

                        </div>
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className={`animate-col-md-12 text-center`}>
                            <p>I confirm my participation in this Risk Assessment as a team member. The outcome reflects our shared professional judgment to the best of our abilities through consensus.</p>
                            <SignatureCanvas
                                penColor="#1F3BB3"
                                canvasProps={{
                                    width: 350,
                                    height: 100,
                                    className: "sigCanvas",
                                    style: {
                                        boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                    },
                                }}
                                ref={signRef}


                            />  <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer className="flex-wrap">



                    <Button onClick={onConfirm}>Done</Button>
                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>
            {/* {renderModal()} */}
        </>
    );


}

export default Action;
