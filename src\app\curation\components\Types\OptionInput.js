import React, { Component } from "react";
import * as _ from "lodash";

class OptionInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tab: "",
      multiple: false,
      toolType: "OPTION_INPUT",
      title: "",
      defaultValue: "",
      validation: {
        isRequired: false,
      },
      radios: [],
      duplicate: false,
      isEmpty:false
    };
    this.removeOption = this.removeOption.bind(this);
  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue(stateFor, value) {
    switch (stateFor) {
      case "TITLE":
        this.setState({ title: value });
        break;

      case "DEFAULT_VALUE":
        this.setState({ defaultValue: value });
        break;
      case "IS_REQUIRED":
        this.setState({
          validation: { ...this.state.validation, isRequired: value },
        });
        break;

      case "MULTIPLE":
        let radios = this.state.radios;
            radios.forEach(ele =>{
                return ele.selected =false
            })
            this.setState({
                radios : radios,
                multiple : value
            });
        break;
      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  removeOption(index) {
    let radios = this.state.radios;
    radios.splice(index, 1);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  render() {
    return (
      <div className="card mb-3" style={this.state.title ==='' && this.props.field.isEmpty ?{boxShadow: '0px 0px 12px 3px #dfdfdf',border:'1px solid red',borderRadius:0}:{boxShadow: '0px 0px 12px 3px #dfdfdf',borderRadius:0}}>
        <div className="card-header d-flex justify-content-between">
          <div className="">
          <i className="fa fa-list-alt  mr-1"></i> Option Input
          </div>
         
          <div className="">
          {this.props.index !== 0 ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveUp(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-up"></i>
                  </span>
                  : ""}
                {this.props.index !== this.props.length ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveDown(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-down"></i>
                  </span>
                  : ''}
                <span
                  className=""
                  onClick={() => this.props.removeField(this.props.index)}
                >
                  <i className="mdi mdi-close"></i>
                </span>
              </div>
        </div>
        <div className="card-body">
          {/* <ul className="nav nav-tabs">
            <li className="nav-item">
              <a
                onClick={(e) => {
                  e.preventDefault();
                  this.setState({ tab: "general" });
                }}
                className={
                  this.state.tab === "general" ? "nav-link active" : "nav-link"
                }
                href="/general"
              >
                General
              </a>
            </li>

            <li className="nav-item">
              <a
                onClick={(e) => {
                  e.preventDefault();
                  this.setState({ tab: "options" });
                }}
                className={
                  this.state.tab === "options" ? "nav-link active" : "nav-link"
                }
                href="/options"
              >
                Options
              </a>
            </li>
            <li
              className="nav-item"
              style={{
                textAlign: "right",
                position: "absolute",
                right: "15px",
              }}
            >
              <a
                onClick={(e) => {
                  e.preventDefault();
                  this.setState({ tab: "" });
                }}
                className={
                  this.state.tab === ""
                    ? "nav-link active font-weight-bold"
                    : "nav-link"
                }
                href="/hide"
              >
                -
              </a>
            </li>
          </ul> */}
       
            <div className="card-body">
              <div className="row">
                <div className="col-12">
                  <div className="form-group">
                    <label htmlFor="title">Question</label>
                    <textarea
                      value={this.state.title}
                      onChange={(e) =>
                        this.changeValue("TITLE", e.target.value)
                      }
                      className="form-control"
                    ></textarea>
                  </div>
                </div>
              </div>

              
              <div className="form-check">
                <input
                  defaultChecked={this.state.multiple}
                  onChange={(e) =>
                    this.changeValue("MULTIPLE", e.target.checked)
                  }
                  className="form-check-input"
                  type="checkbox"
                  id="multiple"
                />
                <label className="form-check-label" htmlFor="isRequired">
                  Multiple Selection
                </label>
              </div>
              <div className="form-check">
                <input
                  defaultChecked={this.state.validation.isRequired}
                  onChange={(e) =>
                    this.changeValue("IS_REQUIRED", e.target.checked)
                  }
                  className="form-check-input"
                  type="checkbox"
                  id="isRequired"
                />
                <label className="form-check-label" htmlFor="isRequired">
                  Required
                </label>
              </div>

              
           
              <p
                hidden={!this.state.duplicate}
                className="alert text-center alert-danger"
              >
                <strong>Duplicate</strong> Values Found
              </p>
              {this.state.radios ? (
                <table className="table text-center">
                  <tbody>
                    {this.state.radios.map((checkbox, index) => {
                      return (
                        <tr key={index}>
                          {this.state.multiple ? (
                            <td hidden={true} style={{ verticalAlign: "middle" }}>
                              {/* <div className="radio">
                                {
                                  <input
                                    defaultChecked={this.state.radios[index].selected}
                                    onChange={(e) =>
                                      this.changeOptionValue(
                                        index,
                                        e.target.checked,
                                        "SELECTED"
                                      )
                                    }
                                    type="checkbox"
                                  />
                                }
                              </div> */}
                            </td>
                          ) : (
                            <td hidden={true}></td>
                          )}
                         
                          <td>
                            <input
                              placeholder="Value"
                              value={this.state.radios[index].value}
                              onChange={(e) =>
                                this.changeOptionValue(
                                  index,
                                  e.target.value,
                                  "VALUE"
                                )
                              }
                              id={checkbox.value}
                              type="text"
                              className="form-control"
                            />
                          </td>
                          {!this.state.multiple ? (
                            <td hidden={true} style={{ verticalAlign: "middle" }}>
                            {/* <input
                                name="default"
                                defaultChecked={this.state.radios[index].selected}
                                onChange={(e) =>
                                  this.changeOptionValue(
                                    index,
                                    e.target.checked,
                                    "SELECTED"
                                  )
                                }
                                id={checkbox.value}
                                type="radio"
                              /> */}
                            </td>
                          ) : (
                            <td hidden={true}></td>
                          )}
                          <td style={{ verticalAlign: "middle" }}>
                            <i
                              onClick={() => this.removeOption(index)}
                              className="mdi mdi-close pull-right"
                            >
                             
                            </i>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              ) : (
                <span></span>
              )}
              <button
                onClick={() => this.addOption()}
                className="btn form-control btn-sm btn-dark"
              >
                Add Option
              </button>
            </div>
          
        </div>
     
      </div>
    );
  }

  changeOptionValue(index, value, state) {
    let radios = this.state.radios;
    let radio = {};
    if (state === "DEFAULT_VALUE") {
      this.setState({
        defaultValue: index,
      });
    }
    if (state === "TITLE") {
      radio = {
        ...radios[index],
        title: value,
      };
    } else if (state === "SELECTED") {
      if(this.state.multiple){ 
        radio = {
            ...radios[index],
            selected: !radios[index].selected
        }
    }else{
        radios.forEach(ele =>{
            return ele.selected =false
        })
        radio = {
            ...radios[index],
            selected: !radios[index].selected
        }
    }
    } else if (state === "VALUE") {
      radio = {
        ...radios[index],
        value: value,
      };
    } else {
      radio = {
        ...radios[index],
      };
    }

    radios[index] = radio;
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  duplicates() {
    let radios = this.state.radios;
    let u = _.uniqBy(radios, "value");
    if (!_.isEqual(radios, u)) {
      this.setState({
        duplicate: true,
      });
    } else {
      this.setState({
        duplicate: false,
      });
    }
  }

  addOption() {
    let radio = {

      value: "",
      selected: false,
    };
    let radios = this.state.radios;
    radios.push(radio);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }
}

export default OptionInput;
