import React, { useState, useEffect, useRef } from "react";
import { Mo<PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import Step<PERSON>abel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";
import { AIR_DRIVERS_WITH_ID_URL, AIR_SURVEYORS_URL, AIR_WITH_ID_URL, AIR_REVIEW_WITH_ID_URL, AIR_COST_ESTIMATOR_URL, AIR_REVIEW_RETURN_WITH_ID_URL, AIR_MEDICAL_OFFICER_URL, USERS_URL, AIR_ENGINEER_URL, API_URL, GENERAL_GROUP_URL, PUBLIC_AIR_WITH_ID_URL, STATIC_URL, UPLOAD_PDF, AIR_REVIEWER_SUBMIT_WITHOUT_ACTION_WITH_ID_URL, AIR_REVIEWER_RETURN_WITHOUT_ACTION_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import { DropzoneArea } from 'material-ui-dropzone';
import Select from "react-select";
import axios from "axios";
import ThirdPartyForm from "./ThirdPartyForm";
import ImageTooltip from "./ImageTooltip";
import HtmlTooltip from "./HtmlTooltip";
import IncidentEdit from "./IncidentEdit";
import { useHistory } from 'react-router-dom';
import { PictureAsPdf, Delete } from "@mui/icons-material";
import { IRPdf } from "./IRPdf";
import { secondaryPopup } from "../notifications/Swal";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

const AirReviewerCard = ({ showModal, setShowModal, data, setData, setOnSaved }) => {
    const [triggerSubmit, setTriggerSubmit] = useState(false);

    const handleButtonClick = () => {
        setTriggerSubmit(true);
    };



    const history = useHistory();
    // Initialize with empty values so user must make a selection
    const [yValue, setYValue] = useState("");
    const [xValue, setXValue] = useState("");
    const [incidentMatrix, setIncidentMatrix] = useState({ item: "", color: "" });
    const [returnStatus, setReturnStatus] = useState('')
    const [disabled, setDisabled] = useState(false);
    const [selectedEvidence, setSelectedEvidence] = useState([]);
    const handleImageToggle = (url) => {
        if (selectedEvidence.includes(url)) {
            setSelectedEvidence(prevImages => prevImages.filter(img => img !== url));
        } else {
            setSelectedEvidence(prevImages => [...prevImages, url]);
        }
    }

    const [isContinue, setIsContinue] = useState(true)
    const onSaved = (flag) => {
        if (flag) {
            getReportIncident(data.id)
            setActiveStep((prevActiveStep) => prevActiveStep + 1);
            setIsContinue(false)
        }
    }

    // Function to update xValue and yValue based on incidentRating input
    const updateValuesBasedOnInput = (input) => {
        // Return early if input is invalid
        if (!input || !input.item || !input.color) {
            // Don't set any values if input is invalid
            return;
        }

        const matrix = {
            "No any injury": {
                0: { item: "C0H0", color: "green" },
                1: { item: "C1H0", color: "yellow" },
                2: { item: "C2H0", color: "orange" },
                3: { item: "C3H0", color: "red" },
            },
            "Human 1st Aid /Potential Medical Treatment/MT": {
                0: { item: "C0H1", color: "yellow" },
                1: { item: "C1H1", color: "yellow" },
                2: { item: "C2H1", color: "orange" },
                3: { item: "C3H1", color: "red" },
            },
            "Potential LTI/LTI": {
                0: { item: "C0H2", color: "orange" },
                1: { item: "C1H2", color: "orange" },
                2: { item: "C2H2", color: "orange" },
                3: { item: "C3H2", color: "red" },
            },
            "Permanent Disability/ Potential Fatal/ Fatal": {
                0: { item: "C0H3", color: "red" },
                1: { item: "C1H3", color: "red" },
                2: { item: "C2H3", color: "red" },
                3: { item: "C3H3", color: "red" },
            },
        };

        let foundXValue = ""; // Start with empty values
        let foundYValue = "";

        // Iterate over each yValue (keys of the matrix)
        for (const yValue in matrix) {
            // Check each xValue within this yValue category
            for (const xValue in matrix[yValue]) {
                const currentEntry = matrix[yValue][xValue];

                // Compare currentEntry with input
                if (currentEntry.item === input.item && currentEntry.color === input.color) {
                    foundXValue = xValue;
                    foundYValue = yValue;
                    break; // Break out of the inner loop
                }
            }

            // If found, break out of the outer loop
            if (foundXValue !== "0" || foundYValue !== "No any injury") {
                break;
            }
        }

        // Update the state values
        setXValue(foundXValue);
        setYValue(foundYValue);
    };

    useEffect(() => {
        const matrix = {
            "": {
                "": { item: "", color: "" },
                0: { item: "", color: "" },
                1: { item: "", color: "" },
                2: { item: "", color: "" },
                3: { item: "", color: "" }
            },
            "No any injury": {
                "": { item: "", color: "" },
                0: { item: "C0H0", color: "green" },
                1: { item: "C1H0", color: "yellow" },
                2: { item: "C2H0", color: "orange" },
                3: { item: "C3H0", color: "red" },
            },
            "Human 1st Aid /Potential Medical Treatment/MT": {
                "": { item: "", color: "" },
                0: { item: "C0H1", color: "yellow" },
                1: { item: "C1H1", color: "yellow" },
                2: { item: "C2H1", color: "orange" },
                3: { item: "C3H1", color: "red" },
            },
            "Potential LTI/LTI": {
                "": { item: "", color: "" },
                0: { item: "C0H2", color: "orange" },
                1: { item: "C1H2", color: "orange" },
                2: { item: "C2H2", color: "orange" },
                3: { item: "C3H2", color: "red" },
            },
            "Permanent Disability/ Potential Fatal/ Fatal": {
                "": { item: "", color: "" },
                0: { item: "C0H3", color: "red" },
                1: { item: "C1H3", color: "red" },
                2: { item: "C2H3", color: "red" },
                3: { item: "C3H3", color: "red" },
            },
        };

        // Only set incidentMatrix if both xValue and yValue are valid
        if (yValue && xValue && matrix[yValue] && matrix[yValue][xValue]) {
            setIncidentMatrix(matrix[yValue][xValue]);
        } else {
            // If either value is missing or invalid, set incidentMatrix to empty
            setIncidentMatrix({ item: "", color: "" });
        }
    }, [xValue, yValue])


    const [showNext, setShowNext] = useState(false);
    const [shortDescription, setShortDescription] = useState('')

    const rejectButtonRef = useRef(null);
    const submitButtonRef = useRef(null);

    const [rejectModal, setRejectModal] = useState(false)
    const handleRejectModal = () => {
        setRejectModal(true)
    }
    const handleReject = async () => {

        setRejectModal(false)
        if (disabled) return;

        if (rejectButtonRef.current) {
            rejectButtonRef.current.disabled = true;
        }

        setDisabled(true)

        try {

            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_REVIEWER_RETURN_WITHOUT_ACTION_WITH_ID_URL(data.id), {
                status: 'returned',
                returnStatus: returnStatus,
                returnComments: returnComments
            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }


            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            // Proceed to the next step
            setActiveStep((prevActiveStep) => prevActiveStep + 1);
            if (rejectButtonRef.current) {
                rejectButtonRef.current.disabled = false;
            }

            setDisabled(false)
            setShowModal(false)

        } catch (error) {
            console.error('An error occurred:', error);
            if (rejectButtonRef.current) {
                rejectButtonRef.current.disabled = false;
            }

            setDisabled(false)
            setShowModal(false)

        }
    }

    const handleContinue = () => {
        setShowNext(true)
    }

    const [surveyor, setSurveyor] = useState([])
    useEffect(() => {
        getSurveyor()
    }, [])

    const getSurveyor = async () => {
        const response = await API.post(AIR_SURVEYORS_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setSurveyor(response.data)
        }
    }

    const [estimator, setEstimator] = useState([])
    useEffect(() => {
        getEstimator()
    }, [])

    const getEstimator = async () => {
        const response = await API.post(AIR_COST_ESTIMATOR_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setEstimator(response.data)
        }
    }

    const [medicalOfficer, setMedicalOfficer] = useState([])
    useEffect(() => {
        getMedicalOfficer()
    }, [])

    const getMedicalOfficer = async () => {
        const response = await API.post(AIR_MEDICAL_OFFICER_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setMedicalOfficer(response.data)
        }
    }

    const [engineer, setEngineer] = useState([])
    useEffect(() => {
        getEngineer()
    }, [])

    const getEngineer = async () => {
        const response = await API.post(AIR_ENGINEER_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setEngineer(response.data)
        }
    }

    const [selectedSurveyor, setSelectedSurveyor] = useState('');
    const [selectedEstimator, setSelectedEstimator] = useState('');
    const [selectedMedicalOfficer, setSelectedMedicalOfficer] = useState('');
    const [selectedEngineer, setSelectedEngineer] = useState('');

    const [immediateAction, setImmediateAction] = useState(false);
    const [majorAccident, setMajorAccident] = useState(false);
    const [medicalPersonal, setMedicalPersonal] = useState(false);
    const [inspectionRequired, setInspectionRequired] = useState(false);
    const [cll, setCll] = useState(false);
    const [files, setFiles] = useState([]);
    const [investigationDate, setInvestigationDate] = useState('');


    const [users, setUsers] = useState([])
    useEffect(() => {
        getUsers()
    }, [])

    const getUsers = async () => {
        const response = await API.get(USERS_URL);
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    const handleUserSelectChange = (selectedOptions) => {
        setSelectedUsers(selectedOptions);
    };

    const [selectedUsers, setSelectedUsers] = useState([]);



    const handleFileChange = (file) => {
        setFiles(file)

    }

    const [drivers, setDrivers] = useState({
        driverDetails: [
            {
                name: "",
                vehicleNo: "",
                licenseNo: "",
                employeeId: "",
                ban: false
            }
        ]
    })

    const addDriver = () => {
        setDrivers(prevState => ({
            ...prevState,
            driverDetails: [...prevState.driverDetails, {
                name: "",
                vehicleNo: "",
                licenseNo: "",
                employeeId: "",
                ban: false
            }],
        }));
    };

    const handleDriver = (index, field, value) => {
        setDrivers(prevState => {
            const updatedActions = [...prevState.driverDetails];
            updatedActions[index][field] = value;
            return { ...prevState, driverDetails: updatedActions };
        });
    };

    const handleDeleteDriver = (index) => {
        const newDrivers = [...drivers.driverDetails];
        newDrivers.splice(index, 1);
        setDrivers(prevState => ({ ...prevState, driverDetails: newDrivers }));
    };

    const [generalGroups, setGeneralGroups] = useState([]);
    const [thirdParty, setThirdParty] = useState(null);

    const [insuranceNotify, setInsuranceNotify] = useState('')


    // const updateForms = (forms) => {
    //     setThirdParty(forms)
    // }
    const [actionTaken, setActionTaken] = useState('');

    const [isEdit, setIsEdit] = useState(false);
    const handleEdit = () => {

        setIsEdit(true);

    }

    function CustomThumbnail(props) {
        // This would render your custom thumbnail, which displays the file name
        // You might have to use the actual file to create a thumbnail (e.g., for images) or use a placeholder for other file types like PDF
        const { file } = props;

        return (
            <div style={{ position: 'relative' }}>
                {/* This is a placeholder; you'd use the actual file to render a thumbnail */}
                <img src={URL.createObjectURL(file)} alt={file.name} style={{ width: '100px', height: '100px' }} />
                <div style={{ position: 'absolute', bottom: 0, background: 'rgba(0, 0, 0, 0.6)', color: 'white', width: '100%', textAlign: 'center' }}>
                    {file.name}
                </div>
            </div>
        );
    }

    const handleDelete = (fileName) => {
        // Filter out the file you want to delete from the current files array
        const updatedFiles = files.filter(file => file.name !== fileName);

        // Update your state with the new list (Assuming you're using useState to manage files)
        setFiles(updatedFiles);
    };


    const [selectedTruck, setSelectedTruck] = useState([]);

    const handleCheckboxChange = (value) => {
        if (selectedTruck.includes(value)) {
            setSelectedTruck((prev) => prev.filter(item => item !== value));
        } else {
            setSelectedTruck((prev) => [...prev, value]);
        }
    };
    const updateForms = (forms) => {
        setThirdParty(forms)
    }

    const steps = [
        {
            label: 'Basic Information',
            description: (
                <>
                    <IncidentEdit triggerSubmit={triggerSubmit} onSaved={onSaved} data={data} />
                </>
            )
        },
        {
            label: 'Review IR',
            description: (<>



                <div className="row align-items-center my-3">
                    <div className="col-md-12 p-1">
                        <label>Header Information</label>
                        <input type="text" maxLength={100} value={shortDescription} onChange={(e) => { setShortDescription(e.target.value); }} placeholder={'Write IR Header...'} className="form-control" />
                    </div>
                </div>
                <div className="row align-items-center my-3">
                    <label htmlFor="chRankingY" className="col-form-label col-md-12 p-1">C/H Ranking
                        <ImageTooltip imageUrl={require("../../assets/images/im.png")}>
                            <i className="mdi mdi-information cursor-pointer"></i>
                        </ImageTooltip>
                    </label>

                    <div className="col-md-4 p-1">

                        <label>Human Risk</label>
                        <select
                            id="chRankingY"
                            value={yValue}
                            className="form-select mb-0"
                            onChange={e => setYValue(e.target.value)}
                        >
                            <option value="">Choose...</option>
                            <option value="No any injury">No any injury</option>
                            <option value="Human 1st Aid /Potential Medical Treatment/MT">
                                Human 1st Aid /Potential Medical Treatment/MT
                            </option>
                            <option value="Potential LTI/LTI">Potential LTI/LTI</option>
                            <option value="Permanent Disability/ Potential Fatal/ Fatal">
                                Permanent Disability/ Potential Fatal/ Fatal
                            </option>
                        </select>
                    </div>

                    <div className="col-md-4 p-1">
                        <label>Incident Cost</label>
                        <select
                            id="chRankingX"
                            value={xValue}
                            className="form-select mb-0"
                            onChange={e => setXValue(e.target.value)}
                        >
                            <option value="">Choose...</option>
                            <option value="0">No any property damage</option>
                            <option value="1">Less than LKR0.25 Mn</option>
                            <option value="2">Between LKR0.25 Mn - LKR 1.5 Mn</option>
                            <option value="3">Over LKR1.5 Mn</option>
                        </select>
                    </div>
                    {/* <div className="col-md-4 p-1">

                        <label>Human Risk</label>
                        <select
                            id="chRankingY"
                            value={yValue}
                            className="form-select mb-0"
                            onChange={e => setYValue(e.target.value)}
                        >
                            <option value="">Choose...</option>
                            <option value="No any injury">No any injury</option>
                            <option value="Human 1st Aid /Potential Medical Treatment/MT">
                                Human 1st Aid /Potential Medical Treatment/MT
                            </option>
                            <option value="Potential LTI/LTI">Potential LTI/LTI</option>
                            <option value="Permanent Disability/ Potential Fatal/ Fatal">
                                Permanent Disability/ Potential Fatal/ Fatal
                            </option>
                        </select>
                    </div> */}



                    <div className="col-md-4 p-1">
                        {incidentMatrix.color && incidentMatrix.item && (
                            <>
                                <label className="">Incident Rating</label>
                                <div
                                    style={{ backgroundColor: incidentMatrix.color, height: '37.6px' }}
                                    className={`d-flex align-items-center justify-content-center ${incidentMatrix.color === 'yellow' ? 'text-black' : 'text-white'}`}
                                >
                                    {incidentMatrix.item ? incidentMatrix.item.replace(/C(\d+)H(\d+)/, "H$2C$1") : ""}
                                </div>
                            </>

                        )}
                    </div>
                </div>

                <div className="row">

                    <label htmlFor="" className='m-0 me-3'>Choose upto 4 images</label>
                    {data.evidence.map(url => (
                        <div key={url.src} className="col-md-4 mb-3">
                            <div className="card">
                                <div className="form-check">
                                    <input
                                        className="form-check-input img-checkbox"
                                        type="checkbox"
                                        checked={selectedEvidence.includes(url.src)}
                                        onChange={() => handleImageToggle(url.src)}
                                    />

                                </div>
                                <img src={url.src} alt="Preview" className="card-img-top" />

                            </div>
                        </div>
                    ))}

                </div>
                <div className='row'>
                    <div className='col'>
                        <label htmlFor="" className='m-0 me-3'>Attach classified statements / documents</label>
                        <div className='form-group'>

                            <DropzoneArea
                                acceptedFiles={[
                                    'application/pdf',
                                    'image/jpeg',
                                    'image/png',
                                    'video/mp4'

                                ]}
                                dropzoneText={"Drag and Drop Classified Statements / documents"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                PreviewComponent={CustomThumbnail}
                                onChange={handleFileChange}
                                showPreviews={false}
                                showPreviewsInDropzone={false}
                            />
                            <div className="container mt-3">
                                <div className="row">
                                    {files.map(file => (
                                        <div key={file.name} className="col-md-4 mb-3">
                                            <div className="position-relative">
                                                {file.type === 'application/pdf' ? (
                                                    <PictureAsPdf style={{ width: '100%', fontSize: '100px' }} />
                                                ) : (
                                                    <img src={URL.createObjectURL(file)} alt="Preview" className="mx-auto d-block" style={{ width: '100%', height: '150px' }} />
                                                )}
                                                <button onClick={() => handleDelete(file.name)} className="btn btn-sm btn-danger position-absolute top-0 end-0">
                                                    <Delete />
                                                </button>
                                            </div>
                                            <p className="text-center mt-2">{file.name}</p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <div className='row'>
                    <div className='col'>
                        <div className='form-group d-flex align-items-center'>
                            <label htmlFor="" className='m-0 me-3'>Any immediate actions required?</label>

                            <Switch onChange={(value) => setImmediateAction(value)} checked={immediateAction} />
                        </div>
                    </div>
                </div>
                {
                    immediateAction && (
                        <>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="">Actions Taken</label>
                                        <input type='text' value={actionTaken} onChange={(e) => setActionTaken(e.target.value)} className='form-control' />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Does these truck to be held under terminal custody?</label>

                                        <Switch onChange={(value) => setCll(value)} checked={cll} />
                                    </div>
                                </div>
                            </div>

                            {data.truckDetails && <div className=''>
                                <div className=''>
                                    <div className='form-group'>
                                        <label htmlFor="" className='m-0 me-3'>Please select the truck(s) to be held: </label>
                                        <div className="list-group">
                                            {data.truckDetails.split(',').map((item, index) => (
                                                <div key={index} className="col">
                                                    <div className="form-check">
                                                        <input
                                                            className="form-check-input"
                                                            type="checkbox"
                                                            value={item}
                                                            id={`checkbox-${index}`}
                                                            checked={selectedTruck.includes(item)}
                                                            onChange={() => handleCheckboxChange(item)}
                                                        />
                                                        <label className="form-check-label" htmlFor={`checkbox-${index}`}>
                                                            {item}
                                                        </label>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                    </div>
                                </div>
                            </div>
                            }
                        </>
                    )
                }
                <div className='row'>
                    <label htmlFor="" className='m-0 me-3'>Notify Insurance / Surveyor
                        <HtmlTooltip content={`
    <h4>TT - Club</h4>
    <p>Deductibles:</p>
    <ul>
    <li>Prime Movers  USD 10,000
    <li>Cargo                   USD 15,000
    <li>Ship Impact        USD 50,000
    <li>Other losses       USD 100,000
    </ul>

    <h4>Fair First</h4>
    <p>
    Damages to PMs exceeding LKR 250,000/- and less than LKR 1,500,000/-
    </p>
  `}>
                            <i className="mdi mdi-information cursor-pointer"></i>
                        </HtmlTooltip>
                    </label>

                    <select onChange={(e) => setInsuranceNotify(e.target.value)} className="form-select mb-5">
                        <option value="">Choose</option>
                        <option value="TT Club">TT Club</option>
                        <option value="Fair First">Fair First</option>
                    </select>




                </div>

                <div className='row'>
                    <label htmlFor="" className='m-0 me-3'>Third Party Notification</label>

                    <ThirdPartyForm updateForms={updateForms} notification={true} />




                </div>


                {/*
                        <div className='row'>
                            <div className='col'>
                                <div className='form-group d-flex align-items-center'>
                                    <label htmlFor="" className='m-0 me-3'>Is this a major incident?</label>

                                    <Switch onChange={(value) => setMajorAccident(value)} checked={majorAccident} />
                                </div>
                            </div>
                        </div> */}

                {/* <div className='row'>
                            <div className='col'>
                                <div className='form-group d-flex align-items-center'>
                                    <label htmlFor="" className='m-0 me-3'>Does this involve medical person consent?</label>

                                    <Switch onChange={(value) => setMedicalPersonal(value)} checked={medicalPersonal} />
                                </div>
                            </div>
                        </div> */}
                {medicalPersonal && (
                    <>
                        {/* <div className='row'>
                                    <div className='col-12'>
                                        <div className='row'>
                                            <div className='col'>
                                                <div className='form-group'>
                                                    <label>Assign Medical Officer</label>
                                                    <select onChange={(e) => setSelectedMedicalOfficer(e.target.value)} className="form-control">
                                                        <option value="">Choose Medical Officer</option>
                                                        {
                                                            medicalOfficer.map(user => {
                                                                return (
                                                                    <option value={user.id}> {user.firstName} </option>
                                                                )
                                                            })
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> */}
                    </>
                )
                }
                {/* < div className='row'>
                            <div className='col'>
                                <div className='form-group d-flex align-items-center'>
                                    <label htmlFor="" className='m-0 me-3'>is there any inspection required to commence work?</label>

                                    <Switch onChange={(value) => setInspectionRequired(value)} checked={inspectionRequired} />
                                </div>
                            </div>
                        </div> */}
                {
                    inspectionRequired && (
                        <>
                            {/* <div className='row'>
                                        <div className='col-12'>
                                            <div className='row'>
                                                <div className='col'>
                                                    <div className='form-group'>
                                                        <label>Enter Surveyor Email Address</label>
                                                        <input
                                                            type="email"
                                                            placeholder="Enter email address"
                                                            className="form-control"
                                                            onChange={(e) => {setSelectedSurveyor(e.target.value) }}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> */}
                        </>
                    )
                }

                {
                    (data.damagedEquipmentNumber && data.damagedEquipmentNumber.length > 0) && (
                        <>
                            {/* <div className='row'>
                                        <div className='col-12'>
                                            <div className='row'>
                                                <div className='col'>
                                                    <div className='form-group'>
                                                        <label>Assign Engineer</label>
                                                        <select onChange={(e) => setSelectedEngineer(e.target.value)} className="form-control">
                                                            <option value="">Choose Engineer</option>
                                                            {
                                                                engineer.map(user => {
                                                                    return (
                                                                        <option value={user.id}> {user.firstName} </option>
                                                                    )
                                                                })
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> */}
                        </>
                    )
                }


                {/* <div className='row'>
                            <div className='col'>
                                <div className='form-group'>
                                    <label>Assign Cost Estimator</label>
                                    <select onChange={(e) => setSelectedEstimator(e.target.value)} className="form-control">
                                        <option value="">Choose Cost Estimator</option>
                                        {
                                            estimator.map(user => {
                                                return (
                                                    <option value={user.id}> {user.firstName} </option>
                                                )
                                            })
                                        }
                                    </select>
                                </div>
                            </div>
                        </div> */}

            </>





            )
        }
    ]

    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        window.location.reload();
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    const generatePDFandUpload = async () => {
        if (incidentData) {

        }
    }
    const [incidentData, setIncidentData] = useState({})
    const getReportIncident = async (id) => {

        const uriString = {
            include: [
                'locationOne',
                'locationTwo',
                'locationThree',
                'locationFour',
                'locationFive',
                'locationSix',
                'lighting',
                'surfaceCondition',
                'surfaceType',
                'workActivityDepartment',
                'workActivity',
                'reporter',
                'workingGroup',
                'weatherCondition',
                'reviewer',
                'drivers',
                'surveyor',
                'estimator',
                'trainee',
                'gmOps',
                'thirdParty',
                'security',
                'costReviewer',
                'financer',
                'dutyEngManager',
                'medicalOfficer',
                'incidentTypeName'
            ]
        };

        const url = `${AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;
            response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
                return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
            }) : []
            setData(data)


        }
    }

    // Load data when component mounts or data changes
    useEffect(() => {
        if (data) {
            // Only set values if data.incidentRating exists and is valid
            if (data.incidentRating && data.incidentRating.item && data.incidentRating.color) {
                updateValuesBasedOnInput(data.incidentRating);
                setIncidentMatrix(data.incidentRating);
            } else {
                // If no valid incidentRating, leave the fields empty for user to select
                setXValue("");
                setYValue("");
                setIncidentMatrix({ item: "", color: "" });
            }

            setShortDescription(data.shortDescription || '');
            setSelectedEvidence(data.evidence ? data.evidence.map(img => { return img.src }) : []);

            setCll(data.cllInvolved || false);
            setImmediateAction(data.immediateActionRequired || false);
            setActionTaken(data.actionsTaken || '');
            setSelectedTruck(data.bannedTruckDetails ? data.bannedTruckDetails.split(',') : []);
            setInsuranceNotify(data.insuranceTeamSelected || '');
        }
    }, [data]);

    const [returnComments, setReturnComments] = useState('')
    const handleSubmit = async () => {


        if (disabled) return;

        if (submitButtonRef.current) {
            submitButtonRef.current.disabled = true;
        }

        if (!shortDescription) {
            cogoToast.error('Please fill header information!')
            setDisabled(false)
            return;
        }

        if (!shortDescription) {
            cogoToast.error('Please fill header information!')
            setDisabled(false)
            return;
        }

        // Check if both xValue and yValue are present and valid
        if (!xValue || !yValue || xValue === "" || yValue === "") {
            cogoToast.error('Please choose both incident cost and human risk!')
            setDisabled(false)
            return;
        }
        setDisabled(true)
        let popupTitle = 'Warning!';
        let popupText = '';
        let popupIcon = 'warning';

        // Check the contents of selectedEvidence and files arrays
        if (selectedEvidence.length === 0 && files.length === 0) {
            popupText = 'Do you want to continue without selecting photos and attaching classified documents?';
        } else if (selectedEvidence.length === 0) {
            popupText = 'Do you want to continue without selecting photos?';
        } else if (files.length === 0) {
            popupText = 'Do you want to continue without attaching classified documents?';
        }

        if (popupText) {
            const result = await secondaryPopup.fire({
                title: popupTitle,
                text: popupText,
                icon: popupIcon,
                showCancelButton: true,
                confirmButtonText: 'Yes, continue',
                cancelButtonText: 'No, cancel'
            });

            // Check the user's response
            if (!result.isConfirmed) {
                // User cancelled, early exit
                if (submitButtonRef.current) {
                    submitButtonRef.current.disabled = false;
                }
                setDisabled(false);
                return; // Exit the function here
            }
        }
        // Fire the popup with the determined message
        try {

            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {
                const originalNames = fileResponse.data.files.map(file => file.originalname);

                // Check if both xValue and yValue are present and valid
                if (!xValue || !yValue || xValue === "" || yValue === "") {
                    cogoToast.error('Please choose both incident cost and human risk!')
                    setDisabled(false)
                    return;
                }

                // Ensure incidentMatrix is properly set with valid values
                if (!incidentMatrix || !incidentMatrix.item || !incidentMatrix.color) {
                    cogoToast.error('Incident matrix is not properly set. Please select valid incident cost and human risk values.')
                    setDisabled(false)
                    return;
                }
                const response = await API.patch(AIR_REVIEWER_SUBMIT_WITHOUT_ACTION_WITH_ID_URL(data.id), {
                    shortDescription: shortDescription,
                    immediateActionRequired: immediateAction,
                    bannedTruckDetails: selectedTruck.join(', '),
                    isMajorIncident: majorAccident,
                    inspectionRequired: inspectionRequired,
                    surveyorEmail: selectedSurveyor,
                    // investigationDate: investigationDate,
                    thirdPartyForm: thirdParty,
                    cllInvolved: cll,
                    incidentRating: incidentMatrix,
                    documents: originalNames,
                    actionsTaken: actionTaken,
                    estimatorId: selectedEstimator,
                    isMedicalPersonInvolved: medicalPersonal,
                    medicalOfficerId: selectedMedicalOfficer,
                    insuranceTeamSelected: insuranceNotify,
                    evidence: selectedEvidence.map(url => url.split('/').pop()),
                    allEvidence: data.evidence,
                    // investigation: {
                    //     investigationTeam: selectedUsers
                    // },
                    engineerId: selectedEngineer
                });

                // If the patch request fails, no need to proceed further
                if (response.status !== 204) {
                    console.error('Failed to patch data. Status:', response.status);
                    return;  // or handle this error appropriately
                }

                // Sending POST requests for each driver using for...of loop to ensure each request completes before the next
                for (const driver of drivers.driverDetails) {
                    const driverResponse = await API.post(AIR_DRIVERS_WITH_ID_URL(data.id), driver);

                    // Check if the POST request was successful, if not, handle the error
                    if (driverResponse.status !== 200 && driverResponse.status !== 201) {
                        console.error('Failed to post driver data. Status:', driverResponse.status);
                        // Decide if you want to break, continue, or handle this error in another way
                    }
                }
                cogoToast.success(`Action for IR ${data.maskId} Completed`)
                if (submitButtonRef.current) {
                    submitButtonRef.current.disabled = true;
                }

                setDisabled(false)
                // getReportIncident(data.id);
                // Proceed to the next step
                setActiveStep((prevActiveStep) => prevActiveStep + 1);
                setOnSaved(true)
                setShowModal(false)
            }

            // Patch Request to AIR_WITH_ID_URL


        } catch (error) {
            console.error('An error occurred:', error);
            if (submitButtonRef.current) {
                submitButtonRef.current.disabled = true;
            }

            setDisabled(false)
            setShowModal(false)

        }
        setDisabled(false)
    };
    const handleStatusChange = (event) => {
        setReturnStatus(event.target.value);
    }
    return (
        <>

            <Modal
                show={rejectModal}
                size="sm"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >

                <Modal.Body>
                    <div>
                        <label className="mb-2">
                            <input
                                className="me-2"
                                type="radio"
                                value="edit"
                                name="returnStatus"
                                checked={returnStatus === 'edit'}
                                onChange={handleStatusChange}
                            />
                            Request Reporter to Edit and Submit Again</label>
                    </div>
                    <div>
                        <label>
                            <input
                                type="radio"
                                className="me-2"
                                value="reject"
                                name="returnStatus"
                                checked={returnStatus === 'reject'}
                                onChange={handleStatusChange}
                            />
                            Direct the Report to an Alternate Reviewer</label>
                    </div>
                    <div>
                        <textarea className="form-control" onChange={(e) => setReturnComments(e.target.value)} placeholder={'Enter Comments...'} value={returnComments}></textarea>
                    </div>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    <Button variant="primary" onClick={() => { handleReject() }}>Submit</Button>
                    <Button variant="light" onClick={() => { setRejectModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>

            {(data && showModal) && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    IR Information
                </Modal.Header>

                <Modal.Body>
                    <div className="row">

                        <div className="col-md-12">
                            <Box>
                                <Stepper activeStep={activeStep} orientation="vertical">
                                    {steps.map((step, index) => (
                                        <Step key={step.label}>
                                            <StepLabel>
                                                {step.label}
                                            </StepLabel>
                                            <StepContent>
                                                <Typography>{step.description}</Typography>
                                                <Box>
                                                </Box>
                                            </StepContent>
                                        </Step>
                                    ))}
                                </Stepper>
                                {activeStep === steps.length && (
                                    <Paper square elevation={0} sx={{ p: 3 }}>
                                        <Typography>Submitted! </Typography>

                                    </Paper>
                                )}
                            </Box>
                        </div>
                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    <Button
                        variant="warning"
                        className='me-2 mt-2'
                        onClick={() => { handleRejectModal() }}
                        disabled={disabled}
                        sx={{ mt: 1, mr: 1 }}
                        ref={rejectButtonRef}
                    >
                        Return it to Reporter
                    </Button>

                    {
                        isContinue && (<Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={handleButtonClick}
                            sx={{ mt: 1, mr: 1 }}

                        >
                            Continue
                        </Button>)
                    }
                    {!isContinue && <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={handleSubmit}
                        sx={{ mt: 1, mr: 1 }}
                        disabled={disabled}
                        ref={submitButtonRef}
                    >
                        Submit
                    </Button>}
                    <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>

                </Modal.Footer>
            </Modal>}

            {isEdit && <IncidentEdit isEdit={isEdit} setIsEdit={setIsEdit} onSaved={onSaved} data={data} />}       </>
    )
}

export default AirReviewerCard;