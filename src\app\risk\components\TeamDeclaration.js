import React from 'react'

function TeamDeclaration({data}) {
    console.log(data)
    return (
        <div className="col-12 mt-4 mb-4 card-boxshadow" >
            <h5 className="mt-2 mb-3 text-center">Team Members Declaration</h5>
            <p  className='text-center'>I confirm my participation in this Risk Assessment as a team member. The outcome reflects our shared professional judgment to the best of our abilities through consensus.</p>
            <table className="table ">
                <thead>
                    <th>Name</th>
                    <th>Department</th>
                    <th>Sign / Date of Affimation</th>
                </thead>
                {/* <tbody>
                    {data.map((item) => {
                        return (<tr>
                            <td>{item.label}</td>
                            <td>{item.department }</td>
                            <td>{item.date === '' ? 'Not Yet Done' :  <div className='d-flex flex-column align-items-start'> <img src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/'+item.sign} style={{maxWidth:'100%',width:'unset',height:'unset'}}/><span>{item.date}</span></div>}</td>
                        </tr>)
                    })}
                </tbody> */}

<tbody>
    {data.filter(item => item !== null).map((item) => (
        <tr key={item.id}>
            <td>{item.label}</td>
            <td>{item.department || "N/A"}</td>
            <td>
                {item.date === "" ? (
                    "Not Yet Done"
                ) : (
                    <div className="d-flex flex-column align-items-start">
                        <img
                            src={
                                "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/" +
                                item.sign
                            }
                            alt="Sign"
                            style={{ maxWidth: "100%", width: "130px", height: "unset" }}
                        />
                        <span>{item.date}</span>
                    </div>
                )}
            </td>
        </tr>
    ))}
</tbody>




            </table>
        </div>
    )
}

export default TeamDeclaration