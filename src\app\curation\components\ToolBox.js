import React, { Component } from "react";
import { Accordion, Tabs, Tab, Nav, Row, Col } from "react-bootstrap";
let Tools = [
  {
    title: "Text",
    name: "PARAGRAPH",
    icon: "fa fa-file-text-o fa-2x",
  },
  {
    title: "Image",
    name: "IMA<PERSON>",
    icon: "fa fa-picture-o fa-2x",
  },
  {
    title: "Youtube",
    name: "<PERSON><PERSON><PERSON>",
    icon: "fa fa-youtube-play fa-2x",
  },
  {
    title: "Videos",
    name: "VIDE<PERSON>",
    icon: "fa fa-film fa-2x",
  },
  {
    title: "Web Link",
    name: "WEB_LINK",
    icon: "fa fa-link fa-2x",
  },

  {
    title: "Audio",
    name: "AUDIO",
    icon: "fa fa-volume-up fa-2x",
  },
  {
    title: "PDF",
    name: "PDF",
    icon: "fa fa-file-pdf-o  fa-2x",
  },
  {
    title: "Embed Code",
    name: "EMBEDCODE",
    icon: "fa fa-code fa-2x",
  },
];
let Tools1 = [
  {
    title: "IMCQ",
    name: "<PERSON><PERSON>",
    icon: "fa fa-bar-chart fa-2x",
  },
  {
    title: "Textbox",
    name: "TEXT_INPUT",
    icon: "fa fa-file-text fa-2x",
  },
  {
    title: "Image",
    name: "IMAGE_INPUT",
    icon: "fa fa-camera-retro  fa-2x",
  },
  {
    title: "Videos",
    name: "VIDEO_INPUT",
    icon: "fa fa-video-camera fa-2x",
  },
  {
    title: "Audio",
    name: "AUDIO_INPUT",
    icon: "fa fa-microphone fa-2x",
  },

  {
    title: "Option",
    name: "OPTION_INPUT",
    icon: "fa fa-list-alt  fa-2x",
  },
  {
    title: "Sign",
    name: "SIGN_INPUT",
    icon: "fa fa-pencil-square-o  fa-2x",
  },
  {
    title: "Check Point",
    name: "CHECK_INPUT",
    icon: "fa fa-check-square-o  fa-2x",
  },
  {
    title: "Multimedia",
    name: "MULTIMEDIA",
    icon: "fa fa-upload  fa-2x",
  },
  {
    title: "Ques Bank",
    name: "BANK",
    icon: "fa fa-question-circle-o  fa-2x",
  },
];
class ToolBox extends Component {
  render() {
    return (
      <div className="card card-default ">
        <div className="card-header bg-header mb-3 text-center" style={{ padding: 13 }}>Drag to add a Field</div>
        {/* <Accordion style={{ padding: '5px 20px' }}>
          <Accordion.Item eventKey="0">
            <Accordion.Header>Communication Widget</Accordion.Header>
            <Accordion.Body>
              
            </Accordion.Body>
          </Accordion.Item>
          <Accordion.Item eventKey="1">
            <Accordion.Header>Feedback Widget</Accordion.Header>
            <Accordion.Body>
             
            </Accordion.Body>
          </Accordion.Item>
        </Accordion> */}
        <div className="card-body p-2">
          <Tabs id="uncontrolled-tab-example " className="tool-nav">
            <Tab eventKey="home" title="Communication">
              <div className="row">
                <div className="col-md-12">
                  <ul
                    className="row p-3"
                    ref={(tools) => (this._tools = tools)}
                  >
                    {Tools.map((types) => {
                      return (
                        <li
                          data-tool={types.name}
                          onDragStart={(e) => this.dragField(e, types.name)}
                          key={types.name}
                          className=" singleField col-6"


                        >
                          <div className="list-group-item d-flex flex-column align-items-center">
                            <i className={types.icon + "  mr-3"} style={{ fontSize: 25 }}></i>
                            <p className="m-0">{types.title}</p>
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
            </Tab>
            <Tab eventKey="profile" title="FeedBack">
              <div className="row">
                <div className="col-md-12">
                  <ul
                    className="row p-3"
                    ref={(tools) => (this._tools1 = tools)}
                  >
                    {Tools1.map((types) => {
                      return (
                        <li
                          data-tool={types.name}
                          onDragStart={(e) => this.dragField(e, types.name)}
                          key={types.name}
                          className=" singleField col-6"
                        >
                          <div className="list-group-item d-flex flex-column align-items-center">
                            <i className={types.icon + " mr-3"} style={{ fontSize: 25 }}></i>

                            <p className="m-0">{types.title}</p>
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
            </Tab>
          </Tabs>
        </div>
      </div>
    );
  }

  componentDidMount() {
    let tools = this._tools;
    let $ = window.$;
    $(tools)
      .children()
      .each((i, l) => {
        $(l).draggable({ helper: "clone" });
      });
    let tools1 = this._tools1;

    $(tools1)
      .children()
      .each((i, l) => {
        $(l).draggable({ helper: "clone" });
      });
  }

  dragField(e, types) {
    e.dataTransfer.setData("dragField", types);
  }
}

export default ToolBox;
