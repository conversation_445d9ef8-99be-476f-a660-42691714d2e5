import React, { useState, useEffect } from "react";
import { Nav, Tab } from "react-bootstrap";
import MaterialTable from "material-table";
import { GOOD_CATCH, RISKASSESSMENT_LIST, RISK_WITH_ID_URL,ACTION_URL } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import CardOverlay from '../pages/CardOverlay';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';

import Box from '@mui/material/Box';

// import Dashboard from "./Dashboard";

import Caches from "./Caches";

// import Actions from "../risk/Actions";
import AppSwitch from "../pages/AppSwitch";
import Actions from "./Actions";

function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && (
                <Box >
                    {children}
                </Box>
            )}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};
const Dashboard = () => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const history = useHistory();
    const location = useLocation()
    const [data, setData] = useState([])
    const [actionData, setActionData] = useState([])
    const [risk, setRisk] = useState([])
    const [overdue, setOverdue] = useState([])
    const [additional, setAdditional] = useState([])
    const [aOverdue, setAOverdue] = useState([])
    const [access, setAccess] = useState(false)



    useEffect(() => {
        getPermit();
        getAction();

    }, [])

    const getAction = async () => {
        try {
            const response = await API.get(ACTION_URL);
            if (response.status === 200) {
                // Filter data where application === "GoodCatch"
                const filteredData = response.data.filter(
                    (item) => item.application === "GoodCatch"
                );
                setActionData(filteredData);
            }
        } catch (error) {
            console.error("Error fetching actions:", error);
        }
    };
    
    const customSwal = Swal.mixin({
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-light'
        },
        buttonsStyling: false
    })
    const customSwal2 = Swal.mixin({
        customClass: {
            confirmButton: 'btn btn-primary',

        },
        buttonsStyling: false
    })

    const getPermit = async () => {
        const uriString = { include: ["reporter", "locationThree", "locationFour", "admin", "actionOwner", "actions"] };

        const url = `${GOOD_CATCH}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {

            setData(response.data)

        }


    }
    const defaultMaterialTheme = createTheme();
    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };
    const viewRisk = (data) => {
        let id = data.id
        if (data.type.label === 'Hazard-Based') {
            history.push('/risk-assessment/viewhazard', { id })
        } else {
            history.push('/risk-assessment/viewrisk', { id })
        }
    }
    const editRisk = (data) => {
        let id = data.id
        if (data.type.label === 'Hazard-Based') {
            history.push('/risk-assessment/amendhazard', { id })
        } else {
            history.push('/risk-assessment/amendrisk', { id })
        }
    }

    const onDelete = async (id) => {

        customSwal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                const response = await API.delete(RISK_WITH_ID_URL(id));
                if (response.status === 204) {

                    customSwal2.fire(
                        'Deleted!',
                        '',
                        'success'
                    )


                }
                getPermit();
            }
        })

    }



    const [value, setValue] = useState('ACTIONS');

    const TABS = {
        ACTIONS: 'ACTIONS',
        MYDOCUMENT: 'MYDOCUMENT',
        DASHBOARD: "DASHBOARD",
        CURATE: "CURATE",
        ASSIGNMENT: "ASSIGNMENT",
        REPORT: "REPORT",

    };
    const handleChange = (event, newValue) => {

        setValue(newValue);
    };
    return (

        <>
            <AppSwitch value={{ label: 'Good Catch', value: 'catches' }} />





            <Tabs value={value} onChange={handleChange} aria-label="incident report table">
                <MTab label="My Action" value={TABS.ACTIONS} />
                <MTab label="All Catches" value={TABS.MYDOCUMENT} />
                {/* <MTab label="All Document" value={TABS.DASHBOARD} /> */}

                {/* <MTab label={"Curate"} value={TABS.CURATE} /> */}
                {/* <MTab label={"Assignment"} value={TABS.ASSIGNMENT} /> */}

            </Tabs>

            <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
                <Actions applicationType={'GoodCatch'} action={actionData}  />
            </CustomTabPanel>
            <CustomTabPanel value={value} tabValue={TABS.MYDOCUMENT}>
                <Caches obsdata={data} />
            </CustomTabPanel>
            {/* <CustomTabPanel value={value} tabValue={TABS.DASHBOARD}>
                <Document />
            </CustomTabPanel> */}
            {/* <CustomTabPanel value={value} tabValue={TABS.CURATE}>
                <Curate/>
            </CustomTabPanel> */}
            {/* <CustomTabPanel value={value} tabValue={TABS.ASSIGNMENT}>
                <Assignment />
            </CustomTabPanel> */}
            {/* <CustomTabPanel value={value} tabValue={TABS.REPORT}>
            <Reports/>
            </CustomTabPanel> */}
            {/* <CustomTabPanel value={value} tabValue={TABS.TOOLBOX}>
                <ToolBoxTalk/>
            </CustomTabPanel> */}

        </>

    );
};

export default Dashboard;
