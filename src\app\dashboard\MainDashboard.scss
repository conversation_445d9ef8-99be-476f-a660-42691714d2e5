/* Main Dashboard Styles */
.main-dashboard {
  padding: 20px 0;
  
  h1 {
    font-weight: 700;
    margin-bottom: 8px;
  }
  
  .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: box-shadow 0.3s ease;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
  }
}

/* Analytics Styles */
.observation-analytics,
.incident-analytics,
.epermit-analytics {
  .card {
    margin-bottom: 24px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }

  // Filter section styling
  .filters-section {
    .form-label {
      font-weight: 600;
      color: #495057;
      margin-bottom: 8px;
      font-size: 0.9rem;
    }

    .p-calendar {
      width: 100%;

      .p-inputtext {
        width: 100%;
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;

        &:focus {
          border-color: #007bff;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }
    }

    .filter-summary {
      background-color: #f8f9fa;
      padding: 12px 16px;
      border-radius: 8px;
      margin-top: 16px;

      .small {
        font-size: 0.875rem;
        margin: 0;
      }
    }
  }

  &.observation-analytics .filters-section .filter-summary {
    border-left: 4px solid #007bff;
  }

  &.incident-analytics .filters-section .filter-summary {
    border-left: 4px solid #dc3545;
  }

  &.epermit-analytics .filters-section .filter-summary {
    border-left: 4px solid #ffc107;
  }

  // Collapsible filter header styling
  .card-header {
    &[style*="cursor: pointer"] {
      user-select: none;
      transition: background-color 0.2s ease;

      &:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
      }

      .badge {
        animation: pulse 2s infinite;

        .position-absolute {
          animation: blink 1.5s infinite;
        }
      }
    }
  }

  // Filter collapse animation
  .collapsing {
    transition: height 0.35s ease;
  }

  // Badge animations
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }

  // Chevron rotation
  .transition-transform {
    transition: transform 0.3s ease;
  }

  .rotate-180 {
    transform: rotate(180deg);
  }

  .card {
    .card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #dee2e6;
      border-radius: 12px 12px 0 0 !important;
      padding: 16px 24px;
      font-size: 16px;
      font-weight: 600;
      color: #495057;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 24px;
        right: 24px;
        height: 2px;
        background: linear-gradient(90deg, #007bff, #28a745);
        border-radius: 1px;
      }
    }

    .card-body {
      padding: 24px;
    }
  }
  
  h2 {
    font-weight: 700;
    margin-bottom: 8px;
  }

  &.observation-analytics h2 {
    color: #007bff;
  }

  &.incident-analytics h2 {
    color: #dc3545;
  }

  &.epermit-analytics h2 {
    color: #ffc107;
  }
  
  .text-muted {
    font-size: 1.1rem;
    margin-bottom: 32px;
  }
  
  // Summary statistics styling
  .summary-stats {
    .text-center {
      padding: 20px;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
      }
      
      h3 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        
        &.text-primary { color: #007bff !important; }
        &.text-success { color: #28a745 !important; }
        &.text-warning { color: #ffc107 !important; }
        &.text-danger { color: #dc3545 !important; }
      }
      
      p {
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0;
      }
    }
  }
}

/* Tab Styling */
.MuiTabs-root {
  .MuiTab-root {
    text-transform: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 12px 20px;
    min-height: auto;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(0, 123, 255, 0.04);
    }
    
    &.Mui-selected {
      color: #007bff;
      font-weight: 600;
    }
  }
  
  .MuiTabs-indicator {
    height: 3px;
    border-radius: 2px;
    background: linear-gradient(90deg, #007bff, #28a745);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-dashboard {
    padding: 15px 0;

    .observation-analytics {
      .card .card-body {
        padding: 20px;
      }

      .filters-section {
        .row > .col-md-2 {
          flex: 0 0 50%;
          max-width: 50%;
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .main-dashboard {
    h1 {
      font-size: 1.8rem;
    }
    
    .observation-analytics {
      .card {
        margin-bottom: 20px;
        
        .card-header {
          padding: 14px 20px;
          font-size: 15px;
        }
        
        .card-body {
          padding: 16px;
        }
      }
      
      .summary-stats .text-center {
        padding: 15px;
        
        h3 {
          font-size: 2rem;
        }
      }
    }
  }
  
  .MuiTabs-root .MuiTab-root {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .main-dashboard {
    padding: 10px 0;

    h1 {
      font-size: 1.6rem;
      text-align: center;
    }

    .text-muted {
      font-size: 1rem;
      text-align: center;
    }

    .observation-analytics {
      .card {
        margin-bottom: 16px;
        border-radius: 8px;

        .card-header {
          padding: 12px 16px;
          font-size: 14px;
          border-radius: 8px 8px 0 0 !important;

          &::after {
            left: 16px;
            right: 16px;
          }
        }

        .card-body {
          padding: 12px;
        }
      }

      .filters-section {
        .row > .col-md-2 {
          margin-bottom: 16px;
          flex: 0 0 100%;
          max-width: 100%;
        }

        .form-label {
          font-size: 0.85rem;
        }

        .filter-summary {
          margin-top: 12px;
          padding: 10px 12px;
        }
      }

      .summary-stats .text-center {
        padding: 12px;
        margin-bottom: 16px;

        h3 {
          font-size: 1.8rem;
        }

        p {
          font-size: 0.8rem;
        }
      }
    }
  }
  
  .MuiTabs-root {
    .MuiTab-root {
      padding: 8px 12px;
      font-size: 0.85rem;
      min-width: auto;
      
      .badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
      }
    }
    
    .MuiTabs-scroller {
      .MuiTabs-flexContainer {
        justify-content: flex-start;
      }
    }
  }
}

@media (max-width: 576px) {
  .main-dashboard {
    .container-fluid {
      padding-left: 10px;
      padding-right: 10px;
    }
    
    .observation-analytics {
      .summary-stats {
        .row > .col-md-3 {
          margin-bottom: 12px;
        }
        
        .text-center {
          padding: 10px;
          
          h3 {
            font-size: 1.5rem;
          }
        }
      }
    }
  }
}

/* Loading and Error States */
.loading-spinner {
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

.alert {
  border-radius: 8px;
  border: none;
  
  &.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  &.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
  }
}

/* Placeholder Content Styling */
.placeholder-content {
  padding: 60px 20px;
  text-align: center;
  
  .feather {
    opacity: 0.3;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 12px;
  }
  
  p {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 24px;
  }
  
  .alert {
    max-width: 400px;
    margin: 0 auto;
  }
}

/* Chart Container Responsive */
.highcharts-container {
  width: 100% !important;
  height: auto !important;
}

/* Custom Scrollbar for Tabs */
.MuiTabs-scroller::-webkit-scrollbar {
  height: 4px;
}

.MuiTabs-scroller::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.MuiTabs-scroller::-webkit-scrollbar-thumb {
  background: #007bff;
  border-radius: 2px;
}

.MuiTabs-scroller::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}
