import React, { useState, useEffect, useMemo } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Row, Col, Card, Button, Form, Collapse } from 'react-bootstrap';
import Select from 'react-select';
import { Calendar } from 'primereact/calendar';
import * as Icon from 'feather-icons-react';
import moment from 'moment';
import {
  processIncidentSeverityData,
  processIncidentStatusData,
  processIncidentReportedStatusData,
  processIncidentDayNightData,
  processIncidentGroupData,
  processIncidentAreaData,
  processIncidentMonthlyTrends,
  processIncidentSeverityTrends,
  getPieChartOptions,
  getColumnChartOptions,
  getLineChartOptions
} from '../utils/chartUtils';

const IncidentAnalytics = ({ data = [] }) => {
  const [filteredData, setFilteredData] = useState(data);
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState({
    severity: [],
    status: [],
    reportedStatus: [],
    dayNight: [],
    group: [],
    area: [],
    zone: [],
    dateRange: { start: null, end: null }
  });
  
  const [chartData, setChartData] = useState({
    severity: [],
    status: [],
    reportedStatus: [],
    dayNight: [],
    group: [],
    area: [],
    monthly: { categories: [], data: [] },
    severityTrends: { categories: [], series: [] }
  });

  // Generate filter options from data
  const filterOptions = useMemo(() => {
    if (!data || data.length === 0) return {};
    
    const uniqueValues = (accessor) => {
      const values = data.map(accessor).filter(Boolean);
      return [...new Set(values)].sort().map(value => ({ value, label: value }));
    };

    return {
      severity: uniqueValues(item => {
        const severity = item.incidentRating?.item || '';
        return severity.replace(/C(\d+)H(\d+)/, "H$2C$1");
      }),
      status: uniqueValues(item => item.status),
      reportedStatus: [
        { value: 'Reported', label: 'Reported' },
        { value: 'Unreported', label: 'Unreported' }
      ],
      dayNight: [
        { value: 'Day', label: 'Day' },
        { value: 'Night', label: 'Night' }
      ],
      group: uniqueValues(item => item.workingGroup?.name),
      area: uniqueValues(item => item.locationThree?.name),
      zone: uniqueValues(item => item.locationFour?.name)
    };
  }, [data]);

  // Apply filters to data
  useEffect(() => {
    let filtered = [...data];

    // Apply severity filter
    if (filters.severity.length > 0) {
      filtered = filtered.filter(item => {
        const severity = item.incidentRating?.item || '';
        const formattedSeverity = severity.replace(/C(\d+)H(\d+)/, "H$2C$1");
        return filters.severity.some(filter => filter.value === formattedSeverity);
      });
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(item => 
        filters.status.some(filter => filter.value === item.status)
      );
    }

    // Apply reported status filter
    if (filters.reportedStatus.length > 0) {
      filtered = filtered.filter(item => {
        const reportedStatus = item.isReported === 'Yes' ? 'Reported' : 
                              item.isReported === 'No' ? 'Unreported' : 
                              item.isReported;
        return filters.reportedStatus.some(filter => filter.value === reportedStatus);
      });
    }

    // Apply day/night filter
    if (filters.dayNight.length > 0) {
      filtered = filtered.filter(item => {
        const date = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
        if (date.isValid()) {
          const hour = date.hour();
          const dayNight = hour >= 6 && hour < 18 ? 'Day' : 'Night';
          return filters.dayNight.some(filter => filter.value === dayNight);
        }
        return false;
      });
    }

    // Apply group filter
    if (filters.group.length > 0) {
      filtered = filtered.filter(item => 
        filters.group.some(filter => filter.value === item.workingGroup?.name)
      );
    }

    // Apply area filter
    if (filters.area.length > 0) {
      filtered = filtered.filter(item => 
        filters.area.some(filter => filter.value === item.locationThree?.name)
      );
    }

    // Apply zone filter
    if (filters.zone.length > 0) {
      filtered = filtered.filter(item => 
        filters.zone.some(filter => filter.value === item.locationFour?.name)
      );
    }

    // Apply date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(item => {
        const itemDate = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
        if (!itemDate.isValid()) return false;

        const start = filters.dateRange.start ? moment(filters.dateRange.start).startOf('day') : null;
        const end = filters.dateRange.end ? moment(filters.dateRange.end).endOf('day') : null;

        if (start && end) {
          return itemDate.isBetween(start, end, null, '[]');
        } else if (start) {
          return itemDate.isSameOrAfter(start);
        } else if (end) {
          return itemDate.isSameOrBefore(end);
        }
        return true;
      });
    }

    setFilteredData(filtered);
  }, [data, filters]);

  // Update chart data when filtered data changes
  useEffect(() => {
    if (filteredData && filteredData.length >= 0) {
      setChartData({
        severity: processIncidentSeverityData(filteredData),
        status: processIncidentStatusData(filteredData),
        reportedStatus: processIncidentReportedStatusData(filteredData),
        dayNight: processIncidentDayNightData(filteredData),
        group: processIncidentGroupData(filteredData),
        area: processIncidentAreaData(filteredData),
        monthly: processIncidentMonthlyTrends(filteredData),
        severityTrends: processIncidentSeverityTrends(filteredData)
      });
    }
  }, [filteredData]);

  // Filter handling functions
  const handleFilterChange = (filterType, selectedOptions) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: selectedOptions || []
    }));
  };

  const handleDateRangeChange = (field, date) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: date
      }
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      severity: [],
      status: [],
      reportedStatus: [],
      dayNight: [],
      group: [],
      area: [],
      zone: [],
      dateRange: { start: null, end: null }
    });
  };

  const hasActiveFilters = () => {
    return Object.values(filters).some(filter => {
      if (Array.isArray(filter)) return filter.length > 0;
      if (typeof filter === 'object' && filter !== null) {
        return filter.start !== null || filter.end !== null;
      }
      return false;
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'dateRange') {
        if (value.start || value.end) count++;
      } else if (Array.isArray(value) && value.length > 0) {
        count++;
      }
    });
    return count;
  };

  // Custom styles for react-select
  const selectStyles = {
    control: (provided) => ({
      ...provided,
      minHeight: '38px',
      border: '1px solid #ced4da',
      borderRadius: '0.375rem',
      '&:hover': {
        borderColor: '#007bff'
      }
    }),
    multiValue: (provided) => ({
      ...provided,
      backgroundColor: '#dc3545',
      color: 'white'
    }),
    multiValueLabel: (provided) => ({
      ...provided,
      color: 'white'
    }),
    multiValueRemove: (provided) => ({
      ...provided,
      color: 'white',
      '&:hover': {
        backgroundColor: '#c82333',
        color: 'white'
      }
    })
  };

  // Chart options
  const severityChartOptions = {
    ...getPieChartOptions('Incident Severity Distribution', 'Distribution by severity levels'),
    series: [{
      name: 'Incidents',
      colorByPoint: true,
      data: chartData.severity
    }]
  };

  const statusChartOptions = {
    ...getPieChartOptions('Incident Status Distribution', 'Current status of all incidents'),
    series: [{
      name: 'Incidents',
      colorByPoint: true,
      data: chartData.status
    }]
  };

  const reportedStatusChartOptions = {
    ...getPieChartOptions('Reported Status Distribution', 'Reported vs Unreported incidents'),
    series: [{
      name: 'Incidents',
      colorByPoint: true,
      data: chartData.reportedStatus
    }]
  };

  const dayNightChartOptions = {
    ...getPieChartOptions('Day/Night Distribution', 'Incidents by time of day'),
    series: [{
      name: 'Incidents',
      colorByPoint: true,
      data: chartData.dayNight
    }]
  };

  const groupChartOptions = {
    ...getColumnChartOptions('Top 10 Groups by Incidents', 'Groups with highest incident counts'),
    series: [{
      name: 'Incidents',
      data: chartData.group,
      colorByPoint: true
    }]
  };

  const areaChartOptions = {
    ...getColumnChartOptions('Top 10 Areas by Incidents', 'Areas with highest incident counts'),
    series: [{
      name: 'Incidents',
      data: chartData.area,
      colorByPoint: true
    }]
  };

  const monthlyTrendsOptions = {
    ...getLineChartOptions('Monthly Incident Trends', 'Incident count over the last 12 months'),
    xAxis: {
      categories: chartData.monthly.categories
    },
    series: [{
      name: 'Incidents',
      data: chartData.monthly.data,
      color: '#dc3545'
    }],
    tooltip: {
      valueSuffix: ' incidents'
    }
  };

  const severityTrendsOptions = {
    ...getLineChartOptions('Severity Trends Over Time', 'Incident severity trends over the last 12 months'),
    xAxis: {
      categories: chartData.severityTrends.categories
    },
    series: chartData.severityTrends.series,
    tooltip: {
      valueSuffix: ' incidents'
    }
  };

  const cardStyle = {
    marginBottom: '20px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    border: 'none'
  };

  const cardHeaderStyle = {
    backgroundColor: '#f8f9fa',
    borderBottom: '1px solid #dee2e6',
    padding: '15px 20px',
    fontSize: '16px',
    fontWeight: '600',
    color: '#495057'
  };

  return (
    <div className="incident-analytics">
      <div className="mb-4">
        <h2 className="text-danger">Incident Analytics Dashboard</h2>
        <p className="text-muted">Comprehensive analysis of incident data with interactive charts</p>
      </div>

      {/* Summary Statistics */}
      <Row className="mb-4">
        <Col lg={12}>
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Summary Statistics
            </Card.Header>
            <Card.Body className="summary-stats">
              <Row>
                <Col md={3} className="text-center">
                  <h3 className="text-danger">{filteredData.length}</h3>
                  <p className="text-muted">Total Incidents</p>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="text-success">
                    {chartData.status.find(item => item.name === 'Closed')?.y || 0}
                  </h3>
                  <p className="text-muted">Closed Incidents</p>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="text-warning">
                    {chartData.status.find(item => item.name === 'Open')?.y || 0}
                  </h3>
                  <p className="text-muted">Open Incidents</p>
                </Col>
                <Col md={3} className="text-center">
                  <h3 className="text-info">
                    {chartData.status.find(item => item.name === 'Under Investigation')?.y || 0}
                  </h3>
                  <p className="text-muted">Under Investigation</p>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Filters Section */}
      <Card style={cardStyle} className="mb-4">
        <Card.Header 
          style={{...cardHeaderStyle, cursor: 'pointer'}} 
          onClick={() => setFiltersOpen(!filtersOpen)}
        >
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <Icon.Filter size={18} className="me-2" />
              <span>Filters</span>
              {hasActiveFilters() && (
                <span className="badge bg-danger ms-2 position-relative">
                  {getActiveFiltersCount()}
                  <span className="position-absolute top-0 start-100 translate-middle p-1 bg-warning border border-light rounded-circle">
                    <span className="visually-hidden">Active filters</span>
                  </span>
                </span>
              )}
            </div>
            <div className="d-flex align-items-center">
              {hasActiveFilters() && (
                <Button 
                  variant="outline-secondary" 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    clearAllFilters();
                  }}
                  className="me-2"
                >
                  <Icon.X size={14} className="me-1" />
                  Clear All
                </Button>
              )}
              <Icon.ChevronDown 
                size={18} 
                className={`transition-transform ${filtersOpen ? 'rotate-180' : ''}`}
                style={{ 
                  transform: filtersOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s ease'
                }}
              />
            </div>
          </div>
        </Card.Header>
        <Collapse in={filtersOpen}>
          <Card.Body className="filters-section">
            <Row>
              <Col md={2} className="mb-3">
                <Form.Label>Severity</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.severity || []}
                  value={filters.severity}
                  onChange={(selected) => handleFilterChange('severity', selected)}
                  placeholder="Select severity..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Status</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.status || []}
                  value={filters.status}
                  onChange={(selected) => handleFilterChange('status', selected)}
                  placeholder="Select status..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Reported Status</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.reportedStatus || []}
                  value={filters.reportedStatus}
                  onChange={(selected) => handleFilterChange('reportedStatus', selected)}
                  placeholder="Select reported status..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Day/Night</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.dayNight || []}
                  value={filters.dayNight}
                  onChange={(selected) => handleFilterChange('dayNight', selected)}
                  placeholder="Select time..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Group</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.group || []}
                  value={filters.group}
                  onChange={(selected) => handleFilterChange('group', selected)}
                  placeholder="Select group..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Area</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.area || []}
                  value={filters.area}
                  onChange={(selected) => handleFilterChange('area', selected)}
                  placeholder="Select area..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Zone</Form.Label>
                <Select
                  isMulti
                  options={filterOptions.zone || []}
                  value={filters.zone}
                  onChange={(selected) => handleFilterChange('zone', selected)}
                  placeholder="Select zone..."
                  styles={selectStyles}
                  isClearable
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Date From</Form.Label>
                <Calendar
                  value={filters.dateRange.start}
                  onChange={(e) => handleDateRangeChange('start', e.value)}
                  placeholder="Select start date"
                  dateFormat="dd/mm/yy"
                  showIcon
                  className="w-100"
                />
              </Col>
              <Col md={2} className="mb-3">
                <Form.Label>Date To</Form.Label>
                <Calendar
                  value={filters.dateRange.end}
                  onChange={(e) => handleDateRangeChange('end', e.value)}
                  placeholder="Select end date"
                  dateFormat="dd/mm/yy"
                  showIcon
                  className="w-100"
                />
              </Col>
            </Row>
            <Row>
              <Col>
                <div className="filter-summary">
                  <div className="text-muted small">
                    Showing <strong>{filteredData.length}</strong> of <strong>{data.length}</strong> incidents
                    {hasActiveFilters() && (
                      <span className="text-danger"> ({getActiveFiltersCount()} filter{getActiveFiltersCount() !== 1 ? 's' : ''} applied)</span>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Collapse>
      </Card>

      <Row>
        {/* Severity Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Severity Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={severityChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Status Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Status Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={statusChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Reported Status Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Reported Status Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={reportedStatusChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Day/Night Distribution */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Day/Night Distribution
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={dayNightChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Monthly Trends */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Monthly Trends
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={monthlyTrendsOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Severity Trends */}
        <Col lg={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Severity Trends Over Time
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={severityTrendsOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Top Groups */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Groups by Incidents
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={groupChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>

        {/* Top Areas */}
        <Col lg={6} md={12} className="mb-4">
          <Card style={cardStyle}>
            <Card.Header style={cardHeaderStyle}>
              Top Areas by Incidents
            </Card.Header>
            <Card.Body>
              <HighchartsReact
                highcharts={Highcharts}
                options={areaChartOptions}
              />
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default IncidentAnalytics;
