import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";

import Select from "react-select";
import { DropzoneArea } from 'material-ui-dropzone'; 

import { USERS_URL, AIR_GM_OPS_URL, AIR_TRAINEE_URL, AIR_GMOPS_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";

const AirGmOpsCard = ({ showModal, setShowModal, data }) => {

    const [showNext, setShowNext] = useState(false);
    const handleReject = () => {

    }

    const handleContinue = () => {
        setShowNext(true)
    }

    const [users, setUsers] = useState([])
    useEffect(() => {
        getUsers()
    }, [])

    const getUsers = async () => {
        const response = await API.get(USERS_URL);
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    const [trainee, setTrainee] = useState([])
    useEffect(() => {
        getTrainee()
    }, [])

    const getTrainee = async () => {
        const response = await API.post(AIR_TRAINEE_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setTrainee(response.data)
        }
    }

    const handleUserSelectChange = (selectedOptions) => {
        setSelectedUsers(selectedOptions);
    };

    const [selectedUsers, setSelectedUsers] = useState([]);

    const [selectedTrainee, setSelectedTrainee] = useState('');



    const [punitiveActions, setPunitiveActions] = useState('');
    const [comments, setComments] = useState('');
    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }

    const steps = [
        {
            label: 'Punitive / Correction Action Taker Action',
            description: (<>

                <p>Punitive Actions (Persons Involved)</p>
                <Select
                    id="user_description"
                    isMulti={true} // Allow multiple selections
                    onChange={handleUserSelectChange} // Handle selection changes
                    options={users.map(user => ({ value: user.id, label: user.firstName }))} // Map users to options
                    value={selectedUsers} // Set selected options
                    placeholder="Type..."
                />



                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">Punitive Actions</label>
                            <textarea value={punitiveActions} onChange={(e) => setPunitiveActions(e.target.value)} className="form-control"> </textarea>
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">Comments</label>
                            <textarea value={comments} onChange={(e) => setComments(e.target.value)} className="form-control"> </textarea>
                        </div>
                    </div>
                </div>


                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <DropzoneArea
                                acceptedFiles={[
                                    'application/pdf',
                                    'image/jpeg',
                                    'image/png'

                                ]}
                                dropzoneText={"Drag and drop files / documents / pictures"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                onChange={handleFileChange}
                            />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label>Assign to Trainer</label>
                            <select onChange={(e) => setSelectedTrainee(e.target.value)} className="form-control">
                                <option value="">Choose Trainer</option>
                                {
                                    trainee.map(user => {
                                        return (
                                            <option value={user.id}> {user.firstName} </option>
                                        )
                                    })
                                }
                            </select>
                        </div>
                    </div>
                </div>

            </>)
        }
    ]

    const [investigation, setInvestigation] = useState(null)
    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };
    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(true);
       
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_GMOPS_WITH_ID_URL(data.id, data.actionId), {
                gmOpsAction: {
                    personInvolved: selectedUsers,
                    punitiveActions: punitiveActions,
                    comments: comments,
                    traineeId: selectedTrainee
                },
                traineeId: selectedTrainee
            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                setIsClicked(false);
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }
            setIsClicked(false);
            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            // Proceed to the next step
            setActiveStep((prevActiveStep) => prevActiveStep + 1);

        } catch (error) {
            setIsClicked(false);
            console.error('An error occurred:', error);

        }
        setIsClicked(false);
    };

    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    IR Information
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div>
                        <div className="col-md-6">
                            <Box>
                                <Stepper activeStep={activeStep} orientation="vertical">
                                    {steps.map((step, index) => (
                                        <Step key={step.label}>
                                            <StepLabel>
                                                {step.label}
                                            </StepLabel>
                                            <StepContent>
                                                <Typography>{step.description}</Typography>
                                                <Box sx={{ mb: 2 }}>
                                                    <div>

                                                        {index === steps.length - 1 ? (
                                                            <>
                                                                <div className='form-group'>
                                                                    {/* <select onChange={(e) => setSelectedReviewer(e.target.value)} className='form-control'>
                                                                <option value={''}>Choose Incident Owner</option>
                                                                {
                                                                    incidentReviewer.map(user => {
                                                                        return (
                                                                            <option value={user.id}>{user.firstName}</option>
                                                                        )
                                                                    })
                                                                }
                                                            </select> */}
                                                                </div>
                                                                <Button
                                                                    variant="light"
                                                                    className='me-2 mt-2'
                                                                    onClick={handleSubmit}
                                                                    disabled={isClicked}
                                                                    sx={{ mt: 1, mr: 1 }}
                                                                >
                                                                    Submit
                                                                </Button>
                                                            </>

                                                        ) : (

                                                            <Button
                                                                variant="light"
                                                                className='me-2 mt-2'
                                                                onClick={handleNext}
                                                                sx={{ mt: 1, mr: 1 }}
                                                            >
                                                                Continue
                                                            </Button>
                                                        )}

                                                        <Button
                                                            disabled={index === 0}
                                                            className='mt-2'
                                                            onClick={handleBack}
                                                            sx={{ mt: 1, mr: 1 }}
                                                        >
                                                            Back
                                                        </Button>
                                                    </div>
                                                </Box>
                                            </StepContent>
                                        </Step>
                                    ))}
                                </Stepper>
                                {activeStep === steps.length && (
                                    <Paper square elevation={0} sx={{ p: 3 }}>
                                        <Typography>Submitted! Action Card will be disappeared from the list!</Typography>

                                    </Paper>
                                )}
                            </Box>
                        </div>
                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirGmOpsCard;