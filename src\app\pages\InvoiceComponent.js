// SelectDropdown.js
import React, { useEffect, useState } from 'react';
import Select from 'react-select';
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';
import { InputNumber } from 'primereact/inputnumber';
const InvoiceComponent = ({ costData, updateInformation, editable }) => {
    // Adjusted onChange to include index
    const [currency, setCurrency] = useState('LKR')
    const [exchangeRate, setExchangeRate] = useState(0.0032)
    const [combinedInvoiceItems, setCombinedInvoiceItems] = useState([]);
    const handleUpdateApiData = (updatedApiData) => {
        setApiData(updatedApiData);
        updateInformation(currency, exchangeRate, updatedApiData);
    };
    const handleAddCombinedInvoiceItem = (item, title) => {
        setCombinedInvoiceItems([...combinedInvoiceItems, { ...item, title }]);
    };


    const [apiData, setApiData] = useState([
        {
            title: "Direct Parts & Material Cost to Repair the Damage",
            items: []

        },
        {
            title: "Direct Labour Cost to Repair the Damage",
            items: [],
            dropDowns: ['Engineer', 'Mechanic/Electrician', 'Welder', 'Tinkerer/Painter', 'Helper', 'Outsourced Jobs']
        },
        {
            title: "Workshop, Tool & Equipment Charge",
            items: []
        },
        {
            title: "Lost Time Charges",
            items: [],

        },
        {
            title: "Professional and Inspection Charges",
            items: []
        }
    ]);

    useEffect(() => { if (costData?.length > 0) { setApiData(costData) } }, [costData])
    return (
        <>
            <div className="row">
                <div className="table-responsive">

                    <div className="">
                        <label>Cost Estimation Tool</label>
                        <form>
                            <div className="row align-items-center">
                                {/* Currency Selector */}
                                <div className="col-md-3">
                                    <label htmlFor="currency" className="form-label">Currency:</label>
                                </div>
                                <div className="col-md-3">
                                    <select id="currency" className="form-select" value={currency} onChange={(e) => setCurrency(e.target.value)}>
                                        <option value="LKR">LKR</option>
                                        <option value="USD">USD</option>
                                    </select>
                                </div>

                                {/* Exchange Rate Input */}
                                <div className="col-md-3">
                                    <label htmlFor="exchangeRate" className="form-label">Exchange Rate:</label>
                                </div>
                                <div className="col-md-3">
                                    <InputNumber
                                        id="exchangeRate"
                                        value={exchangeRate}
                                        onValueChange={(e) => setExchangeRate(parseFloat(e.value))}
                                        mode="decimal"
                                        min={0}
                                        className='w-100'
                                        placeholder="Enter exchange rate"
                                        locale="en-US"
                                        minFractionDigits={2}
                                        maxFractionDigits={2}
                                    />
                                    
                                </div>
                            </div>
                        </form>
                    </div>

                    <div className="col-md-12">
                        {costData?.length > 0 && costData.map((data, index) => (
                            <InvoiceGenerator
                                key={index}
                                title={data.title}
                                addCombinedInvoiceItem={handleAddCombinedInvoiceItem}
                                initialItems={data.items}
                                updateApiData={handleUpdateApiData}
                                apiData={costData}
                                dropDownItems={data.dropDowns}
                                currency={currency}
                                exchangeRate={exchangeRate}
                                editable={editable}
                            />
                        ))}

                        {!costData?.length > 0 && apiData.map((data, index) => (
                            <InvoiceGenerator
                                key={index}
                                title={data.title}
                                addCombinedInvoiceItem={handleAddCombinedInvoiceItem}
                                initialItems={data.items}
                                updateApiData={handleUpdateApiData}
                                apiData={apiData}
                                dropDownItems={data.dropDowns}
                                currency={currency}
                                exchangeRate={exchangeRate}
                                editable={editable}
                            />
                        ))}

                        <TotalCalculator apiData={apiData} currency={currency} exchangeRate={exchangeRate} />
                    </div>
                </div>
            </div>
        </>
    );
};

export default InvoiceComponent;
