import React from 'react';
import ActivityForm from './ActivityForm';

const SuspendedLoadForm = () => {


    return (<>
        <div>
            <ActivityForm title={'Activity'} activities={['Lifting, Lowering']} vehicles={[
                "RTG",
                "QGC",
                "Forklift",
                "JLG",
                "Building / Roof",

            ]} />

            <ActivityForm title={'Cause'} activities={['Cause']} vehicles={[
                "Low Experience",
                "Didn't see / expect",
                "Mechanical",
                "Electrical",
                "Sleepy",
                "Drowsy / Drunk",
                "Natural Reasons",
                "Over Weight",
                "Improper Twistlock",
                "Other"
            ]} />

            <ActivityForm title={'Impact'} activities={['Impact']} vehicles={[
                "Flipper Damage",
                "Struck with ec.",
                "Rope Entangled with",
                "Vessel damage",
                "Wharf / yard damage",
                "RTG damage",
                "QC damage",
                "OHS Speaker damage",
                "Trailer damage",
                "Other"
            ]
            } />

        </div>
    </>)
}

export default SuspendedLoadForm;