// @ts-nocheck
import React, { useState, useRef } from 'react';
import { useHistory } from 'react-router-dom'
import DataTables from '../tables/DataTables';
import { CHECKLIST_URL, CHECKLIST_WITH_ID_URL } from '../constants'
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-danger',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',

  },
  buttonsStyling: false
})

const customSwal3 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})

$(document).on('click', '.delete-checklist', function () {

  const id = $(this).data('id')
  customSwal.fire({
    title: 'Are you sure?',
    text: "You won't be able to revert this!",
    icon: 'warning',
    showCancelButton: true,
    reverseButtons: true,

    confirmButtonText: 'Delete'
  }).then((result) => {
    if (result.isConfirmed) {
      deleteChecklist(id);

    }
  })


})

$(document).on('blur', '.edit-inline', function () {

  var column = $(this).data('column');
  var id = $(this).data('id');
  var data = $(this).text();

  editChecklist(id, column, data)

})


$(document).on('click', '.clone-checklist', function () {

  var id = $(this).data('id');
  customSwal3.fire({
    title: 'Do you want to clone this checklist?',
    icon: 'warning',
    showCancelButton: true,
    reverseButtons: true,

    confirmButtonText: 'Clone'
  }).then((result) => {
    if (result.isConfirmed) {
      cloneChecklist(id);

    }
  })

})

const editChecklist = async (id, column, data) => {
  const response = await fetch(CHECKLIST_WITH_ID_URL(id), {

    // Adding method type 
    method: "PATCH",

    // Adding body or contents to send 
    body: JSON.stringify({
      [column]: data,

    }),

    // Adding headers to the request 
    headers: {
      "Content-type": "application/json; charset=UTF-8"
    }
  })
  if (response.ok) {
    //show success
    cogoToast.info('Updated', { position: 'top-right' })
  }
}

const cloneChecklist = async (id) => {
  const response = await fetch(CHECKLIST_WITH_ID_URL(id), {

    method: "POST",
    headers: {
      "Content-type": "application/json; charset=UTF-8"
    }
  })
  if (response.ok) {
    //show success
    $('#dataTable').DataTable().ajax.reload();
    customSwal2.fire(
      'Checklist Cloned!',
      '',
      'success'
    )
  }
}

const deleteChecklist = async (id) => {

  const response = await fetch(
    CHECKLIST_WITH_ID_URL(id), {
    method: 'DELETE',
    headers: {
      "Content-type": "application/json; charset=UTF-8"
    }
  })

  if (response.ok) {
    customSwal2.fire(
      'Deleted!',
      '',
      'success'
    )
    $('#dataTable').DataTable().ajax.reload();
  }


}
const Checklist = () => {

  const [mdShow, setMdShow] = useState(false)
  const [isLoading, setIsLoading] = useState(false);

  const history = useHistory();

  const cName = useRef();
  const cDesc = useRef();
  const cRef = useRef();
  const cVersion = useRef();

  const thead = [
    'Checklist Name',
    'Description',
    'Reference',
    'Version No',
    'Actions'
  ];

  $(document).on('click', '.edit-checklist', function () {
    var id = $(this).data('id');
    history.push('/edit_checklist/' + id);
  })

  const createChecklistHandler = async () => {
    // @ts-ignore
    setIsLoading(true)

    const response = await fetch(
      CHECKLIST_URL,
      {
        method: 'POST',
        body: JSON.stringify({
          name: cName.current.value,
          description: cDesc.current.value,
          uniqueid: cRef.current.value,
          version: cVersion.current.value,

        }),
        headers: { "Content-type": "application/json; charset=UTF-8",  "Authorization": `Bearer ${localStorage.getItem('access_token')}`}
      })

    if (response.ok) {
      $('#dataTable').DataTable().ajax.reload();
      customSwal2.fire(
        'Checklist Created!',
        '',
        'success'
      )
    } else {
      //show error
      customSwal2.fire(
        'Please Try Again!',
        '',
        'error'
      )
      setIsLoading(false)
    }

    cName.current.value = '';
    cDesc.current.value = '';
    cRef.current.value = '';
    cVersion.current.value = '';
    setMdShow(false)
  }

  const options = {
    "ajax": {
      url: CHECKLIST_URL,
      dataSrc: '',
      "beforeSend": function (xhr) {
        const token = localStorage.getItem('access_token');
        xhr.setRequestHeader('Authorization', 'Bearer ' + token);
      }
    },
    columnDefs: [
      { type: 'natural', targets: [0, 1, 2, 3] }
    ],
    "columns": [{
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="name" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.name ? data.name : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="description" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.description ? data.description : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="uniqueid" data-id="${data.id}" class="edit-inline" contenteditable="true"> ${data.uniqueid ? data.uniqueid : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="version" data-id="${data.id}" class="edit-inline" contenteditable="true"> ${data.version ? data.version : ''} </div>`;

      }
    },

    {
      "data": null,
      "render": function (data, type, full, meta) {

        return `<div style="font-size: 22px;">
        <i class="mdi mdi-content-copy clone-checklist cursor-pointer" data-id="${data.id}" ></i>
        <i class="mdi mdi-pencil-box text-primary edit-checklist cursor-pointer" data-id="${data.id}"></i>
        <i style="color: red;" class="mdi mdi-delete delete-checklist cursor-pointer" data-id="${data.id}" ></i></div>`;
      }
    },
    ]
  }

  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">

                <h4 className="card-title">Checklist Library</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-note-plus-outline mr-2" /> Create New Checklist</button>
                      <DataTables thead={thead} options={options} />

                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        show={mdShow}
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >

        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="checklist_name" >Checklist Name</label>
              <Form.Control type="text" ref={cName} id="checklist_name" placeholder="Enter Checklist Name" />
            </div>

            <div className="form-group">
              <label htmlFor="checklist_description" >Description</label>
              <Form.Control type="text" ref={cDesc} id="checklist_description" placeholder="Enter Checklist Description" />
            </div>

            <div className="form-group">
              <label htmlFor="checklist_ref" >Reference</label>
              <Form.Control type="text" ref={cRef} id="checklist_ref" placeholder="Enter Checklist Reference" />
            </div>

            <div className="form-group">
              <label htmlFor="checklist_version" >Version No</label>
              <Form.Control type="text" ref={cVersion} id="checklist_version" placeholder="Enter Checklist Version No" />
            </div>



          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">
          {
            isLoading ? <Loader /> : (
              <>
                <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>
                <Button variant="primary" onClick={createChecklistHandler}>Create</Button>
              </>
            )
          }

        </Modal.Footer>
      </Modal>
    </>
  )
}


export default Checklist;
