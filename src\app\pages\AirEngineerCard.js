import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";

import Select from "react-select";
import { DropzoneArea } from 'material-ui-dropzone';

import { AIR_APPROVE_REPORTS_SECOND_WITH_ID_URL, AIR_COST_REVIEWER_URL, AIR_COST_REVIEWER_WITH_ID_URL, AIR_DUTY_MANAGER_ESTIMATION_WITH_ID_URL, AIR_ENGINEER_WITH_ID_URL, AIR_FINANCE_URL, AIR_SURVEYOR_WITH_ID_URL, API_URL, GET_USER_ROLE_BY_MODE } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';

import TagInput from "./TagInput";
import axios from "axios";

const AirEngineerCard = ({ showModal, setShowModal, data, setData }) => {


    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([]);
    const [damagedEquipmentDetails, setDamagedEquipmentDetails] = useState(data.damagedEquipmentNumber.map(item => ({
        ...item,
        costDetails: '',
        workOrderNumber: '',
        operational: false,
        files: [] // To store uploaded file information
    })));

    const handleCostDetailsChange = (index, value) => {
        
        const updatedDetails = [...damagedEquipmentDetails];
        updatedDetails[index].costDetails = value;
        setDamagedEquipmentDetails(updatedDetails);
    };

    const handleWorkOrderChange = (index, value) => {
        
        const updatedDetails = [...damagedEquipmentDetails];
        updatedDetails[index].workOrderNumber = value;
        setDamagedEquipmentDetails(updatedDetails);
    };

    const handleOperationalChange = (index, value) => {

        console.log(index, value)
        const updatedDetails = damagedEquipmentDetails.map((item, i) => {
            if (i === index) {
                return { ...item, operational: value };
            }
            return item;
        });
        setDamagedEquipmentDetails(updatedDetails);
    };

    const handleFileChangeForEquipment = async (index, files) => {
        const formData = new FormData();
        files.forEach(file => {
            formData.append('file', file);
        });
        const token = localStorage.getItem('access_token');
        try {
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });
            if (fileResponse.status === 200) {
                const fileNames = fileResponse.data.files.map(file => file.originalname);
                const updatedDetails = [...damagedEquipmentDetails];
                updatedDetails[index].files = fileNames;  // Update with file names from the server
                setDamagedEquipmentDetails(updatedDetails);
            }
        } catch (error) {
            console.error('Error uploading files:', error);
        }
    };



    const handleFileChange = (file) => {
        
        setFiles(file)

    }

    useEffect(() => { console.log('1'); getIRCostEstimator(); }, [])

    const getIRCostEstimator = async () => {
        const response = await API.post(GET_USER_ROLE_BY_MODE, { locationOneId: data.locationOneId || '', locationTwoId: data.locationTwoId || '', locationThreeId: data.locationThreeId, locationFourId: data.locationFourId, mode: 'ir-cost-estimator' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }


    const [comments, setComments] = useState('');
    const [operational, setOperational] = useState(false);
    const [labourTime, setLabourTime] = useState(0)
    const [technicalFeedback, setTechnicalFeedback] = useState('')
    const [isEstimate, setIsEstimate] = useState(false)
    const [estimatorId, setEstimatorId] = useState('')

    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(true);
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_ENGINEER_WITH_ID_URL(data.id, data.actionId), {


                engineerComments: {
                    comments: comments,

                    labourTime: labourTime,
                    isEstimate: isEstimate,
                    damagedEquipmentDetails: damagedEquipmentDetails.map(item => ({
                        category: item.category,
                        number: item.number,
                        workOrderNumber: item.workOrderNumber,
                        damageType: item.damageType,
                        costDetails: item.costDetails,
                        operational: item.operational,
                        files: item.files
                        // Files handling as per your API's requirement
                    })),

                },
                estimatorId: estimatorId

            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                setIsClicked(false);
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setShowModal(false)
            setIsClicked(false);
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);
            setIsClicked(false);

        }
        setIsClicked(false);
    };

    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex align-items-center justify-content-between">
                        <h4 >
                            IR Information - {data.maskId}
                        </h4 >
                        <h4 >
                            Incident Date & Time: {data.incidentDate}
                        </h4 >
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">

                        <div className="col-md-12">
                            {damagedEquipmentDetails.map((i, index) => (
                                <div key={index} className="col-md-12 mb-3">
                                    <div className="row">
                                        <div className="col-sm-4">
                                            <label className="col-form-label p-1">Equipment Type</label>
                                            <p className="white-bg-border-radius-10 form-control-plaintext">
                                                {i.category}
                                            </p>
                                        </div>
                                        <div className="col-sm-4">
                                            <label className="col-form-label p-1">Equipment Number</label>
                                            <p className="white-bg-border-radius-10 form-control-plaintext">
                                                {i.number}
                                            </p>
                                        </div>
                                        <div className="col-sm-4">
                                            <label className="col-form-label p-1">Damage Type</label>
                                            <p className="white-bg-border-radius-10 form-control-plaintext">
                                                {i.damageType}
                                            </p>
                                        </div>
                                    </div>
                                    {/* Additional fields for Cost Details and Photo Upload */}
                                    <div className="row">
                                        <div className="col-sm-4">
                                            <label className="col-form-label">Cost Details</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                placeholder="Enter cost details"
                                                value={i.costDetails}
                                                onChange={(e) => handleCostDetailsChange(index, e.target.value)}
                                            />
                                        </div>
                                        <div className="col-sm-4">
                                            <label className="col-form-label">Work Order Number #</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                placeholder="Enter Work Order Number #"
                                                value={i.workOrderNumber}
                                                onChange={(e) => handleWorkOrderChange(index, e.target.value)}
                                            />
                                        </div>
                                        <div className="col-sm-4">
                                            <label className="col-form-label">Operational</label>
                                            <Switch
                                                onChange={(checked) => handleOperationalChange(index, checked)}
                                                checked={i.operational}
                                                className="d-block mt-2"
                                            />
                                        </div>
                                    </div>
                                    <div className="row">
                                        <div className="col-sm-12">
                                            <label className="col-form-label">Upload Photos</label>
                                            <DropzoneArea
                                                acceptedFiles={['image/jpeg', 'image/png']}
                                                dropzoneText={"Drag and drop photos or click"}
                                                filesLimit={5}
                                                maxFileSize={10485760} // 10MB
                                                onChange={(files) => handleFileChangeForEquipment(index, files)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className={`col-md-12`}>
                            <Box>



                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label>Estimated Labour Time (Hours)</label>
                                            
                                            <input className="form-control w-25" type="number" min={0} value={labourTime} onChange={(e) => setLabourTime(e.target.value)} />
                                        </div>
                                    </div>
                                </div>


                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label>Any other comments</label>
                                            <textarea className="form-control" value={comments} onChange={(e) => setComments(e.target.value)}>

                                            </textarea>
                                        </div>
                                    </div>
                                </div>

                                <div className="col-sm-6 mb-2">
                                    <label className="col-form-label">Is cost to be estimated?</label>
                                    <Switch
                                        onChange={(checked) => setIsEstimate(checked)}
                                        checked={isEstimate}
                                        className="d-block mt-2"
                                    />
                                </div>

                                {/* <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <DropzoneArea
                                                acceptedFiles={[
                                                    'application/pdf',
                                                    'image/jpeg',
                                                    'image/png'

                                                ]}
                                                dropzoneText={"Drag and drop files / documents / pictures"}
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                onChange={handleFileChange}
                                            />
                                        </div>
                                    </div>
                                </div> */}
                                <div className="mb-3">
                                    <label className="form-label">Submit to Cost Estimator</label>
                                    <select className="form-select" onChange={(e) => setEstimatorId(e.target.value)}>
                                        <option>Select</option>
                                        {
                                            users.map(u => (
                                                <option key={u.id} value={u.id}>{u.firstName}</option>
                                            ))
                                        }
                                    </select>
                                </div>
                                <div className="row">
                                    <div className="col-3">
                                        <Button variant="primary" onClick={handleSubmit} disabled={isClicked}>
                                            Submit
                                        </Button>
                                    </div>

                                </div>
                            </Box>
                        </div>


                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirEngineerCard;