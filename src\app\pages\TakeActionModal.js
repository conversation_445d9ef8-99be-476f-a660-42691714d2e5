import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { API_URL, AUDIT_ACTION_PLAN_REVIEWER, AUDIT_FINDINGS_ACTION_URL, AUDIT_FINDINGS_ASSIGN_ACTION_URL, OBSERVATION_REVIEWER_LIST_URL, OBSERVATION_REVIEWER_SUBMIT_URL, REPORT_INCIDENT_ACTIONS_WITH_ID, STATIC_URL } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import MedicalHRCard from "./MedicalHRCard";

const TakeActionModal = ({ data, applicationType, showModal, setShowModal }) => {

    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])
    const [isClicked, setIsClicked] = useState(false);
    const userId = useRef();

    const actionTaken = useRef();

    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {
        // if (data.applicationDetails.locationOneId && data.applicationDetails.locationTwoId && data.applicationDetails.locationThreeId && data.applicationDetails.locationFourId) {
        if (applicationType === 'Observation') {
            getObsUsers();

        }

        if (applicationType === 'Audit') {
            getAuditUsers();
        }
        // }

    }, [])

    const getObsUsers = async () => {
        const response = await API.get(OBSERVATION_REVIEWER_LIST_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    const getAuditUsers = async () => {
        const response = await API.post(AUDIT_ACTION_PLAN_REVIEWER, { locationOneId: 'tier1-all', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }
    const handleSubmit = async () => {
        setIsClicked(true);
        try {

            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {

                const originalNames = fileResponse.data.files.map(file => file.originalname);

                let url = '';
                let actionType = '';
                let assignedToId = '';

                switch (applicationType) {
                    case 'Observation':
                        url = OBSERVATION_REVIEWER_SUBMIT_URL(data.id);
                        actionType = 'reviewer'
                        assignedToId = userId.current.value
                        break;

                    case 'AIR':
                        url = REPORT_INCIDENT_ACTIONS_WITH_ID(data.id);
                        actionType = data.actionType
                        break;

                    case 'Audit':
                        url = AUDIT_FINDINGS_ACTION_URL(data.id);
                        actionType = data.actionType
                        break;

                    case 'AuditFinding':
                        url = AUDIT_FINDINGS_ACTION_URL(data.id);
                        actionType = data.actionType
                        break;
                }

                const response = await API.patch(url, {
                    actionType: actionType,
                    comments: data.comments ? data.comments : '',
                    actionTaken: actionTaken.current.value,
                    assignedToId: [assignedToId],
                    actionToBeTaken: data.actionToBeTaken,
                    objectId: data.objectId,
                    description: data.description,
                    dueDate: data.dueDate,
                    uploads: originalNames,
                    createdDate: data.createdDate

                })

                if (response.status === 204) {
                    cogoToast.success('Submitted!')
                    setIsClicked(false);
                    setShowModal(false)
                }
            }
        } catch (e) { console.log(e); setShowModal(false);setIsClicked(false); }
        setIsClicked(false);
    }
    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>


                        {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>}
                        {applicationType === 'AIR' && ` ${data.applicationDetails.maskId}`}
                        {applicationType === 'Audit' || applicationType === 'AuditFinding' && `Audit`}


                    </div>

                </Modal.Header>

                <Modal.Body>

                    <Box>
                        {data.applicationDetails && <div className="container">
                            <div className="card">

                                <div className="card-body">

                                    {
                                        data.description === 'HR' && <MedicalHRCard title="Person Involved" data={data.applicationDetails.personInvolved} />
                                    }

                                    {
                                        data.description === 'HR' && <MedicalHRCard title="Personnel Impacted" data={data.applicationDetails.personnelImpacted} />
                                    }


                                    {!applicationType === 'Audit' && <div className="mb-3">
                                        <label className="form-label">Location</label>
                                        <input type="text" className="form-control" value={`${data.applicationDetails.locationOne && data.applicationDetails.locationOne.name} > ${data.applicationDetails.locationTwo && data.applicationDetails.locationTwo.name} > ${data.applicationDetails.locationThree && data.applicationDetails.locationThree.name} > ${data.applicationDetails.locationFour && data.applicationDetails.locationFour.name} > ${data.applicationDetails.locationFive && data.applicationDetails.locationFive.name}`} readOnly />
                                    </div>}

                                    {
                                        applicationType === 'AuditFinding' && <p>Findings: {data.applicationDetails?.findings}</p>
                                    }

                                    {!applicationType === 'Audit' && <div className="mb-3">
                                        <label className="form-label">Zone</label>
                                        <input type="text" className="form-control" value={data.applicationDetails.locationSix && data.applicationDetails.locationSix.name} readOnly />
                                    </div>}
                                    {
                                        (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && <div className="mb-3">
                                            <label className="form-label">Uploads</label>
                                            <div className="border p-3 row">
                                                {
                                                    (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && data.applicationDetails.uploads.map(i => (
                                                        <div className="col-md-3">
                                                            <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                        </div>

                                                    ))
                                                }

                                            </div>
                                        </div>
                                    }
                                    {(applicationType === 'AIR' && data.applicationDetails.description) && <div className="mb-3">
                                        <label className="form-label">Description of Incident</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.applicationDetails.description}</textarea>
                                    </div>}
                                    {(applicationType === 'AIR' && data.additionalComments) && <div className="mb-3">
                                        <label className="form-label">Additional Information</label>
                                        <textarea disabled className="form-control" rows="3" readOnly>{data.additionalComments}</textarea>
                                    </div>}
                                    {(applicationType === 'AIR' && data.additionalComments) && <div className="mb-3">
                                        <label className="form-label">Additional Information</label>
                                        <textarea disabled className="form-control" rows="3" readOnly>{data.additionalComments}</textarea>
                                    </div>}

                                    {(!applicationType === 'AIR' && data.description) && <div className="mb-3">
                                        <label className="form-label">Description</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.description}</textarea>
                                    </div>}

                                    {console.log(data.applicationDetails, 'check now')}

                                    {data.comments && <div className="mb-3">
                                        <label className="form-label">Comments</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.comments}</textarea>
                                    </div>}


                                    {data.actionToBeTaken && <div className="mb-3">
                                        <label className="form-label">Actions to be taken</label>
                                        <textarea className="form-control" rows="3" value={data.actionToBeTaken} readOnly></textarea>
                                    </div>}

                                    <div className="mb-3">
                                        <label className="form-label">Action taken</label>
                                        <textarea ref={actionTaken} className="form-control" rows="3"></textarea>
                                    </div>

                                    <div className="mb-3">
                                        <label className="form-label">Upload evidence</label>
                                        <DropzoneArea
                                            acceptedFiles={[

                                                'image/jpeg',
                                                'image/png'

                                            ]}
                                            dropzoneText={"Drag and Drop Evidence Images"}
                                            filesLimit={5}
                                            maxFileSize={104857600}

                                            onChange={handleFileChange}

                                        />
                                    </div>

                                    {applicationType === 'Observation' && <div className="mb-3">
                                        <label className="form-label">Submit to</label>
                                        <select className="form-select" ref={userId}>
                                            <option>Select</option>
                                            {
                                                users.map(u => (
                                                    <option key={u.id} value={u.id}>{u.title}</option>
                                                ))
                                            }
                                        </select>
                                    </div>
                                    }

                                    {applicationType === 'Audit' && <div className="mb-3">
                                        <label className="form-label">Submit to</label>
                                        <select className="form-select" ref={userId}>
                                            <option>Select</option>
                                            {
                                                users.map(u => (
                                                    <option key={u.id} value={u.id}>{u.firstName}</option>
                                                ))
                                            }
                                        </select>
                                    </div>
                                    }

                                </div>
                            </div>
                        </div>}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">


                    <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={handleSubmit}
                        sx={{ mt: 1, mr: 1 }}
                        disabled={isClicked}
                    >
                        Submit
                    </Button>


                    <Button
                        variant="light"
                        onClick={() => setShowModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>
        </>
    )
}

export default TakeActionModal;