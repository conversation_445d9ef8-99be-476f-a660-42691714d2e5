import React from 'react';
import ActivityForm from './ActivityForm';

const WorkAtHeightForm = () => {


    return (<>
        <div>
            <ActivityForm title={'Activity'} activities={['Activity']} vehicles={[
                "RTG",
                "QGC",
                "Forklift",
                "JLG",
                "Building / Roof",

            ]} />

            <ActivityForm title={'Cause'} activities={['Cause']} vehicles={[
                "Low Experience",
                "Didn't see / expect",
                "Mechanical",
                "Electrical",
                "Sleepy",
                "Drowsy / Drunk",
                "Natural Reasons",
                "Manual handling",
                "Lack of awareness",
                "Other"
            ]} />

            <ActivityForm title={'Impact'} activities={['Impact']} vehicles={[
                "Fall from height",
                "Drop objects",
                "Slip/Trip",
                "Collapse structure",
                "Wharf / yard damage",
                "RTG damage",
                "QC damage",
                "Container damage",
                "Other"
            ]} />


        </div>
    </>)
}

export default WorkAtHeightForm;