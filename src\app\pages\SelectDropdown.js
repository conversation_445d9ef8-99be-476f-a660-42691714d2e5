// SelectDropdown.js
import React from 'react';
import Select from 'react-select';

const SelectDropdown = ({ options, value, onChange, placeholder, index, flag }) => {
  // Adjusted onChange to include index
  const handleChange = (selectedOption) => {
    // Check if onChange handler expects an index
    if (onChange) {
      onChange(selectedOption, index);
    }
  };

  return (
    <Select
      isDisabled={flag}
      value={options.find(option => option.value === value)}
      onChange={handleChange}
      options={options}
      placeholder={placeholder || 'Choose'}
    />
  );
};
SelectDropdown.defaultProps = {
  flag: false // Default the flag prop to true
};
export default SelectDropdown;
