import React, { useState, useEffect } from "react";
import CreatableSelect from 'react-select/creatable';
import AssetListBox from "../form-elements/AssetListBox";
import { TIER1_TIER2_URL, TIER2_TIER3_URL, TIER3_TIER4_URL, CHECKLIST_URL, DOCUMENTS_URL, EDIT_TIER_URL, SUBMIT_URL, ASSET_TIER1_TIER2_URL, ASSET_TIER1_URL, EDIT_ASSET_TIER_URL } from "../constants";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';

import { deletePopup, singlePopup } from "../notifications/Swal";
import { Alert } from "react-bootstrap";
import SubmitCard from "../dashboard/SubmitCard";
import AddItem from "../form-elements/AddItem";
import API from "../services/API";

const AssetCard = () => {
    const [locations, setLocations] = useState([]);
    const [locationModal, setLocationModal] = useState(false);
    const [activityModal, setActivityModal] = useState(false);
    const [selectedLocation, setSelectedLocation] = useState({ value: '', label: '' });


    useEffect(() => {

        getChecklist();
        getDocuments();
    }, [])


    const [submitted, setSubmitted] = useState([]);
    const [filterSubmit, setFilterSubmit] = useState([]);
    useEffect(() => {
        // getSubmit();
    }, [])

    const getSubmit = async () => {

        const response = await API.get(SUBMIT_URL);
        if (response.status === 200) {
            const data = response.data;
            setSubmitted(data);


        }

    }
    const [checklist, setChecklist] = useState([]);

    const getChecklist = async () => {



        const response = await API.get(CHECKLIST_URL);
        if (response.status === 200) {
            const checklists = response.data;

            setChecklist(checklists.map(i => {
                return { value: i.id, label: i.name }
            }));
        }


    }

    const [documents, setDocuments] = useState([]);
    const getDocuments = async () => {



        const response = await API.get(DOCUMENTS_URL);
        if (response.status === 200) {
            const documents = response.data;
            setDocuments(documents.map(i => {
                return { value: i.id, label: i.name }
            }));
        }


    }




    const [title, setTitles] = useState({ tier1: 'Tier I', tier2: 'Tier II', tier3: 'Tier III', tier4: 'Tier IV' });






    const [tier1, setTier1] = useState([]);
    const [selectedTier1, setSelectedTier1] = useState({ id: '' });

    useEffect(() => {
        getTier1();
        setSelectedTier1({ id: '' });
        setSelectedTier2({ id: '' });

    }, [])

    const getTier1 = async () => {
        const response = await API.get(ASSET_TIER1_URL);
        if (response.status === 200) {
            setTier1(response.data)
        }
    }
    const handleTier1Select = (id) => {
        setSelectedTier1(tier1.find(i => i.id === id))
    }



    const getTier1Tier2 = async () => {
        const response = await API.get(ASSET_TIER1_TIER2_URL(selectedTier1.id));
        if (response.status === 200) {
            const tier2 = response.data;
            setTier2(tier2);
        }
    }

    const [tier2, setTier2] = useState([]);
    const [selectedTier2, setSelectedTier2] = useState({ id: '' });

    useEffect(() => {
        if (selectedTier1.id !== '')
            getTier1Tier2();
        setSelectedTier2({ id: '' });


    }, [selectedTier1.id])

    const handleTier2Select = (id) => {
        setSelectedTier2(tier2.find(i => i.id === id))
    }

    const createTier2 = async (value) => {
        const response = await API.post(ASSET_TIER1_TIER2_URL(selectedTier1.id), {

            name: value


        })
        if (response.status === 200) {
            const createdTier2 = response.data;
            setTier2((prev) => [...prev, createdTier2]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }



    const handleDeleteItem = async (mode, id) => {

        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await API.delete(EDIT_ASSET_TIER_URL(mode, id))

                if (response.status === 204) {
                    switch (mode) {
                        case 'tier1':
                            setTier1(prev => prev.filter(i => i.id !== id))
                            setSelectedTier1({ id: '' });
                            setSelectedTier2({ id: '' });

                            break;
                        case 'tier2':
                            setTier2(prev => prev.filter(i => i.id !== id))
                            setSelectedTier2({ id: '' });


                            break;


                        default: break;
                    }
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                }

            }
        })

    }

    const handleCloneItem = async (mode, id) => {

    }

    const handleActivityItem = async (mode, id) => {

    }
    const createTier1 = async (value) => {
        const response = await API.post(ASSET_TIER1_URL, {

            name: value


        })
        if (response.status === 200) {
            const createdTier1 = response.data;
            setTier1((prev) => [...prev, createdTier1]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }

    return (
        <>
            <div className="row">
                <div className="col-lg-12 p-0">


                    <h4 className="card-title">Asset Management</h4>




                    <>
                        <div className="row">
                            <div className="col p-1 ps-3">
                                <AssetListBox handleActivity={handleActivityItem} handleClone={handleCloneItem} handleDeleteItem={handleDeleteItem} location={selectedLocation.value} documents={documents} changeTitle={(id, value) => setTier1(prev => prev.map(i => { return i.id === id ? { ...i, name: value } : i }))} checklist={checklist} title={title.tier1} onHandleCreateItem={createTier1} lists={tier1} selected={true} handleSelect={handleTier1Select} selectedItem={selectedTier1} mode='tier1' />
                            </div>
                            <div className="col p-1">
                                <AssetListBox handleActivity={handleActivityItem} handleClone={handleCloneItem} handleDeleteItem={handleDeleteItem} location={selectedLocation.value} documents={documents} changeTitle={(id, value) => setTier2(prev => prev.map(i => { return i.id === id ? { ...i, name: value } : i }))} checklist={checklist} title={title.tier2} onHandleCreateItem={createTier2} lists={tier2} selected={selectedTier1.id !== ''} handleSelect={handleTier2Select} selectedItem={selectedTier2} mode='tier2' />
                            </div>


                        </div>
                    </>






                </div>
            </div>



            <Modal
                show={activityModal}
                onHide={() => setActivityModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Work Order History
                </Modal.Header>
                <Modal.Body>
                    {
                        filterSubmit.map(i => {
                            return (
                                <SubmitCard key={i.id} data={i} />
                            )
                        })
                    }
                    {/* <SubmitCard /> */}
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => setActivityModal(false)}>Close</Button>


                </Modal.Footer>
            </Modal>
        </>
    );

}

export default AssetCard;