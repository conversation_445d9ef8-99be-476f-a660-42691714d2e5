import React, { useEffect, useState, useRef } from "react";
import { Modal, Form, Accordion } from 'react-bootstrap';
import { Button } from 'primereact/button';
import moment from "moment";
import GalleryPage from '../apps/Gallery';
import API from "../services/API";
import { STATIC_URL, USERS_URL, DYNAMIC_TITLES_URL } from "../constants";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
const ObservationModal = ({ reportData1, showReportModal, setShowReportModal }) => {

    console.log(reportData1)
    const [reportData, setReportData] = useState(null);
    const reportRef = useRef();
    useEffect(() => {

        getDynamicTitle();



        // getAllActions();

    }, [reportData1])

    const convertImagesToDataUrls = async (data) => {
        const convertArrayToDataUrls = async (array) => {
            const imagePromises = array.map(async (item) => {
                console.log(item)
                const imageUrl = `${STATIC_URL}/${item}`;
                const response = await fetch(imageUrl);
                const blob = await response.blob();
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            });

            try {
                return await Promise.all(imagePromises);
            } catch (error) {
                console.error('Error fetching images:', error);
                return array;
            }
        };

        const uploadsDataUrls = data.uploads && await convertArrayToDataUrls(data.uploads);
        const evidenceDataUrls = data.evidence && await convertArrayToDataUrls(data.evidence);

        const actionsWithConvertedUploads = data.actions && await Promise.all(data.actions.map(async (action) => {
            const actionUploadsDataUrls = await convertArrayToDataUrls(action.uploads || []);
            return {
                ...action,
                uploads: actionUploadsDataUrls,
            };
        }));

        return {
            ...data,
            uploads: uploadsDataUrls,
            evidence: evidenceDataUrls,
            actions: actionsWithConvertedUploads,
        };
    };
    useEffect(() => {
        const fetchAndConvertImages = async () => {
            const newData = await convertImagesToDataUrls(reportData1);
            setReportData(newData);
        };
        if (reportData1 !== null) {
            getDynamicTitle();
            fetchAndConvertImages();

        }
    }, [reportData1]);

    let k = 0;
    const [actions, setActions] = useState([])
    const [users, setUsers] = useState([])
    const [title, setTitle] = useState([])

    useEffect(() => { getAllUsers() }, [])
    const generatePdf = () => {
        html2canvas(reportRef.current, { scale: 2 }).then(canvas => {
            const imgData = canvas.toDataURL('image/png');
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'px',
                format: 'a4'
            });

            // Get the width and height of the canvas
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;

            // Define the dimensions of the PDF page
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();

            // Calculate the aspect ratio of the canvas
            const canvasAspectRatio = canvasWidth / canvasHeight;
            const pdfAspectRatio = pdfWidth / pdfHeight;

            // Determine the dimensions to fit the image within the PDF page
            let imgWidth, imgHeight;

            if (canvasAspectRatio > pdfAspectRatio) {
                imgWidth = pdfWidth;
                imgHeight = pdfWidth / canvasAspectRatio;
            } else {
                imgHeight = pdfHeight;
                imgWidth = pdfHeight * canvasAspectRatio;
            }

            // Center the image on the PDF page
            const x = (pdfWidth - imgWidth) / 2;
            const y = (pdfHeight - imgHeight) / 2;

            // Add the image to the PDF
            pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);
            pdf.save('report.pdf');
        });
    };


    // const getAllActions = async () => {
    //     if (reportData.actions && reportData.actions.length > 0) {
    //         const filteredAndFormattedData = reportData.actions
    //             .filter(item => item.actionType === 'reject' || item.actionType === 'approve' || item.actionType === 'reviewer')
    //             .map(item => ({
    //                 ...item,
    //                 evidence: item.uploads.map(upload => ({
    //                     src: `${STATIC_URL}/${upload}`,
    //                     width: 4,
    //                     height: 3
    //                 })),
    //                 createdDate: moment(item.createdDate).format('Do MMM YYYY hh:mm:ss A') // Including createdDate as requested
    //             }));
    //         setActions(filteredAndFormattedData)
    //     }


    // }

    const getClosedDate = (item) => {

        return moment(item.createdDate).format('MMMM Do YYYY, h:mm:ss a')

    }

    const getDynamicTitle = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);
        setTitle(response.data)

        // setUsers(response.data)

    }


    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }

    function getName(id) {
        const user = users.find(user => user.id === id)
        return id ? user?.firstName || '' : ''
    }

    function getCompany(id) {
        const user = users.find(user => user.id === id)
        return id ? user?.company || '' : ''
    }
    const getStatus = (status) => {
        let state = '';
        switch (status) {
            case 'reject':
                state = 'Rejected';
                break;
            // case 'action_owner':
            //     state = 'Sent for Verification';
            //     break;
            case 'approve':
                state = 'Approved';
                break;
            case 'reviewer':
                state = "Sent for Verification"
                break;

        }
        return state;
    }
    const setStatus = (item) => {
        console.log(item.status)

        // return item.status;
        if (item.rectifiedStatus === 'Yes') {
            return 'Reported & Rectified on Spot';
        }
        if (item.type === 'Safe') {
            return 'Reported & Closed';
        }

        let status = ''
        switch (item.status) {
            case 'In Review':
                status = 'Actions Taken - Pending Verification'
                break;
            case 'Returned':
                status = 'Action Reassigned'
                break;
            case 'Initiated':
                status = 'Actions Assigned'
                break;
            case 'Approved':
                status = 'Action Verified - Closed'
                break;
            default:
                status = item.status
                break;
        }

        return status;

    }

    const displayTitle = (type) => {

        const locTitle = title.filter(item => item.title === type)

        return locTitle[0].altTitle

    }

    const getReviewerDate = (item) => {

        const reviewerId = item.reviewerId ? item.reviewerId : ''
        const date = item.actions?.filter(item => item.submittedById === reviewerId)
        console.log(date)
        if (date !== undefined) {
            return moment(date.createdDate).format('DD-MM-YYYY')
        }
        else {
            return ''
        }

    }

    const getVerifiedName = (action) => {
        const date = actions?.filter(item => item.actionType === 'approve')
        if (date.length) {
            return getName(date[0].assignedToId[0])
        }
    }
    const getVerifiedComments = (action) => {
        const date = actions?.filter(item => item.actionType === 'approve')
        if (date.length) {
            return date[0].comments ? date[0].comments : ''
        }

    }
    return (
        <>
            <Modal
                show={showReportModal}
                size={'xl'}
                className="medium-modal"
                onHide={() => setShowReportModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                {/* <Modal.Header>

                  


                </Modal.Header> */}
                <Modal.Body className="p-0" ref={reportRef}>
                    {
                        reportData && (<>

                            <div className="observation-report" >
                                <div className=" p-4 pb-0">
                                    <div className="d-flex justify-content-between">
                                        <h2 className="mb-3 obs-head-title">Observation Report</h2>


                                        <Button type="button" label="Download Report" outlined icon="pi pi-download" onClick={generatePdf} />

                                    </div>

                                    <div className="d-flex align-items-center mb-2">

                                        <h3 className="mb-0 me-2 obs-id">{(reportData && reportData.maskId) ? reportData.maskId : ''}</h3> <span className={`badge ${reportData.status.includes("Risk") ? 'badge-danger' : 'badge-success'}`}>
                                            {setStatus(reportData)}
                                        </span>
                                    </div>
                                    <div className="row mt-4 mb-4">
                                        <div className="col-8">
                                            <p className="obs-head-dec obs-head-color">Description</p>
                                            <p className="obs-dec obs-head-color">{reportData.description}</p>
                                        </div>
                                        <div className="col-4">
                                            <p className="obs-head-dec obs-head-color">Reported Date</p>
                                            <p className="obs-content">{reportData && moment(reportData.created).format('Do MMM YYYY')}</p>
                                        </div>
                                    </div>

                                    <div className="row mb-3">
                                        <div className="col-md-4">
                                            <p className="obs-title">Domain</p>
                                            <p className="obs-content">{reportData.category || ''}</p>
                                        </div>

                                        <div className="col-md-4">
                                            <p className="obs-title">Severity</p>
                                            <p className="obs-content">{reportData.severity || ''}</p>
                                        </div>

                                        <div className="col-md-4">
                                            <p className="obs-title">Category</p>
                                            <p className="obs-content">{reportData.rectifiedStatus === 'Yes' ? 'Minor RoS' : reportData.type === 'Safe' ? 'Positive OBS' : reportData.type === 'At Risk' ? JSON.parse(reportData.remarks).unsafeCondition === true ? "Unsafe Condition" : JSON.parse(reportData.remarks).unsafeAct === true ? "Unsafe Act" : reportData.type : reportData.type}</p>
                                        </div>


                                    </div>

                                    <div className="col-md-12">
                                        <p className="obs-title"> Images</p>
                                        {reportData.uploads && reportData.uploads.length > 0 && (
                                            // <div className="table-responsive p-0">
                                            //     <table className="table table-striped p-0">

                                            //         <tbody>
                                            //             <td className="p-0"><GalleryPage photos={reportData.uploads} /></td>
                                            //         </tbody>
                                            //     </table>
                                            // </div>
                                            <div className="d-flex">
                                                {reportData.uploads.map(item => (

                                                    <GalleryPage photos={[{
                                                        src: `${item}`,
                                                        width: 4,
                                                        height: 3
                                                    }]} />



                                                ))}
                                            </div>



                                        )}
                                    </div>
                                </div>
                                <div className="obs-section p-4">
                                    {/* <div className="row mb-3">
                                        <div className="col-md-6">

                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Due Date</p>
                                            <p className="obs-content">
                                                {reportData && reportData.dueDate
                                                    ? moment(reportData.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
                                                    : '-'}
                                            </p>
                                        </div>
                                    </div> */}

                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Reporter</p>
                                            <p className="obs-content">{reportData.submittedId && getName(reportData.submittedId)}</p>
                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Reporter's Organisation</p>
                                            <p className="obs-content"> {reportData.submittedId && getCompany(reportData.submittedId)}</p>
                                        </div>
                                    </div>
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">{displayTitle('LocationThree')}</p>
                                            <p className="obs-content">{reportData.locationThree && reportData.locationThree.name}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p className="obs-title">{displayTitle('LocationFour')}</p>
                                            <p className="obs-content">{reportData.locationFour && reportData.locationFour.name}</p>
                                        </div>

                                    </div>
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Observation Source Name</p>
                                            <p className="obs-content">
                                                {reportData.remarks && JSON.parse(reportData.remarks).obsSourceName}
                                            </p>
                                        </div>
                                    </div>


                                </div>


                                {
                                    reportData.type !== 'Safe' ?
                                        <div className="obs-section p-4">


                                            {reportData.rectifiedStatus !== 'Yes' &&

                                                <>
                                                    {reportData.actions &&

                                                        reportData.actions.map((action, i) => {


                                                            if (action.actionType === 'action_owner') {
                                                                const actionOwnerObject = reportData.actions.slice().reverse().find(item => item.actionType === "action_owner");
                                                                console.log(actionOwnerObject)

                                                                k = k + 1;

                                                                return (
                                                                    <div className="obs-section p-4">
                                                                        <div className="row mb-3">
                                                                            <div className="col-md-12">
                                                                                <div className="row">
                                                                                    <div className="col-6">
                                                                                        {k === 1 ?
                                                                                            <>

                                                                                                <p className="obs-title"> Assigned Action {reportData.maskId} - A{k} </p>
                                                                                                <p className="obs-content">{action.actionToBeTaken}</p>
                                                                                                {action.status === 'open' && <>
                                                                                                    <p className="obs-title"> Action Assignee</p>
                                                                                                    <p className="obs-content">{action.assignedToId &&
                                                                                                        getName(action.assignedToId[0]
                                                                                                        )}</p>
                                                                                                </>}


                                                                                            </>
                                                                                            :
                                                                                            <>

                                                                                                <p className="obs-title"> Action Verifier Comments & Reassigned Action {reportData.maskId} - A{k} </p>
                                                                                                <p className="obs-content">{action.comments}</p>
                                                                                                {action.status === 'open' && <>
                                                                                                    <p className="obs-title"> Action Assignee</p>
                                                                                                    <p className="obs-content">{action.assignedToId &&
                                                                                                        getName(action.assignedToId
                                                                                                        )}</p>
                                                                                                </>}

                                                                                            </>
                                                                                        }
                                                                                    </div>
                                                                                    {actionOwnerObject ?
                                                                                        action.id === actionOwnerObject.id &&

                                                                                        <div className="col-md-6">
                                                                                            <p className="obs-title">Due Date</p>
                                                                                            <p className="obs-content">
                                                                                                {reportData && reportData.dueDate
                                                                                                    ? moment(reportData.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
                                                                                                    : '-'}
                                                                                            </p>
                                                                                        </div>
                                                                                        : ''
                                                                                    }
                                                                                </div>
                                                                            </div>
                                                                        </div>


                                                                        {action.status === 'submitted' && <>
                                                                            <div className="row mb-3">
                                                                                <div className="col-md-12">
                                                                                    <p className="obs-title">Action Taken </p>
                                                                                    <p className="obs-content">{action.actionTaken}</p>
                                                                                </div>
                                                                            </div>

                                                                            <div className="row mb-3">
                                                                                <div className="col-md-6">
                                                                                    <p className="obs-title">Action Taken By</p>
                                                                                    <p className="obs-content">{action.assignedToId &&
                                                                                        getName(action.assignedToId[0]
                                                                                        )}</p>
                                                                                </div>
                                                                                <div className="col-md-6">
                                                                                    <p className="obs-title">Date</p>
                                                                                    <p className="obs-content">{getClosedDate(action)}</p>
                                                                                </div>
                                                                            </div>

                                                                            <div className="col-md-12">


                                                                                {action.uploads && <>
                                                                                    <p className="obs-title">Evidence</p>
                                                                                    <div className="d-flex">
                                                                                        {action.uploads.map(item => (<>

                                                                                            <GalleryPage photos={[{
                                                                                                src: `${item}`,
                                                                                                width: 4,
                                                                                                height: 3
                                                                                            }]} />

                                                                                        </>

                                                                                        ))}
                                                                                    </div></>
                                                                                }




                                                                            </div>
                                                                        </>
                                                                        }

                                                                    </div>
                                                                )


                                                            } else if (action.actionType === 'reviewer') {

                                                                return (
                                                                    <div className="obs-section p-4">

                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verifier</p>
                                                                                <p className="obs-content">{getName(action.assignedToId[0])}</p>
                                                                            </div>
                                                                            {/* <div className="col-md-6">
                                                                                    <p className="obs-title">Date</p>
                                                                                    <p className="obs-content">{moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a')}</p>
                                                                                </div> */}
                                                                        </div>

                                                                    </div>
                                                                )
                                                            } else if (action.actionType === 'reject' && action.status === 'submitted') {

                                                                return (
                                                                    <div className="obs-section p-4">

                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verified By</p>
                                                                                <p className="obs-content">{getName(action.assignedToId[0])}</p>
                                                                            </div>
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Date</p>
                                                                                <p className="obs-content">{moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a')}</p>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                                )
                                                            } else if (action.actionType === 'approve' && action.status === 'submitted') {
                                                                return (
                                                                    <div className="obs-section p-4">


                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verified By</p>
                                                                                <p className="obs-content">{reportData.reviewerId && getName(reportData.reviewerId)}</p>
                                                                            </div>
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Date</p>
                                                                                <p className="obs-content">{moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a')}</p>
                                                                            </div>
                                                                        </div>
                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verifier Comments</p>
                                                                                <p className="obs-content">{action.comments}</p>
                                                                            </div>
                                                                        </div>

                                                                        <span className={`badge ${reportData.status.includes("Risk") ? 'badge-danger' : 'badge-success'}`}>
                                                                            {reportData.status}
                                                                        </span>






                                                                    </div>
                                                                )
                                                            }

                                                        })}




                                                </>}





                                        </div>
                                        : ''}


                                {reportData.rectifiedStatus === 'Yes' && (<>
                                    <div className="obs-section p-4">
                                        <div className="col-md-12">



                                            <div className="row mb-3">
                                                <div className="col-md-12">
                                                    <p className="obs-title">Action Taken </p>
                                                    <p className="obs-content">{reportData.actionTaken}</p>
                                                </div>
                                            </div>
                                            <p className="obs-title">Evidence</p>
                                            <div>
                                                {reportData.evidence.map(item => (<>

                                                    <GalleryPage photos={[{
                                                        src: `${item}`,
                                                        width: 4,
                                                        height: 3
                                                    }]} />

                                                </>

                                                ))}
                                                {/* <GalleryPage photos={reportData.evidence} /> */}
                                            </div>


                                        </div>
                                    </div>
                                </>)
                                }
                                {/* {
                                    reportData.type !== 'Safe' ?

                                        <div className="obs-section p-4">

                                        
                                            {reportData.rectifiedStatus !== 'Yes' && <>
                                                <div className="row mb-3">
                                                    <div className="col-md-12">
                                                        <p className="obs-title"> Action to be taken </p>
                                                        <p className="obs-content">{reportData.actionToBeTaken}</p>
                                                    </div>
                                                </div>


                                                <div className="row mb-3">
                                                    <div className="col-md-6">
                                                        <p className="obs-title">Due Date</p>
                                                        <p className="obs-content">{reportData.dueDate}</p>
                                                    </div>
                                                    <div className="col-md-6">
                                                        <p className="obs-title">Responsibility</p>
                                                        <p className="obs-content">{reportData.actionOwnerId &&
                                                            getName(reportData.actionOwnerId)}</p>
                                                    </div>

                                                </div>
                                            </>}
                                            <div className="row mb-3">
                                                <div className="col-md-6">
                                                    <p className="obs-title">Action Taken</p>
                                                    <p className="obs-content">{reportData.actionTaken ? reportData.actionTaken : ''}</p>
                                                </div>
                                                <div className="col-md-6">
                                                    <p className="obs-title">Action Taken By</p>
                                                    <p className="obs-content">{reportData.actionOwnerId &&
                                                        getName(reportData.actionOwnerId)}</p>
                                                </div>

                                            </div>
                                            <div className="row mb-3">
                                                <div className="col-md-6">
                                                    <p className="obs-title">Comments</p>
                                                    <p className="obs-content">{reportData.actionOwnerId &&
                                                        getVerifiedComments(reportData.actions)}</p>
                                                </div>
                                                <div className="col-md-6">
                                                    <p className="obs-title">Verified By</p>
                                                    <p className="obs-content">{reportData.actionOwnerId &&
                                                        getVerifiedName(reportData.actions)}</p>
                                                </div>

                                            </div>

                                            <div className="col-md-12">
                                                <p className="obs-title">Evidence</p>
                                                {actions.map((reportData, index) => (
                                                    <div key={index}>
                                                        
                                                        {reportData.evidence && reportData.evidence.length > 0 && (<>

                                                            <GalleryPage photos={reportData.evidence} />


                                                            <p>Date: {reportData.createdDate}</p>



                                                        </>)}
                                                    </div>
                                                ))}
                                            </div>



                                        </div>
                                        : ''} */}

                                {/* <div className="obs-section p-4">
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Action Owner</p>
                                            <p className="obs-content">{reportData.actionOwnerId &&
                                                getName(reportData.actionOwnerId)}</p>
                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Rectified on Spot</p>
                                            <p className="obs-content">{reportData.type !== 'Safe' && reportData.rectifiedStatus} </p>
                                        </div>
                                    </div>

                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Action to be Taken</p>
                                            <p className="obs-content"> {reportData.actionToBeTaken && reportData.actionToBeTaken}</p>
                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Action Taken</p>
                                            <p className="obs-content"> {reportData.actionTaken && reportData.actionTaken}</p>
                                        </div>
                                    </div>

                                    <div className="row">


                                        <div className="col-md-12">
                                            <p className="obs-title">Evidences</p>
                                            {actions.map((reportData, index) => (
                                                <div key={index}>
                                                    <p>Status: <strong>{getStatus(reportData.actionType)}</strong></p>
                                                    {reportData.evidence && reportData.evidence.length > 0 && (
                                                        <div className="table-responsive mb-3">
                                                            <table className="table table-striped">
                                                                <tbody>
                                                                    <tr>
                                                                        <td><GalleryPage photos={reportData.evidence} /></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Date: {reportData.createdDate}</td>
                                                                        <td>Action By: {getName(reportData.assignedToId[0])}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>


                                    </div>


                                </div> */}
                            </div>




                        </>)
                    }
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {

                        <>
                            <Button severity="secondary" onClick={() => setShowReportModal(false)}>Close</Button>

                        </>

                    }

                </Modal.Footer>
            </Modal>
        </>
    )
}

export default ObservationModal;