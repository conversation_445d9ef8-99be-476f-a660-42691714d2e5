import React, { useState, useEffect } from "react";
import { Nav, Tab } from "react-bootstrap";
import MaterialTable from "material-table";
import { RISKASSESSMENT_ARCHIVED, RISKASSESSMENT_LIST, RISK_WITH_ID_URL } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

const RiskAssessment = () => {
  const user = useSelector((state) => state.login.user)
  console.log(user)
  const history = useHistory();
  const location = useLocation()
  const [data, setData] = useState([])
  const [risk, setRisk] = useState([])
  const [users, setUsers] = useState([])
  const [depart, setDepart] = useState([])
  const [overdue, setOverdue] = useState([])
  const [additional, setAdditional] = useState([])
  const [aOverdue, setAOverdue] = useState([])
  const [access, setAccess] = useState(false)
  const [archived, setArchived] = useState([])
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    meetid: { value: null, matchMode: FilterMatchMode.IN },
    activity: { value: null, matchMode: FilterMatchMode.IN },
    date: { value: null, matchMode: FilterMatchMode.IN },
    nextdate: { value: null, matchMode: FilterMatchMode.IN },
    'type.label': { value: null, matchMode: FilterMatchMode.IN },
    status: { value: null, matchMode: FilterMatchMode.IN },
    captain: { value: null, matchMode: FilterMatchMode.IN },
    department: { value: null, matchMode: FilterMatchMode.IN },
  });
  // const [hazard,setHazard] =useState([])
  const renderHeader = () => {
    const value = filters['global'] ? filters['global'].value : '';

    return (
      <div className='d-flex justify-content-end'>
        <span className="p-input-icon-left">
          <i className="fa fa-search" />
          <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
        </span>
      </div>
    );
  };

  const header = renderHeader();

  const onGlobalFilterChange = (event) => {
    const value = event.target.value;
    let _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
  };

  useEffect(() => {
    getPermit();
    getPermitArchived();

  }, [])

  const getPermitArchived = async () => {
    const uriString = { include: ["user"] };

    const url = `${RISKASSESSMENT_ARCHIVED}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {
      setArchived(response.data)
    }


  }
  const customSwal = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-danger',
      cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
  })
  const customSwal2 = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
  })

  const getPermit = async () => {
    const uriString = { include: ["user"] };

    const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {
      //   const utc = moment()
      //   console.log(moment.utc().format('DD-MM-YYYY'))
      response.data = response.data.filter(item =>
        item.type.label !== 'Hazard-Based' && item.type.label !== 'Topical'
      );


      const name = response.data.map(item => ({

        name: item.captain,
        value: item.captain // This adds the 
      }));

      let pp1 = name.filter((ele, ind) => ind === name.findIndex(elem => elem.value === ele.value && elem.name === ele.name))

      setUsers(pp1)


      const depart1 = response.data.map(item => ({

        name: item.department,
        value: item.department // This adds the 
      }));

      let pp22 = depart1.filter((ele, ind) => ind === depart1.findIndex(elem => elem.value === ele.value && elem.name === ele.name))

      setDepart(pp22)

      const over = response.data.filter(item =>
        moment().utc(item.nextdate, 'DD-MM-YYYY').isBefore(moment().utc().format())
      )

      setOverdue(over)
      const add = response.data.filter(item =>

        item.additional.includes('No')
      )
      console.log(add)
      setAdditional(add)

      const addControl = add.filter(item => {
        let check = []
        if (item.additionalDates) {

          item.additionalDates.map((item) => {

            item.map((item2) => {

              if (item2.date !== '') {

                const dateString = item2.date;
                const givenDate = moment(dateString);
                const today = moment();
                if (givenDate.isBefore(today)) {
                  console.log('The given date is before today.');
                  console.log(givenDate + 'before')
                  console.log(today + 'today')
                  check.push(true)
                } else if (givenDate.isAfter(today)) {
                  console.log('The given date is after today.');
                  console.log(givenDate + 'after')
                  console.log(today + 'today')
                  check.push(false)
                } else {
                  console.log('The given date is today.');
                }

              }

            })

          }

          )
        }
        return check.includes(true)

      })

      setAOverdue(addControl)

      setRisk(response.data)

    }

    if (user.length !== 0) {
      setAccess(user.roles.some(item => item.name === 'RA Team Leader'))
    }

  }
  const defaultMaterialTheme = createTheme();
  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };
  const viewRisk = (data) => {
    let id = data.id
    if (data.type.label === 'Hazard-Based') {
      history.push('/risk-assessment/viewhazard', { id })
    } else {
      history.push('/risk-assessment/viewrisk', { id })
    }
  }
  const editRisk = (data) => {
    let id = data.id
    if (data.type.label === 'Hazard-Based') {
      if (data.status === 'Draft') {
        history.push('/risk-assessment/hazarddraft', { id })
      } else {
        history.push('/risk-assessment/amendhazard', { id })
      }

    } else {
      if (data.status === 'Draft') {
        history.push('/risk-assessment/draft', { id })
      } else {
        history.push('/risk-assessment/amendrisk', { id })
      }

    }
  }

  const onDelete = async (id) => {

    customSwal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        const response = await API.delete(RISK_WITH_ID_URL(id));
        if (response.status === 204) {

          customSwal2.fire(
            'Deleted!',
            '',
            'success'
          )


        }
        getPermit();
      }
    })

  }

  const tableActions = [

    {
      icon: 'visibility',
      tooltip: 'View RA',
      onClick: (event, rowData) => {

        viewRisk(rowData)
        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      }
    },
    {
      icon: "modeEdit",
      tooltip: "Edit RA",
      onClick: (event, rowData) => {
        if (access) {
          editRisk(rowData)
        } else {
          customSwal2.fire(
            'You Cant Access',
            '',
            'success'
          )
        }
        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      },
    },
    {
      icon: "delete",
      tooltip: "Delete RA",
      onClick: (event, rowData) => {
        if (access) {
          onDelete(rowData.id)
        } else {
          customSwal2.fire(
            'You Cant Access',
            '',
            'success'
          )
        }

        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      },
    },
  ]
  const columns = [

    {
      field: 'meetid',
      title: 'RA No',


    },
    {
      field: 'activity',
      title: 'Process/Activity',
      render: (row) => {
        if (row.type.label === 'Hazard-Based') {
          return 'N/A';
        } else {
          if (row.activity === 'Others') {
            return row.activity + '-' + row.otherActivity
          } else {
            return row.activity
          }


        }
      }


    },
    {
      field: 'type.label',
      title: 'Type',

      // formatter: (cell, row) => {

      //   return (
      //     <>{row.contractor.name}</>
      //   );
      // }

    },
    {
      field: 'department',
      title: 'Initiated by',



    },


    {
      field: 'date',
      title: 'Published / Amended Date/Time',


    },
    {
      field: 'nextdate',
      title: 'Next Review Date',


    },
    {
      field: 'status',
      title: 'Status',


    },

    {
      field: 'user.firstName',
      title: 'RA Leader',


    },


  ];
  const actionBodyTemplate = (row) => {
    return (
      <div className="table-action d-flex ">
        <i className="mdi mdi-eye" onClick={() => viewRisk(row)}></i>
        {access && <>
          <i className="mdi mdi-lead-pencil" onClick={() => editRisk(row)}></i>
          <i className="mdi mdi-delete" onClick={() => onDelete(row.id)}></i>
        </>}
      </div>
    )

  }
  const typeFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={[{ name: 'Routine Work', value: 'Routine Work' }, { name: 'Non-Routine Work', value: 'Non-Routine Work' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const departFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={depart} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const leaderFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Leader</div>
        <MultiSelect value={options.value} options={users} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const statusFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Status</div>
        <MultiSelect value={options.value} options={[{ name: 'Pending', value: 'Pending' }, { name: 'Published', value: 'Published' }, { name: 'Draft', value: 'Draft' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const representativesItemTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.value}</span>
      </div>
    );
  };
  return (
    <>
      <div>
        <div className="row  mt-4">
          <div className="col-12">


            <Tab.Container id="left-tabs-example" defaultActiveKey="first">
              <div className="row mb-4">
                <div className="col-sm-12">
                  <div className="risk-details row">
                    <Nav className="d-flex w-100">
                      <Nav.Item className="flex-grow-1 text-center">
                        <Nav.Link eventKey="first" className="text-primary">
                          <div className="card card-rounded" style={{ height: 120 }}>
                            <div className="card-body">
                              <p className="statistics-title">No of RA's</p>
                              <h3 className="rate-percentage">{risk.length}</h3>
                            </div>
                          </div>
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item className="flex-grow-1 text-center">
                        <Nav.Link eventKey="second" className="text-primary">
                          <div className="card card-rounded" style={{ height: 120 }}>
                            <div className="card-body">
                              <p className="statistics-title">RA's overdue for Review</p>
                              <h3 className="rate-percentage">{overdue.length}</h3>
                            </div>
                          </div>
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item className="flex-grow-1 text-center">
                        <Nav.Link eventKey="third" className="text-primary">
                          <div className="card card-rounded" style={{ height: 120 }}>
                            <div className="card-body">
                              <p className="statistics-title">RA's requiring additional Controls</p>
                              <h3 className="rate-percentage">{additional.length}</h3>
                            </div>
                          </div>
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item className="flex-grow-1 text-center">
                        <Nav.Link eventKey="four" className="text-primary">
                          <div className="card card-rounded" style={{ height: 120 }}>
                            <div className="card-body">
                              <p className="statistics-title">RA's with overdue additional Controls</p>
                              <h3 className="rate-percentage">{aOverdue.length}</h3>
                            </div>
                          </div>
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item className="flex-grow-1 text-center">
                        <Nav.Link eventKey="five" className="text-primary">
                          <div className="card card-rounded" style={{ height: 120 }}>
                            <div className="card-body">
                              <p className="statistics-title">Archived RA's</p>
                              <h3 className="rate-percentage">{archived.length}</h3>
                            </div>
                          </div>
                        </Nav.Link>
                      </Nav.Item>
                    </Nav>
                  </div>
                </div>
              </div>

              <div className="card">
                <div className="card-body">
                  <div className="row">
                    <div className="col-12">
                      <div>
                        {/* {access &&
                          <button
                            type="button"
                            className="btn btn-primary btn-rounded mb-3 "
                            onClick={(e) => {
                              e.preventDefault();
                              history.push('/editrisk')

                            }}
                          >

                            Start Risk Assessment
                          </button>
                        } */}

                        <Tab.Content>
                          <Tab.Pane eventKey="first">
                            <>
                              {/* <ThemeProvider theme={defaultMaterialTheme}>
                                <MaterialTable
                                  columns={columns}
                                  data={risk}
                                  title="Risk Register"
                                  style={tableStyle}
                                  actions={tableActions}
                                  options={{
                                    actionsColumnIndex: -1,
                                  }}
                                />
                              </ThemeProvider> */}

                              <DataTable value={risk} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                rowsPerPageOptions={[10, 25, 50]}
                                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                                <Column field="meetid" header="ID" sortable style={{ width: '15%' }} ></Column>

                                <Column field='activity' header="Process/Activity" sortable filter style={{ width: '25%' }}></Column>
                                <Column field='department' header="Department" style={{ width: '25%' }} filterElement={departFilterTemplate} showFilterMatchModes={false} filter></Column>
                                <Column field="type.label" header="Type" filterElement={typeFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="date" header="Published / Amended Date/Time" sortable style={{ width: '25%' }}></Column>

                                <Column field="nextdate" header="Next Review Date" sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="captain" header="RA Leader" filterElement={leaderFilterTemplate} showFilterMatchModes={false} filter sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column header="Action" body={actionBodyTemplate} style={{ width: '200px' }}></Column>

                              </DataTable>
                            </>
                          </Tab.Pane>
                          <Tab.Pane eventKey="second">
                            <>
                              <DataTable value={overdue} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                rowsPerPageOptions={[10, 25, 50]}
                                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                                <Column field="meetid" header="ID" sortable style={{ width: '15%' }} ></Column>

                                <Column field='activity' header="Process/Activity" sortable filter style={{ width: '25%' }}></Column>
                                <Column field='department' header="Department" style={{ width: '25%' }} filterElement={departFilterTemplate} showFilterMatchModes={false} filter></Column>
                                <Column field="type.label" header="Type" filterElement={typeFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="date" header="Published / Amended Date/Time" sortable style={{ width: '25%' }}></Column>

                                <Column field="nextdate" header="Next Review Date" sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="captain" header="RA Leader" filterElement={leaderFilterTemplate} showFilterMatchModes={false} filter sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column header="Action" body={actionBodyTemplate} style={{ width: '200px' }}></Column>
                              </DataTable>
                            </>
                          </Tab.Pane>
                          <Tab.Pane eventKey="third">
                            <>
                              <DataTable value={additional} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                rowsPerPageOptions={[10, 25, 50]}
                                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                                <Column field="meetid" header="ID" sortable style={{ width: '15%' }} ></Column>

                                <Column field='activity' header="Process/Activity" sortable filter style={{ width: '25%' }}></Column>
                                <Column field='department' header="Department" style={{ width: '25%' }} filterElement={departFilterTemplate} showFilterMatchModes={false} filter></Column>
                                <Column field="type.label" header="Type" filterElement={typeFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="date" header="Published / Amended Date/Time" sortable style={{ width: '25%' }}></Column>

                                <Column field="nextdate" header="Next Review Date" sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="captain" header="RA Leader" filterElement={leaderFilterTemplate} showFilterMatchModes={false} filter sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column header="Action" body={actionBodyTemplate} style={{ width: '200px' }}></Column>

                              </DataTable>
                            </>
                          </Tab.Pane>
                          <Tab.Pane eventKey="four">
                            <>
                              <DataTable value={aOverdue} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                rowsPerPageOptions={[10, 25, 50]}
                                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                                <Column field="meetid" header="ID" sortable style={{ width: '15%' }} ></Column>

                                <Column field='activity' header="Process/Activity" sortable filter style={{ width: '25%' }}></Column>

                                <Column field='department' header="Department" style={{ width: '25%' }} filterElement={departFilterTemplate} showFilterMatchModes={false} filter></Column>
                                <Column field="type.label" header="Type" filterElement={typeFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="date" header="Published / Amended Date/Time" sortable style={{ width: '25%' }}></Column>

                                <Column field="nextdate" header="Next Review Date" sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="captain" header="RA Leader" filterElement={leaderFilterTemplate} showFilterMatchModes={false} filter sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column header="Action" body={actionBodyTemplate} style={{ width: '200px' }}></Column>

                              </DataTable>
                            </>
                          </Tab.Pane>

                          <Tab.Pane eventKey="five">
                            <>
                              <DataTable value={archived} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                rowsPerPageOptions={[10, 25, 50]}
                                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                                <Column field="meetid" header="ID" sortable style={{ width: '15%' }} ></Column>

                                <Column field='activity' header="Process/Activity" sortable filter style={{ width: '25%' }}></Column>

                                <Column field='department' header="Department" style={{ width: '25%' }} filterElement={departFilterTemplate} showFilterMatchModes={false} filter></Column>
                                <Column field="type.label" header="Type" filterElement={typeFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="date" header="Published / Amended Date/Time" sortable style={{ width: '25%' }}></Column>

                                <Column field="nextdate" header="Next Review Date" sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} sortable filter filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column field="captain" header="RA Leader" filterElement={leaderFilterTemplate} showFilterMatchModes={false} filter sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

                                <Column header="Action" body={actionBodyTemplate} style={{ width: '200px' }}></Column>

                              </DataTable>
                            </>
                          </Tab.Pane>
                        </Tab.Content>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Tab.Container>
          </div>
        </div>
      </div>
    </>
  );
};

export default RiskAssessment;
