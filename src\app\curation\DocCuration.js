import React, { useState } from "react";
import ToolBox from "./components/DocTool";
import FormContainer from "./components/FormContainer";
import {
    STEP_BY_ID,
    DOCUMENTS_WITH_ID_URL,
    DOC_REVIEWER_SET_BY_CREATOR
} from "../constants";
import API from "../services/API";
import { singlePopup, secondaryPopup } from "./../notifications/Swal";
import { useLocation, useHistory } from "react-router-dom/cjs/react-router-dom.min";
import DocFormContainer from "./components/DocFormContainer";
const DocCuration = (props) => {

    const location = useLocation();
    console.log(location)
    const history = useHistory()
    const [isLoading, setIsLoading] = useState(true);
    const [checklistData, setChecklistData] = useState({ name: "", value: {} });

    const setReviewer = async () => {


        const response = await fetch(DOC_REVIEWER_SET_BY_CREATOR, {
            method: "PATCH",
            body: JSON.stringify({
                status: '2',
                docId: location.state.id,
                actionId: location.state.actionId
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8",
            },
        });
        if (response.ok) {
            singlePopup.fire("Document Sent to Reviewer", "", "success");

            history.goBack()
        }


    }

    const myForm = async (form, type) => {
        console.log(form);
        let url = "";

        url = DOCUMENTS_WITH_ID_URL(location.state.id);

        const data = JSON.stringify(form);

        const response = await fetch(url, {
            method: "PATCH",
            body: JSON.stringify({
                value: data,
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8",
            },
        });
        if (response.ok) {
            secondaryPopup.fire({
                title: 'Are you sending document to Reviewer?',
                // text: "You won't be able to revert this!",
                icon: 'success',
                showCancelButton: true,
                reverseButtons: true,

                confirmButtonText: 'Send'
            }).then(async (result) => {
                if (result.isConfirmed) {

                    setReviewer();


                } else {


                }
            })

            if (type === 'exit') {
                history.goBack()
            }
        } else {
            //show error
            singlePopup.fire("Please Try Again", "", "error");
        }
    };
    const updateForm = async (callback) => {
        let url = "";

        url = DOCUMENTS_WITH_ID_URL(location.state.id);

        const response = await API.get(
            url
        );
        if (response.status === 200) {
            let form = JSON.parse(response.data.value);
            console.log(form);
            callback(form);
            setChecklistData(response.data);
            setIsLoading(false);
        }
    };
    const goBack = () => {
        history.goBack();
    }
    return (
        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className="card-body p-0" >
                        <div className="row">

                            <div
                                className="col-md-3 pr-0">
                                <ToolBox />
                                {location.state.data.comments ?
                                    <div className="col-12 card" style={{ background: '#f5f5f5' }}>
                                        <h5 className="pl-2 card-header bg-header">Comments from {location.state.data.description} </h5>
                                        <p className="p-2" dangerouslySetInnerHTML={{ __html: location.state.data.comments }} />

                                    </div> : ''

                                }

                            </div>
                            <div className="col-md-9 pl-0">
                                <DocFormContainer
                                    data={location.state.data}
                                    loader={false}
                                    debug={false}
                                    updateOnMount={true}
                                    updateForm={updateForm}
                                    onSave={myForm}
                                    goBack={goBack}
                                />
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DocCuration;
