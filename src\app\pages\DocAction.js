import React, { useEffect, useState } from 'react'
import API from '../services/API';
import { ACTION_URL, DOC_APPROVER_SET_BY_REVIEWER, DOC_APPROVER_SET_BY_APPROVER, DOC_RETURN_SET_BY_APPROVER } from '../constants';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import { Button } from 'react-bootstrap';
import DocReviewerModal from './DocReviewerModal';
import { singlePopup } from "./../notifications/Swal";

import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

function DocAction(props) {
    const [actions, setActions] = useState([])
    const [modal, setModal] = useState(false)
    const [actionDetails, setActionDetails] = useState([])
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.docId': { value: null, matchMode: FilterMatchMode.IN },
        actionType: { value: null, matchMode: FilterMatchMode.IN },
        createdDate: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }] },
        'actionSubmittedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
    });

    const history = useHistory()
    useEffect(() => {
        getActions();
    }, [])

    const getActions = async () => {
        const response = await API.get(ACTION_URL);
        if (response.status === 200) {


            const actions = response.data

                .filter(i => i.application === props.application && i.status !== 'completed')
                .reverse()


            const action = actions.map(item => ({
                ...item,
                docStatus: getStatus(item, 'status'),
                submittedBy: getStatus(item, 'submit')

            }))
            setActions(action);
        }

    }
    const openActionCard = (action) => {
        console.log(action)
        switch (action.actionType) {
            case 'doc_initiated':

                history.push('/document/curate/edit', { id: action.applicationDetails.id, actionId: action.id, data: action, type: 'document' })

                break;

            case 'doc_created':

                setActionDetails(action)
                setModal(true)

                break;
            case 'doc_reviewed':

                setActionDetails(action)
                setModal(true)


                break;
            case 'doc_returned':

                history.push('/document/curate/edit', { id: action.applicationDetails.id, actionId: action.id, data: action, type: 'document' })


                break;
        }

    }
    const handleSendToReviewer = (action) => {
        console.log(action)

    }
    const setReviewerModal = (e) => {
        setModal(e)

    }
    const handleReturnSubmit = async (data, val) => {

        const response = await fetch(DOC_RETURN_SET_BY_APPROVER, {
            method: "PATCH",
            body: JSON.stringify({
                status: '2',
                docId: data.applicationDetails.id,
                actionId: data.id,
                from: data.applicationDetails.status === '2' ? 'Reviewer' : 'Approver',
                reason: val
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8",
            },
        });
        if (response.ok) {
            singlePopup.fire("Document Returned to Author", "", "success");
            setModal(false)
            getActions()


        }


    }
    const handleReviewerSubmit = async (data) => {
        console.log(data)

        if (data.applicationDetails.status === '2') {
            const response = await fetch(DOC_APPROVER_SET_BY_REVIEWER, {
                method: "PATCH",
                body: JSON.stringify({
                    status: '3',
                    docId: data.applicationDetails.id,
                    actionId: data.id
                }),
                headers: {
                    "Content-type": "application/json; charset=UTF-8",
                },
            });
            if (response.ok) {
                singlePopup.fire("Document Sent to Approver", "", "success");
                setModal(false)
                getActions()


            }

        } else {
            const response = await fetch(DOC_APPROVER_SET_BY_APPROVER, {
                method: "PATCH",
                body: JSON.stringify({
                    status: '4',
                    docId: data.applicationDetails.id,
                    actionId: data.id
                }),
                headers: {
                    "Content-type": "application/json; charset=UTF-8",
                },
            });
            if (response.ok) {
                singlePopup.fire("Document Approved", "", "success");
                setModal(false)
                getActions()


            }
        }
    }

    const getStatus = (action, type) => {
        let displayActionType = '';
        let submittedBy = '';

        switch (action.actionType) {
            case 'doc_initiated':
                displayActionType = 'Author New Document';
                submittedBy =  action.applicationDetails?.initiator?.firstName || '';
                break;

            case 'doc_created':
                displayActionType = 'Review New Document';
                submittedBy =  action.applicationDetails?.creator?.firstName || '';
                break;

            case 'doc_reviewed':
                displayActionType = 'Approve New Document';
                submittedBy =   action.applicationDetails?.reviewer?.firstName || '';
                break;
            case 'doc_approver':
                displayActionType = 'Completed';
                submittedBy = action.applicationDetails?.DocApprover?.firstName || '';
                break;
            case 'doc_returned':
                displayActionType = 'Document Returned from ' + action.description;
                submittedBy = action.description === 'Approver' ? 'Returned by :' + action.applicationDetails?.DocApprover?.firstName || '' : 'Returned by :' + action.applicationDetails?.reviewer?.firstName || '';
                break;
        }
        if (type === 'status') {
            return displayActionType;
        } else {
            return submittedBy;
        }

    }

    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openActionCard(row)}><span className='pending'></span>{row.applicationDetails.docId}</div>;
    }

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
                
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span>
            </div>
        );
    };
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };
    const header = renderHeader();
    return (
        <>






            {/* 
                        // <div key={action.id} className="card cursor-pointer zoom-on-hover shadow mb-3" onClick={() => openActionCard(action)}>
                        //     <div className="card-body">
                        //         <div className="d-flex justify-content-between">
                        //             <p className="font-weight-bold">{action.applicationDetails?.docId}</p>

                        //             <p className="font-weight-bold">{displayActionType}</p>
                        //         </div>
                        //         <p className="font-weight-bold">{action.applicationDetails?.name}</p>



                        //         <p> {submittedBy}</p>
                        //         <p>Submitted on: {action.createdDate}</p>
                        //     </div>
                        // </div> */}

            <DataTable value={actions} paginator rows={10} globalFilterFields={["applicationDetails.docId", "createdDate", "submittedBy"]} header={header} filters={filters} onFilter={(e) => { if (e.filters['actionSubmittedBy.firstName'] !== undefined) { e.filters['actionSubmittedBy.firstName'].matchMode = 'in'; setFilters(e.filters) } else { setFilters(e.filters) } }}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                <Column field="applicationDetails?.docId" header="ID" body={idBodyTemplate} sortable style={{ width: '25%' }}  ></Column>

                <Column field='docStatus' header="Status"  sortable  style={{ width: '25%' }}></Column>

                <Column field="createdDate" header="Submitted On" sortable  style={{ width: '25%' }}></Column>

                <Column field="submittedBy" header="Submitted By"  sortable  style={{ width: '25%' }}></Column>

            </DataTable>



            {
                !actions.length > 0 && <p className='text-center'>No Actions</p>
            }

            {(modal && Object.keys(actionDetails).length > 0) && (
                <DocReviewerModal showModal={modal} setShowModal={setReviewerModal} data={actionDetails} handleSubmitData={handleReviewerSubmit} handleReturn={handleReturnSubmit} />
            )}

        </>
    )
}

export default DocAction