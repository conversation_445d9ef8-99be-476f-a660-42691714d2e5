import React from 'react';
import InvoiceRow from './InvoiceRow';

function InvoiceTable({ items, onDelete, onEdit, editable, currency, exchangeRate }) {

    const totalCost = items.reduce((total, item) => total + item.quantity * item.price, 0);


    return (
        <table className="table table-responsive">
            <thead>
                <tr>
                    <th>Line Item</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit Cost ({currency})</th>
                    <th>Remarks</th>
                    <th>Total</th>
                    {editable && <th>Actions</th>}
                </tr>
            </thead>
            <tbody>
                {items.map((item, index) => (
                    <InvoiceRow key={index} id={index + 1} item={item} onDelete={onDelete} onEdit={onEdit} editable={editable} currency={currency} exchangeRate={exchangeRate} />
                ))}
            </tbody>
            <tfoot>
                <tr>
                    <td colSpan="5" className="text-right"><strong>Total:</strong></td>
                    <td><strong>{currency === 'USD' ? parseFloat(totalCost).toFixed(2) * exchangeRate : parseFloat(totalCost).toFixed(2)}</strong></td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
    );
}

export default InvoiceTable;
