// @ts-nocheck
import React, { useState, useRef, useEffect } from "react";
import { useHistory } from "react-router-dom";
import {
  ENTERPRISE_USER_OWNER_URL,
  ENTERPRISE_USER_URL,
  GROUP_URL,
  GROUP_WITH_ID_URL,
  ASSIGN_USER_GROUP,
  GROUP_USERS_WITH_ID,
  USERS_URL
} from "../constants";
import MaterialTable from "material-table";
import { Modal, Button, Form } from "react-bootstrap";
import { ThemeProvider, createTheme } from "@mui/material";
import cogoToast from "cogo-toast";
import Loader from "../shared/Loader";
import Swal from "sweetalert2";
import { useSelector } from "react-redux";
// @ts-ignore
import { Link } from "react-router-dom";
// import SagoTable from "../tables/SagoTable";
import API from "../services/API";
import CheckboxTree from "react-checkbox-tree";
import "react-checkbox-tree/lib/react-checkbox-tree.css";
const customSwal = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-danger",
    cancelButton: "btn btn-light",
  },
  buttonsStyling: false,
});

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-primary",
  },
  buttonsStyling: false,
});

const customSwal3 = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-primary",
    cancelButton: "btn btn-light",
  },
  buttonsStyling: false,
});
const defaultMaterialTheme = createTheme();
const tableStyle = {
  borderRadius: '0',
  boxShadow: 'none',
};
const Groups = (props) => {
  const [mdShow, setMdShow] = useState(false);
  const [userShow, setUserShow] = useState(false);
  const [smShow, setSmShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [user, setUser] = useState("");
  const [checked, setChecked] = useState([]);
  const [expanded, setExpanded] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [filteredNode, setFilteredNode] = useState([]);
  const [notes, setNotes] = useState([]);
  const history = useHistory();
  const [selectedFile, setSelectedFile] = useState();
  const dName = useRef();
  const gName = useRef();
  const [userinfo, setUserInfo] = useState([]);

  useEffect(() => {

    getChecklist();


  }, []);

  const getChecklist = async () => {
    const response = await API.get(GROUP_URL);
    if (response.status === 200) {
      setData(response.data);
    }
  };
  const getGroupUsers = async (item) => {
    const response = await API.get(GROUP_USERS_WITH_ID(item.id));
    if (response.status === 200) {
      let data = []
      response.data.map((item) =>
        data.push(item.userId)
      )
      setChecked(data)
    }
  }
  const getOwnerList = async () => {
    const response = await API.get(USERS_URL);
    if (response.status === 200) {

      // const data = response.data.filter((item) =>
      //   item.roles.includes("user")
      // );


      const usr = response.data.map((u) => {
        return { label: u.firstName, value: u.id }
      })
      console.log(usr)
      setNotes(usr)
      setFilteredNode(usr);
    }
  }
  const tableActions = [

    {
      icon: 'groupadd',
      tooltip: 'View Risk',
      onClick: (event, rowData) => {

        setUserShow(true);
        setUser(rowData);
        getOwnerList();
        getGroupUsers(rowData)
        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      }
    },
    {
      icon: "modeEdit",
      tooltip: "Edit Risk",
      onClick: (event, rowData) => {
        setSmShow(true);
        setUser(rowData);

      },
    },
    {
      icon: "delete",
      tooltip: "Delete Risk",
      onClick: (event, rowData) => {
        onDeleteGroup(rowData.id)

        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      },
    },
  ]
  const columns = [
    {
      field: "name",
      title: "Name",
      sort: true,
    },
    {
      field: "createdAt",
      title: "Created Date",
      sort: true,
    },


  ];

  const onDeleteGroup = (id) => {
    customSwal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        const response = await API.delete(GROUP_WITH_ID_URL(id));
        if (response.status === 204) {
          customSwal2.fire(
            'Deleted!',
            '',
            'success'
          )
          getChecklist();
        }
      }
    })

  }
  const updateGroupHandler = async () => {
    const response = await API.patch(GROUP_WITH_ID_URL(user.id), {
      name: gName.current.value
    });
    if (response.status === 204) {
      cogoToast.info('Updated', { position: 'top-right' })
      setSmShow(false)
      getChecklist();
    }
  }

  const onFilterChange = (e) => {
    setFilterText(e.target.value);
    filterTree();
  };
  const filterTree = () => {
    if (!filterText) {
      setFilteredNode(notes);

      return;
    }
    setFilteredNode(notes.reduce(filterNodes, []));
    // this.setState({
    //     filteredNodes: nodes.reduce(this.filterNodes, []),
    // });
  };

  const filterNodes = (filtered, node) => {
    const children = (node.children || []).reduce(filterNodes, []);

    if (
      // Node's label matches the search string
      node.label.toLocaleLowerCase().indexOf(filterText.toLocaleLowerCase()) >
        -1 ||
      // Or a children has a matching node
      children.length
    ) {
      filtered.push({ ...node, children });
    }

    return filtered;
  };

  const createDocumentHandler = async () => {
    // @ts-ignore


    const response = await fetch(GROUP_URL, {
      method: "POST",
      body: JSON.stringify({
        name: dName.current.value,
      }),
      headers: { "Content-type": "application/json; charset=UTF-8" },
    });

    if (response.ok) {
      customSwal2.fire("Group Created!", "", "success");
    } else {
      //show error
      customSwal2.fire("Please Try Again!", "", "error");
      setIsLoading(false);
    }


    dName.current.value = "";

    setMdShow(false);
    getChecklist();
  };

  const assignUserGroup = async () => {
    const response = await API.post(ASSIGN_USER_GROUP, {
      groupsId: user.id,
      userId: checked
    });
    if (response.status === 204) {
      cogoToast.info('Assigned !', { position: 'top-right' })
      setUserShow(false)

    }
  }
  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">
                <h4 className="card-title">Groups Management</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      <button
                        type="button"
                        className="btn btn-primary btn-rounded mb-3 "
                        onClick={(e) => {
                          e.preventDefault();
                          setMdShow(true);
                        }}
                      >
                        <i className="mdi mdi-account-plus mr-2" /> Add Group
                      </button>
                      <ThemeProvider theme={defaultMaterialTheme}>
                        <MaterialTable
                          columns={columns}
                          data={data}
                          title=""
                          style={tableStyle}
                          actions={tableActions}
                          options={{
                            actionsColumnIndex: -1,
                          }}
                        />
                      </ThemeProvider>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        show={smShow}
        size="md"
        onHide={() => setSmShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Header closeButton>
          <Modal.Title>Edit Group Name</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {user === "" ? (
            ""
          ) : (
            <>
              <form className="forms">
                <div className="form-group">
                  <label htmlFor="document_name">Group Name</label>
                  <Form.Control
                    type="text"
                    ref={gName}
                    id="document_name"
                    placeholder="Enter Group Name"
                    defaultValue={user.name}
                  />
                </div>
              </form>
            </>
          )}
        </Modal.Body>
        <Modal.Footer className="flex-wrap">
          {isLoading ? (
            <Loader />
          ) : (
            <>
              <Button variant="light" onClick={() => setSmShow(false)}>
                Cancel
              </Button>
              <Button variant="primary" onClick={updateGroupHandler}>
                Update
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>
      <Modal
        show={mdShow}
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="document_name">Group Name</label>
              <Form.Control
                type="text"
                ref={dName}
                id="document_name"
                placeholder="Enter Group Name"
              />
            </div>
          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">
          {isLoading ? (
            <Loader />
          ) : (
            <>
              <Button variant="light" onClick={() => setMdShow(false)}>
                Cancel
              </Button>
              <Button variant="primary" onClick={createDocumentHandler}>
                Create
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>
      <Modal
        show={userShow}
        onHide={() => setUserShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Header closeButton>
          <Modal.Title>Assign User</Modal.Title>
        </Modal.Header>
        <Modal.Body >
          <div className="col-12">
          <div className="form-group">
              <Form.Control
                type="text"
                value={filterText}
                id="document_name"
                placeholder="Search..."
                onChange={(e) => onFilterChange(e)}
              />
            </div>
          </div>
          <div className="col-12" style={{ height: 300, overflowY: 'auto' }}>
           
            <CheckboxTree
              nodes={filteredNode}
              checked={checked}
              expanded={expanded}
              onCheck={(checked) => setChecked(checked)}
              onExpand={(expanded) => setExpanded(expanded)}
            />
          </div>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">
          {isLoading ? (
            <Loader />
          ) : (
            <>
              <Button variant="light" onClick={() => setUserShow(false)}>
                Cancel
              </Button>
              <Button variant="primary" onClick={assignUserGroup}>
                Assign
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default Groups;
