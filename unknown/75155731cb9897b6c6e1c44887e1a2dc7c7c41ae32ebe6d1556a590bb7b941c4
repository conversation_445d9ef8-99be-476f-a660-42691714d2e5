import React, { useEffect, useRef } from 'react';
import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist/build/pdf';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';

// Set the worker source for PDF.js
GlobalWorkerOptions.workerSrc = pdfjsWorker;

const PdfViewer = ({ blobUrl }) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    const fetchPdf = async () => {
      if (blobUrl) {
        // Load the PDF document
        const loadingTask = getDocument(blobUrl);

        try {
          const pdf = await loadingTask.promise;
          const page = await pdf.getPage(1); // Example: Get the first page
          const viewport = page.getViewport({ scale: 1.5 });

          // Prepare canvas using PDF page dimensions
          const canvas = canvasRef.current;
          const context = canvas.getContext('2d');
          canvas.height = viewport.height;
          canvas.width = viewport.width;

          // Render PDF page into canvas context
          const renderContext = {
            canvasContext: context,
            viewport: viewport,
          };

          await page.render(renderContext).promise;
        } catch (error) {
          console.error('Error loading PDF: ', error);
        }
      }
    };

    fetchPdf();
  }, [blobUrl]);

  return <canvas ref={canvasRef} />;
};

export default PdfViewer;
