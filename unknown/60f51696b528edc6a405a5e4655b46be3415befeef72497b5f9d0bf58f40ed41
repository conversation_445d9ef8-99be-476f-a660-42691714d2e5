import React from 'react';

function InvoiceRow({ id, item, onDelete, onEdit, editable, currency, exchangeRate }) {
  const total = currency === 'USD' ? item.quantity * item.price * exchangeRate : item.quantity * item.price;

  return (
    <tr>
      <td>{id}</td>
      <td>{item.description}</td>
      <td>{item.quantity}</td>
      <td>{currency === 'USD' ? item.price * exchangeRate : item.price}</td>
      <td>{item.remarks}</td>
      <td>{total}</td>
      {editable && <td>
        <button className="btn btn-danger btn-sm mb-3" onClick={() => onDelete(item)}>Delete</button>
        <button className="btn btn-primary btn-sm ms-3 mb-3" onClick={() => onEdit(item)}>Edit</button>
      </td>}
    </tr>
  );
}

export default InvoiceRow;
