import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';

const AirCard = () => {
    const [injury, setInjury] = useState(false)
    const [firstAid, setFirstAid] = useState(false)
    const [personInjured, setPersonInjured] = useState(false);
    const [medicalTreatment, setMedicalTreatment] = useState(false);
    const [medicalLeave, setMedicalLeave] = useState(false);
    const [option8, setOption8] = useState("");
    const [option7, setOption7] = useState("");
    const [option6, setOption6] = useState("");
    const [option5, setOption5] = useState("");
    const [option4, setOption4] = useState("");
    const [option3, setOption3] = useState("");
    const [option2, setOption2] = useState("");
    const [option1, setOption1] = useState("");

    const [status, setStatus] = useState("");
    const [remarks, setRemarks] = useState("");

    useEffect(() => {

        if (option2 === "No. But, it could have" || option3 === "No. But, it could have" || option4 === "No. But, it could have") {
            setRemarks('Potentially Serious Incident')
        }

        if (option6 === "Yes") {
            setStatus("Level 1");


        }
        if (option5 === "Yes") {
            setStatus("Level 2");

        }
        if (option4 === "Yes" || option4 === "No. But, it could have") {
            setStatus("Level 3");
            if (option4 === "Yes") {
                setRemarks('')
            }

        }

        if (option3 === "Yes" || option3 === "No. But, it could have") {
            setStatus("Level 4");
            if (option3 === "Yes") {
                setRemarks('')
            }
        }

        if (option2 === "Yes") {
            setStatus("Level 5");
            if (option2 === "Yes") {
                setRemarks('')
            }
        }

        if (option2 === "No. But, it could have") {
            setStatus("Level 4");
        }
        if (option1 === "Yes" && option2 !== "Yes") {
            setStatus("Level 4");
        }

        if (option3 === "No" && option2 === "No" && option4 === "No") {
            setRemarks('')
        }
        if (
            option6 === "No" &&
            option5 === "No" &&
            option4 === "No" &&
            option3 === "No" &&
            option2 === "No" &&
            option1 === "No"
        ) {
            setStatus("Near Miss");

            setRemarks('');

        }
    }, [option6, option5, option4, option3, option2, option1]);

    const [InformationData, setInformationData] = useState({
        whenIncidentHappen: "",
        files: [],
        personInvolved: "",
        witness: "",
        howIncidentHappen: "",
        equipment: "",
        activitiesPerformed: "",
        unsualActivity: "",
        safeProcedure: "",
        administeredBy: "",
        medicalFaciltiyName: "",
        driverName: "",
        treatmentDescription: "",
        medicalLeaveDays: 0,
        regularWorkDate: "",
        modifiedWorkDate: "",
        regularWork: false,
        modifiedWork: false,
        ambulance: false,
        companyVehicle: false,
        privateVehicle: false,
        body: {
            head: {
                show: true,
                selected: false
            },
            left_shoulder: {
                show: true,
                selected: false
            },
            right_shoulder: {
                show: true,
                selected: false
            },
            left_arm: {
                show: true,
                selected: false
            },
            right_arm: {
                show: true,
                selected: false
            },
            chest: {
                show: true,
                selected: false
            },
            stomach: {
                show: true,
                selected: false
            },
            left_leg: {
                show: true,
                selected: false
            },
            right_leg: {
                show: true,
                selected: false
            },
            left_hand: {
                show: true,
                selected: false
            },
            right_hand: {
                show: true,
                selected: false
            },
            left_foot: {
                show: true,
                selected: false
            },
            right_foot: {
                show: true,
                selected: false
            }
        }

    })

    const [riskControl, setRiskControl] = useState({
        immediateActions: [
            {
                date: "",
                description: ""
            }
        ],
        controlMeasures: [
            {
                completionDate: "",
                personResponsible: "",
                controlMeasures: ""
            }
        ],
        riskAssessment: [
            {
                name: "",
                completionDate: "",
                personResponsible: ""
            }
        ]
    })

    const addImmediateAction = () => {
        setRiskControl(prevState => ({
            ...prevState,
            immediateActions: [...prevState.immediateActions, { date: "", description: "" }],
        }));
    };

    const handleImmediateActionChange = (index, field, value) => {
        setRiskControl(prevState => {
            const updatedActions = [...prevState.immediateActions];
            updatedActions[index][field] = value;
            return { ...prevState, immediateActions: updatedActions };
        });
    };

    const addcontrolMeasures = () => {
        setRiskControl(prevState => ({
            ...prevState,
            controlMeasures: [...prevState.controlMeasures, {
                completionDate: "",
                personResponsible: "",
                controlMeasures: ""
            }],
        }));
    };

    const handlecontrolMeasuresChange = (index, field, value) => {
        setRiskControl(prevState => {
            const updatedActions = [...prevState.controlMeasures];
            updatedActions[index][field] = value;
            return { ...prevState, controlMeasures: updatedActions };
        });
    };

    const addRiskAssessment = () => {
        setRiskControl(prevState => ({
            ...prevState,
            riskAssessment: [...prevState.riskAssessment, {
                name: "",
                completionDate: "",
                personResponsible: ""
            }],
        }));
    };

    const handleRiskAssessmentChange = (index, field, value) => {
        setRiskControl(prevState => {
            const updatedActions = [...prevState.riskAssessment];
            updatedActions[index][field] = value;
            return { ...prevState, riskAssessment: updatedActions };
        });
    };


    const handleDeleteImmediateAction = (index) => {
        const newImmediateActions = [...riskControl.immediateActions];
        newImmediateActions.splice(index, 1);
        setRiskControl(prevState => ({ ...prevState, immediateActions: newImmediateActions }));
    };

    const handleDeleteControlMeasure = (index) => {
        const newControlMeasures = [...riskControl.controlMeasures];
        newControlMeasures.splice(index, 1);
        setRiskControl(prevState => ({ ...prevState, controlMeasures: newControlMeasures }));
    };

    const handleDeleteRiskAssessment = (index) => {
        const newRiskAssessments = [...riskControl.riskAssessment];
        newRiskAssessments.splice(index, 1);
        setRiskControl(prevState => ({ ...prevState, riskAssessment: newRiskAssessments }));
    };

    const steps = [

        {
            label: 'Investigation Team',
            description: (
                <>
                    <p className="h5 mb-4">Team Members</p>
                    {riskControl.immediateActions.map((action, index) => (
                        <div className="form-group" key={index}>
                            <label className='col-6'>
                                Name
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.date}
                                    onChange={(e) =>
                                        handleImmediateActionChange(index, "date", e.target.value)
                                    }
                                />
                            </label>

                            <label className='col-6'>
                                Designation
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.description}
                                    onChange={(e) =>
                                        handleImmediateActionChange(index, "description", e.target.value)
                                    }
                                />
                            </label>
                            <button
                                type="button"
                                className="btn btn-danger"
                                onClick={() => handleDeleteImmediateAction(index)}
                            >
                                Delete
                            </button>
                        </div>
                    ))}
                    <button variant="light" className='btn btn-light mb-4' type="button" onClick={addImmediateAction}>
                        Add More
                    </button>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Purpose of Investigation</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Consequence of
                                    Interest</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Timeframe of
                                    Interest</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">People of
                                    Interest</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Equipment of
                                    Interest</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Activities of
                                    Interest</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Geographical
                                    Boundaries of
                                    Interest</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>
                </>
            )
        },
        {
            label: 'Sequence of Events Leading to Damage / Incident',
            description: (
                <>
                    <p className="h5 mb-4"></p>
                    {riskControl.controlMeasures.map((action, index) => (
                        <div className="form-group" key={index}>
                            <label className='col-12'>
                                Description
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.controlMeasures}
                                    onChange={(e) =>
                                        handlecontrolMeasuresChange(index, "controlMeasures", e.target.value)
                                    }
                                />
                            </label>

                            <label className='col-6'>
                                Date
                                <input
                                    className="form-control"
                                    type="date"
                                    value={action.completionDate}
                                    onChange={(e) =>
                                        handlecontrolMeasuresChange(index, "completionDate", e.target.value)
                                    }
                                />
                            </label>

                            <label className='col-6'>
                                
                                <input
                                    className="form-control"
                                    type="hidden"
                                    value={action.personResponsible}
                                    onChange={(e) =>
                                        handlecontrolMeasuresChange(index, "personResponsible", e.target.value)
                                    }
                                />
                            </label>
                            <button
                                type="button"
                                className="btn btn-danger"
                                onClick={() => handleDeleteControlMeasure(index)}
                            >
                                Delete
                            </button>
                        </div>
                    ))}
                    <button variant="light" className='btn btn-light mb-4' type="button" onClick={addcontrolMeasures}>
                        Add More
                    </button>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Incident Description</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <DropzoneArea
                                    acceptedFiles={[
                                        'application/pdf',
                                        'image/jpeg',
                                        'image/png',
                                        

                                    ]}
                                    dropzoneText={"Drag and drop files that includes sketch/pictures"}
                                    filesLimit={5}
                                    maxFileSize={104857600}
                                    onChange={handleFileChange}
                                />
                            </div>
                        </div>
                    </div>
                </>
            ),
        },
        {
            label: 'Information gathering (A. People)',
            description:
                (<>

                    <p className="h5 mb-4">A. People</p>
                    {riskControl.controlMeasures.map((action, index) => (
                        <div className="form-group" key={index}>
                            <label className='col-12'>
                                Name
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.controlMeasures}
                                    onChange={(e) =>
                                        handlecontrolMeasuresChange(index, "controlMeasures", e.target.value)
                                    }
                                />
                            </label>

                            <label className='col-6'>
                                Company / Contractor
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.completionDate}
                                    onChange={(e) =>
                                        handlecontrolMeasuresChange(index, "completionDate", e.target.value)
                                    }
                                />
                            </label>

                            <label className='col-6'>
                                Role / Involvement
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.personResponsible}
                                    onChange={(e) =>
                                        handlecontrolMeasuresChange(index, "personResponsible", e.target.value)
                                    }
                                />
                            </label>
                            <button
                                type="button"
                                className="btn btn-danger"
                                onClick={() => handleDeleteControlMeasure(index)}
                            >
                                Delete
                            </button>
                        </div>
                    ))}
                    <button variant="light" className='btn btn-light mb-4' type="button" onClick={addcontrolMeasures}>
                        Add More
                    </button>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Name of Injured Person?</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Date of Birth?</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Gender</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Nationality</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Trade Activity</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Responsibility</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A2. What injury, ill-health or damage was caused?</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A3. What did the people involved in the incident do/not do that was essential to continuing the incident
                                    sequence?</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A4. Consider - Physical capability or condition / Mental state / Behaviour / Culture or custom.</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A5. Consider - Skill or competence level.</label>
                                <input  style={{color: 'black'}} type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A6. Who were the first responders and what actions were taken?</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                </>)
        },
        {
            label: 'Information Gathering (B. Equipment)',
            description: (<>
                <p className="h5 mb-4">(List the ‘Equipment’ elements involved in the immediate circumstances of the incident –
                    plant, machinery, equipment, tools, PPE, etc.)</p>
                {riskControl.immediateActions.map((action, index) => (
                    <div className="form-group" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleImmediateActionChange(index, "description", e.target.value)
                                }
                            />
                        </label>
                        <button
                            type="button"
                            className="btn btn-danger"
                            onClick={() => handleDeleteImmediateAction(index)}
                        >
                            Delete
                        </button>
                    </div>
                ))}
                <button variant="light" className='btn btn-light mb-4' type="button" onClick={addImmediateAction}>
                    Add More
                </button>


                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B1. Were the equipment in good working condition?</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B2. How were the equipment used?</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B3. Was the shape / nature of the equipment relevant to the incident? Describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B4. Was difficulty / unfamiliarity in using the equipment, etc. a contributory factor? If “Yes”,
                                describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B5. Were the required safety controls implemented to address potential risks from the equipment?
                                Describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B6. Was safety equipment (e.g., PPE) adequate to operate the equipment in a safe manner?
                                Describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>





            </>),
        },
        {
            label: 'Information Gathering (C. Material)',
            description: (<>

                <p className="h5 mb-4">List the 'Material' elements involved in the immediate circumstances of the incident</p>
                {riskControl.immediateActions.map((action, index) => (
                    <div className="form-group" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleImmediateActionChange(index, "description", e.target.value)
                                }
                            />
                        </label>
                        <button
                            type="button"
                            className="btn btn-danger"
                            onClick={() => handleDeleteImmediateAction(index)}
                        >
                            Delete
                        </button>
                    </div>
                ))}
                <button variant="light" className='btn btn-light mb-4' type="button" onClick={addImmediateAction}>
                    Add More
                </button>


                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C1. How were the materials used?</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C2. Was the shape / nature / property of the materials relevant to the incident? Describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C3. Was difficulty / unfamiliarity in handling the materials, etc. a contributory factor? If “Yes”, describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C4. Were there any safety controls required as per manufacturer’s requirements and were they
                                implemented? Describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>



            </>)
        }, {
            label: 'Information Gathering (D. Environment)',
            description: (<>

                <p className="h5 mb-4">List the 'Environment' elements involved in the immediate circumstances of the
                    incident</p>
                {riskControl.immediateActions.map((action, index) => (
                    <div className="form-group" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleImmediateActionChange(index, "description", e.target.value)
                                }
                            />
                        </label>
                        <button
                            type="button"
                            className="btn btn-danger"
                            onClick={() => handleDeleteImmediateAction(index)}
                        >
                            Delete
                        </button>
                    </div>
                ))}
                <button variant="light" className='btn btn-light mb-4' type="button" onClick={addImmediateAction}>
                    Add More
                </button>


                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D1. Location of the incident on the project / operation:
                                What were the positions of all parties (injured party / witnesses), any machinery, materials, barriers,
                                signs, protections, tools &amp; equipment, etc.?
                                (Provide plan)</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D2. Were there anything unusual about the working conditions and work environment? Describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D3. Were maintenance, workplace layout and/or housekeeping relevant factors? If so, what were they?</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D4. What other features of the environment were present/absent that was essential to contribute to the
                                incident occurring? Describe.</label>
                            <input type='text' value={""} className='form-control' />
                        </div>
                    </div>
                </div>
            </>)
        }, {
            label: 'Information Gathering (E. Method)',
            description: (
                <>

                    <p className="h5 mb-4">List the 'Documents' elements involved in the immediate circumstances of the
                        incident</p>
                    {riskControl.immediateActions.map((action, index) => (
                        <div className="form-group" key={index}>


                            <label className='col-12'>

                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.description}
                                    onChange={(e) =>
                                        handleImmediateActionChange(index, "description", e.target.value)
                                    }
                                />
                            </label>
                            <button
                                type="button"
                                className="btn btn-danger"
                                onClick={() => handleDeleteImmediateAction(index)}
                            >
                                Delete
                            </button>
                        </div>
                    ))}
                    <button variant="light" className='btn btn-light mb-4' type="button" onClick={addImmediateAction}>
                        Add More
                    </button>


                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E1. What documentation is relevant and are they adequate? Describe.
                                    Paper evidence includes all relevant documentation e.g., risk assessments and risk registers / JSA or
                                    safety method statements / EHS plans / drawings / instructions / permits / certification (test,
                                    examination, training), licenses / induction &amp; toolbox talk registers.</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E2. Were the organisation and arrangements for the works contributory factors? Describe.</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E3. What other aspects of Systems and Processes were contributory to the incident occurring?
                                    Describe.</label>
                                <input type='text' value={""} className='form-control' />
                            </div>
                        </div>
                    </div>


                </>
            )
        }

    ];

    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    return (
        <>
            <Modal
                show={showModal}
                size="md"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    Investigation Part
                </Modal.Header>

                <Modal.Body>

                    <Box>
                        <Stepper activeStep={activeStep} orientation="vertical">
                            {steps.map((step, index) => (
                                <Step key={step.label}>
                                    <StepLabel>
                                        {step.label}
                                    </StepLabel>
                                    <StepContent>
                                        <Typography>{step.description}</Typography>
                                        <Box sx={{ mb: 2 }}>
                                            <div>

                                                {index === steps.length - 1 ? (
                                                    <>
                                                        <div className='form-group'>
                                                            <select onChange={(e) => setSelectedReviewer(e.target.value)} className='form-control'>
                                                                <option value={''}>Choose Incident Owner</option>
                                                                {
                                                                    incidentReviewer.map(user => {
                                                                        return (
                                                                            <option value={user.id}>{user.firstName}</option>
                                                                        )
                                                                    })
                                                                }
                                                            </select>
                                                        </div>
                                                        <Button
                                                            variant="light"
                                                            className='me-2 mt-2'
                                                            onClick={handleSubmit}
                                                            sx={{ mt: 1, mr: 1 }}
                                                        >
                                                            Submit to Incident Owner
                                                        </Button>
                                                    </>

                                                ) : (

                                                    <Button
                                                        variant="light"
                                                        className='me-2 mt-2'
                                                        onClick={handleNext}
                                                        sx={{ mt: 1, mr: 1 }}
                                                    >
                                                        Continue
                                                    </Button>
                                                )}

                                                <Button
                                                    disabled={index === 0}
                                                    className='mt-2'
                                                    onClick={handleBack}
                                                    sx={{ mt: 1, mr: 1 }}
                                                >
                                                    Back
                                                </Button>
                                            </div>
                                        </Box>
                                    </StepContent>
                                </Step>
                            ))}
                        </Stepper>
                        {activeStep === steps.length && (
                            <Paper square elevation={0} sx={{ p: 3 }}>
                                <Typography>Submitted to Incident Owner</Typography>

                            </Paper>
                        )}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>

                </Modal.Footer>
            </Modal>
        </>
    )
}

export default AirCard;