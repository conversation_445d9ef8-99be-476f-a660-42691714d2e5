import React from 'react';

const TotalCalculator = ({ apiData, currency, exchangeRate }) => {
    const titleTotals = {}; // To store title-specific totals
    let overallTotal = 0; // To store the overall total

    // Calculate the title-specific totals and overall total
    apiData.forEach(data => {
        const title = data.title;
        const items = data.items;

        items.forEach(item => {
            if (!titleTotals[title]) {
                titleTotals[title] = 0;
            }
            titleTotals[title] += item.quantity * item.price;
            overallTotal += item.quantity * item.price;
        });
    });

    return (
        <div className='text-right mt-5'>
            <ul className='list-unstyled'>
                {Object.keys(titleTotals).map(title => (
                    <li key={title}>
                        {title}: {currency === 'USD' ? titleTotals[title] * exchangeRate : titleTotals[title]}
                    </li>
                ))}
            </ul>
            <h5>Overall Total: {currency === 'USD' ? overallTotal * exchangeRate : overallTotal}</h5>
        </div>
    );
};

export default TotalCalculator;
