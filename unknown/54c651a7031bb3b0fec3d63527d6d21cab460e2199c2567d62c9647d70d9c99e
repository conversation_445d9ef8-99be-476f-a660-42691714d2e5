import React, { useState } from "react";
import InvoiceForm from './InvoiceForm';
import InvoiceTable from './InvoiceTable';

const InvoiceGenerator = (props) => {

    const [invoiceItems, setInvoiceItems] = useState(props.initialItems || []);
    const [editingItem, setEditingItem] = useState(null);

    const addInvoiceItem = (item) => {
        const newItems = [...invoiceItems, item];
        setInvoiceItems(newItems);
        props.addCombinedInvoiceItem(item, props.title);
        updateApiDataWithNewItems(newItems); // Update apiData when adding
    };

    const handleDeleteItem = (itemToDelete) => {
        const updatedItems = invoiceItems.filter((item) => item !== itemToDelete);
        setInvoiceItems(updatedItems);
        updateApiDataWithNewItems(updatedItems); // Update apiData when deleting
    };

    const handleEditItem = (itemToEdit) => {
        setEditingItem(itemToEdit);
    };

    const updateApiDataWithNewItems = (newItems) => {
        const updatedApiData = props.apiData.map(data => {
            if (data.title === props.title) {
                return { ...data, items: newItems };
            }
            return data;
        });

        props.updateApiData(updatedApiData); // Update apiData in CostEstimator
    };

    const handleEditItems = (updateItems) => {
        setInvoiceItems(updateItems)
        updateApiDataWithNewItems(updateItems)
    }

    return (
        <div className="">


            <h4 className="my-4">{props.title}</h4>
            {props.editable && <InvoiceForm
                addInvoiceItem={addInvoiceItem}
                editingItem={editingItem}
                setEditingItem={setEditingItem}
                invoiceItems={invoiceItems}
                handleEditItems={handleEditItems}
                dropDownItems={props.dropDownItems}
                currency={props.currency}
                exchangeRate={props.exchangeRate}
            />}

            <InvoiceTable
                items={invoiceItems}
                onDelete={handleDeleteItem}
                onEdit={handleEditItem}
                editable={props.editable}
                currency={props.currency}
                exchangeRate={props.exchangeRate}
            />
        </div>
    );
}

InvoiceGenerator.defaultProps = {
    editable: true
};

export default InvoiceGenerator;
