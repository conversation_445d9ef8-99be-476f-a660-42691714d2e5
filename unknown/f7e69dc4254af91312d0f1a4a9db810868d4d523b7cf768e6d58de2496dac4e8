import React, { useState } from 'react'
import { Accordion, Tabs, Tab } from 'react-bootstrap';




function Hazards({ item, i, j, onClickHazardIn,full }) {
    const [value, setValue] = useState(null)
    console.log(full)


    const onClickHazard = (e, i, j, c, h, ca, ha) => {
        onClickHazardIn(e, i, j, c, h, ca, ha);
    }

    return (
        <div className='row mb-4' style={{ padding: '20px', boxShadow: 'rgba(0, 0, 0, 0.12) 0px 0px 10px 4px' }}>
            <div
                className="form-group mt-5 mb-3"
                style={{ textAlign: "center" }}
            >
                <label
                    htmlFor="user_name"
                    style={{ fontSize: 20 }}
                >
                    Hazards Identification
                </label>
            </div>
            <div className='row'>
                <div className='col-12'>
                    <div className='text-center'>
                        <h4>
                            Identify potential hazards associated with sub-activity :{full[0].value}
                        </h4>
                    </div>
                    <Tabs

                        id="fill-tab-example"
                        className="mb-3"
                        fill
                    >

                        {item.option[0].allhazards.map(
                            (cate, c) => {
                                return (
                                    <Tab eventKey={c} title={cate.name}>
                                        {cate.hazards ? (
                                            <div className="row">
                                                {cate.hazards.map(
                                                    (ha, h) => {
                                                        return (
                                                            <div
                                                                className="col-3"
                                                                onClick={(
                                                                    e
                                                                ) =>
                                                                    onClickHazard(
                                                                        e,
                                                                        i,
                                                                        j,
                                                                        c,
                                                                        h,
                                                                        cate,
                                                                        ha
                                                                    )
                                                                }
                                                            >
                                                                <div
                                                                    className="row m-2 align-items-center justify-content-center"
                                                                    style={
                                                                        ha.active ===
                                                                            true
                                                                            ? {
                                                                                boxShadow:
                                                                                    "0px 0px 12px 4px #d6d4d4",
                                                                                border:
                                                                                    "2px solid red",
                                                                                cursor:
                                                                                    "pointer",
                                                                            }
                                                                            : {
                                                                                boxShadow:
                                                                                    "0px 0px 12px 4px #d6d4d4",
                                                                                border:
                                                                                    "2px solid #fff",
                                                                                cursor:
                                                                                    "pointer",
                                                                            }
                                                                    }
                                                                >
                                                                    <div className="col-4 p-0">
                                                                        <img
                                                                            src={
                                                                                "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                ha.image
                                                                            }
                                                                            style={{
                                                                                height: 60,
                                                                            }}
                                                                            alt="sample"
                                                                        />
                                                                    </div>
                                                                    <div className="col-8">
                                                                        <p>
                                                                            {" "}
                                                                            {
                                                                                ha.name
                                                                            }{" "}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        );
                                                    }
                                                )}
                                            </div>
                                        ) : (
                                            ""
                                        )}
                                    </Tab>
                                )
                            })}


                    </Tabs>

                </div>

                <div
                    className="col-12"

                >
                    <div style={{
                        background: "#f5f5f5",
                        padding: 15,

                        overflowY: "auto",
                    }}>
                        <div className='text-center'>
                            <h4>
                            Hazards identified by the RA Team for this sub-activity
                            </h4>
                        </div>

                        {item.option[0].allhazards
                            .length >= 0 ? (
                            <div className="row">
                                {item.option[0].allhazards.map(
                                    (cate, c) => {
                                        return (
                                            <>
                                                {cate.hazards ? (
                                                    <>
                                                        {cate.hazards.map(
                                                            (ha, h) => {
                                                                return (
                                                                    <>
                                                                        {ha.active ===
                                                                            true ? (
                                                                            <div
                                                                                className="col-3"
                                                                                style={{
                                                                                    marginBottom: 16,
                                                                                }}
                                                                            >
                                                                                <div
                                                                                    className="d-flex"
                                                                                    style={{
                                                                                        boxShadow:
                                                                                            "0px 0px 12px 4px #d6d4d4",
                                                                                    }}
                                                                                >
                                                                                    <div
                                                                                        className=""
                                                                                        style={{
                                                                                            background:
                                                                                                "#fff",
                                                                                        }}
                                                                                    >
                                                                                        <img
                                                                                            src={
                                                                                                "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                                ha.image
                                                                                            }
                                                                                            style={{
                                                                                                height: 60,
                                                                                                width: 60,
                                                                                            }}
                                                                                            alt="sample"
                                                                                        />
                                                                                    </div>
                                                                                    <div
                                                                                        className="d-flex align-items-center"
                                                                                        style={{
                                                                                            position:
                                                                                                "relative",
                                                                                            width:
                                                                                                "100%",
                                                                                            paddingLeft: 10,
                                                                                            background:
                                                                                                "#fff",
                                                                                        }}
                                                                                    >
                                                                                        <p>
                                                                                            {" "}
                                                                                            {
                                                                                                ha.name
                                                                                            }{" "}
                                                                                        </p>
                                                                                        <span
                                                                                            style={{
                                                                                                position:
                                                                                                    "absolute",
                                                                                                right: 0,
                                                                                                top: "-7px",
                                                                                                fontStyle:
                                                                                                    "italic",
                                                                                                fontSize: 12,
                                                                                                color:
                                                                                                    "red",
                                                                                                cursor:
                                                                                                    "pointer",
                                                                                            }}
                                                                                        >
                                                                                            <i
                                                                                                className="mdi mdi-close"
                                                                                                onClick={(
                                                                                                    e
                                                                                                ) =>
                                                                                                    onClickHazard(
                                                                                                        e,
                                                                                                        i,
                                                                                                        j,
                                                                                                        c,
                                                                                                        h,
                                                                                                        cate,
                                                                                                        ha
                                                                                                    )
                                                                                                }
                                                                                            ></i>
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        ) : (
                                                                            ""
                                                                        )}
                                                                    </>
                                                                );
                                                            }
                                                        )}{" "}
                                                    </>
                                                ) : (
                                                    ""
                                                )}
                                            </>
                                        );
                                    }
                                )}
                            </div>
                        ) : (
                            ""
                        )}
                    </div>
                </div>


            </div>


            {/* <div className="form-group">
                <div className="row">
                    <div
                        className="col-6"
                        style={{
                            height: 500,
                            overflowY: "auto",
                        }}
                    >
                        <label htmlFor="user_name">
                            Select potentials hazards
                            applicable for this
                            sub-activity
                        </label>
                        {item.option[0].allhazards
                            .length >= 0 ? (
                            <Accordion>
                                {item.option[0].allhazards.map(
                                    (cate, c) => {
                                        return (
                                            <Accordion.Item
                                                eventKey={c}
                                            >
                                                <Accordion.Header
                                                    className="accord"
                                                    style={{
                                                        padding: 10,
                                                    }}
                                                >
                                                    {cate.name}
                                                </Accordion.Header>
                                                <Accordion.Body
                                                    style={{
                                                        paddingTop: 10,
                                                    }}
                                                >
                                                    {cate.hazards ? (
                                                        <div className="row">
                                                            {cate.hazards.map(
                                                                (ha, h) => {
                                                                    return (
                                                                        <div
                                                                            className="col-6"
                                                                            onClick={(
                                                                                e
                                                                            ) =>
                                                                                onClickHazard(
                                                                                    e,
                                                                                    i,
                                                                                    j,
                                                                                    c,
                                                                                    h,
                                                                                    cate,
                                                                                    ha
                                                                                )
                                                                            }
                                                                        >
                                                                            <div
                                                                                className="row m-2 align-items-center justify-content-center"
                                                                                style={
                                                                                    ha.active ===
                                                                                        true
                                                                                        ? {
                                                                                            boxShadow:
                                                                                                "0px 0px 12px 4px #d6d4d4",
                                                                                            border:
                                                                                                "2px solid red",
                                                                                            cursor:
                                                                                                "pointer",
                                                                                        }
                                                                                        : {
                                                                                            boxShadow:
                                                                                                "0px 0px 12px 4px #d6d4d4",
                                                                                            border:
                                                                                                "2px solid #fff",
                                                                                            cursor:
                                                                                                "pointer",
                                                                                        }
                                                                                }
                                                                            >
                                                                                <div className="col-4 p-0">
                                                                                    <img
                                                                                        src={
                                                                                            "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                            ha.image
                                                                                        }
                                                                                        style={{
                                                                                            height: 60,
                                                                                        }}
                                                                                        alt="sample"
                                                                                    />
                                                                                </div>
                                                                                <div className="col-8">
                                                                                    <p>
                                                                                        {" "}
                                                                                        {
                                                                                            ha.name
                                                                                        }{" "}
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    );
                                                                }
                                                            )}
                                                        </div>
                                                    ) : (
                                                        ""
                                                    )}
                                                </Accordion.Body>
                                            </Accordion.Item>
                                        );
                                    }
                                )}
                            </Accordion>
                        ) : (
                            ""
                        )}
                    </div>
                    <div
                        className="col-6"
                        style={{
                            background: "#f5f5f5",
                            padding: 15,
                            height: 500,
                            overflowY: "auto",
                        }}
                    >
                        <label htmlFor="user_name">
                            Identified Hazards
                        </label>
                        {item.option[0].allhazards
                            .length >= 0 ? (
                            <div className="row">
                                {item.option[0].allhazards.map(
                                    (cate, c) => {
                                        return (
                                            <>
                                                {cate.hazards ? (
                                                    <>
                                                        {cate.hazards.map(
                                                            (ha, h) => {
                                                                return (
                                                                    <>
                                                                        {ha.active ===
                                                                            true ? (
                                                                            <div
                                                                                className="col-6"
                                                                                style={{
                                                                                    marginBottom: 16,
                                                                                }}
                                                                            >
                                                                                <div
                                                                                    className="d-flex"
                                                                                    style={{
                                                                                        boxShadow:
                                                                                            "0px 0px 12px 4px #d6d4d4",
                                                                                    }}
                                                                                >
                                                                                    <div
                                                                                        className=""
                                                                                        style={{
                                                                                            background:
                                                                                                "#fff",
                                                                                        }}
                                                                                    >
                                                                                        <img
                                                                                            src={
                                                                                                "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                                ha.image
                                                                                            }
                                                                                            style={{
                                                                                                height: 60,
                                                                                                width: 60,
                                                                                            }}
                                                                                            alt="sample"
                                                                                        />
                                                                                    </div>
                                                                                    <div
                                                                                        className="d-flex align-items-center"
                                                                                        style={{
                                                                                            position:
                                                                                                "relative",
                                                                                            width:
                                                                                                "100%",
                                                                                            paddingLeft: 10,
                                                                                            background:
                                                                                                "#fff",
                                                                                        }}
                                                                                    >
                                                                                        <p>
                                                                                            {" "}
                                                                                            {
                                                                                                ha.name
                                                                                            }{" "}
                                                                                        </p>
                                                                                        <span
                                                                                            style={{
                                                                                                position:
                                                                                                    "absolute",
                                                                                                right: 0,
                                                                                                top: "-7px",
                                                                                                fontStyle:
                                                                                                    "italic",
                                                                                                fontSize: 12,
                                                                                                color:
                                                                                                    "red",
                                                                                                cursor:
                                                                                                    "pointer",
                                                                                            }}
                                                                                        >
                                                                                            <i
                                                                                                className="mdi mdi-close"
                                                                                                onClick={(
                                                                                                    e
                                                                                                ) =>
                                                                                                    onClickHazard(
                                                                                                        e,
                                                                                                        i,
                                                                                                        j,
                                                                                                        c,
                                                                                                        h,
                                                                                                        cate,
                                                                                                        ha
                                                                                                    )
                                                                                                }
                                                                                            ></i>
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        ) : (
                                                                            ""
                                                                        )}
                                                                    </>
                                                                );
                                                            }
                                                        )}{" "}
                                                    </>
                                                ) : (
                                                    ""
                                                )}
                                            </>
                                        );
                                    }
                                )}
                            </div>
                        ) : (
                            ""
                        )}
                    </div>

                </div>
            </div> */}
        </div>
    )
}


export default Hazards