// playground requires you to assign document definition to a variable called dd

var dd = {
  content: [
      {
      width: '100%',
      alignment: 'center',
      text: 'INCIDENT REPORT',
      bold: true,
      margin: [0, 0, 0, 10],
      fontSize: 15,
    },
    {
        
      columns: [
          
        {
          image: '',
          width: 100,
        },
        [
          {
            text: 'IR-23/11/23-72',
            color: 'green',
            width: '*',
            fontSize: 24,
            bold: true,
            alignment: 'right',
            margin: [0, 0, 0, 15],
          },
          {
            stack: [
              {
                columns: [
                  {
                    text: 'Type',
                    color: '#aaaaab',
                    bold: true,
                    width: '*',
                    fontSize: 12,
                    alignment: 'right',
                  },
                  {
                    text: 'Safety',
                    bold: true,
                    color: '#333333',
                    fontSize: 12,
                    alignment: 'right',
                    width: 100,
                  },
                ],
              },
              {
                columns: [
                  {
                    text: 'Circulated On',
                    color: '#aaaaab',
                    bold: true,
                    width: '*',
                    fontSize: 12,
                    alignment: 'right',
                  },
                  {
                    text: 'Nov 11, 2023',
                    bold: true,
                    color: '#333333',
                    fontSize: 12,
                    alignment: 'right',
                    width: 100,
                  },
                ],
              },
              {
                columns: [
                  {
                    text: 'Rating',
                    color: '#aaaaab',
                    bold: true,
                    fontSize: 12,
                    alignment: 'right',
                    width: '*',
                  },
                  {
                    text: 'C0H0',
                    bold: true,
                    fontSize: 14,
                    alignment: 'right',
                    color: 'green',
                    width: 100,
                  },
                ],
              },
            ],
          },
        ],
      ],
    },
    '\n',
    
    {
      width: '100%',
      alignment: 'center',
      text: 'QC14 Collided with Vessel starboard side gangway',
      bold: true,
      margin: [0, 10, 0, 10],
      fontSize: 15,
    },
    
    {
      width: '100%',
      alignment: 'justify',
      text: 'QC14 collided with vessel starboard side gangway while travelling to forward part of the vessel. The portable stanchion and Ganway railing got damege.',
      margin: [0, 0, 0, 5],
      fontSize: 12,
    },
   
    '\n',
 
    {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: ['*', '*', '*', '*'],
        body: [
          [
              {
              border: [false, true, false, true],
              text: 'Location',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Wharf > Berth 2',
              border: [false, true, false, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Date / TIme',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '22-11-2023 18:40',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            }
            
          ],
          [
            {
              text: 'Working Group',
              border: [false, false, false, true],
              alignment: 'left',
              fillColor: '#dadada',
              bold: true,
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Group 3',
              border: [false, false, false, true],
            
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Weather Condition',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Rainy',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            }
          ],
          [
            {
              text: 'Surface Type & Condition',
              border: [false, false, false, true],
              alignment: 'left',
              fillColor: '#dadada',
              bold: true,
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Slippery Surface > Wet',
              border: [false, false, false, true],
            
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Lighting',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Artificial- Good',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            }
          ],
          [
            {
              text: 'Work Activity',
              border: [false, false, false, true],
              alignment: 'left',
              fillColor: '#dadada',
              bold: true,
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Routing Office Work',
              border: [false, false, false, true],
            
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Department',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Engineering',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            }
          ]
         
        ],
      },
    },
    '\n\n',
    {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: ['*', '*', '*'],
        body: [
            
             [
            {
              text: 'Equipment Type',
              bold: true,
              fillColor: '#dadada',
              border: [false, true, false, true],
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
            {
              text: 'Equipment Number',
              border: [false, true, false, true],
                bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
            {
              text: 'Damage Type',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            }
          ],
          [
              {
              border: [true, true, true, true],
              text: 'RTG',
             
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                          {
              border: [true, true, true, true],
              text: '09',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },              {
              border: [true, true, true, true],
              text: 'Broken',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
           
           
            
          ],
          [
              {
              border: [true, true, true, true],
              text: 'ITT',
             
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                          {
              border: [true, true, true, true],
              text: '11',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },              {
              border: [true, true, true, true],
              text: 'Damaged',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
           
           
            
          ],
        
     
        
         
        ],
      },
    },
        '\n\n',
         {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: [100, '*'],
        body: [
            
      
         [
              {
              border: [false, true, true, true],
              text: 'Vessel Details',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Vessel and Voyage Details',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
           
          ],
           [
              {
              border: [false, true, true, true],
              text: 'Any Other Type of Damage',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Damage',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
           
          ],
           [
              {
              border: [false, true, true, true],
              text: 'Trucks Involved',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'ADP 122, ASP 345, ASD 123',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
           
          ],
           [
              {
              border: [false, true, true, true],
              text: 'how/Why the Incident Occur',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Accidentally occurred -- 001',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
           
          ],
           [
              {
              border: [false, true, true, true],
              text: 'Immediate Actions Taken',
              bold: true,
              alignment: 'left',
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: 'Actions Taken',
              border: [false, true, true, true],
              alignment: 'left',
              margin: [0, 5, 0, 5],
            },
           
          ]
        
     
        
         
        ],
      },
    },
     '\n\n',
    {
      text: 'Person(s) Involved',
      style: 'notesTitle',
    },
      {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: ['*', '*', '*', '*'],
        body: [
            
             [
           
            {
              text: 'Employee Details',
              border: [false, true, false, true],
                bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
            
         {
              text: 'Injuries',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
             {
              text: 'Injury Details',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
             {
              text: 'PPE',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            }
          ],
          
             [
              {
              border: [true, true, true, true],
              text: 'P.D.P. Nishantha (Controller D. W) - 000061',
             
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                          {
              border: [true, true, true, true],
              text: 'Neck, Stomach, Left Leg',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },              {
              border: [true, true, true, true],
              text: 'Abrasion Wounds, Bleeding from Ear/Nose/Throat',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                  {
              border: [true, true, true, true],
              text: 'Face Sheild, Face Mask',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
           
           
            
          ]
         
        
        
     
        
         
        ],
      },
    },
      
         {
      text: 'Personnel Injured',
      style: 'notesTitle',
    },
      {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: ['*', '*', '*', '*'],
        body: [
            
             [
           
            {
              text: 'Employee Details',
              border: [false, true, false, true],
                bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
            
         {
              text: 'Injuries',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
             {
              text: 'Injury Details',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
             {
              text: 'PPE',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            }
          ],
          
             [
              {
              border: [true, true, true, true],
              text: 'T.H.K Hassim (Controller D. W) - 000044',
             
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                          {
              border: [true, true, true, true],
              text: 'Right Arm, Stomach, Left Foot',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },              {
              border: [true, true, true, true],
              text: 'Back ache, Punctured Wounds',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                  {
              border: [true, true, true, true],
              text: 'Overall / High Visible Jacket, Goggles',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
           
           
            
          ],
          [
              {
              border: [true, true, true, true],
              text: 'R.B.P.S Illangasinghe (Controller D. W) - 000034',
             
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                          {
              border: [true, true, true, true],
              text: 'Left Leg, Right Arm, Right Hand',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },              {
              border: [true, true, true, true],
              text: 'Laceration Wounds, Punctured Wounds',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                  {
              border: [true, true, true, true],
              text: 'Safety Shoes, Overall/high visible jacket',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
           
           
            
          ]
         
        
        
     
        
         
        ],
      },
    },
        {
      text: 'Witness Involved',
      style: 'notesTitle',
    },
      {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: ['*', '*'],
        body: [
            
             [
           
            {
              text: 'Employee Details',
              border: [false, true, false, true],
                bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
            
         {
              text: 'Comments',
              border: [false, true, false, true],
             bold: true,
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
              textTransform: 'uppercase',
            },
             
          ],
          
             [
              {
              border: [true, true, true, true],
              text: 'N.G.S Kumara (Cont Yard) - 000051',
             
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                          {
              border: [true, true, true, true],
              text: '-',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },              
             
           
           
            
          ],
          [
              {
              border: [true, true, true, true],
              text: 'D.K.M. Risvy (Manager - Operations) - 000087',
             
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            },
                          {
              border: [true, true, true, true],
              text: 'No comments',
            
              alignment: 'left',
          
              margin: [0, 5, 0, 5],
            }
           
            
          ]
         
        
        
     
        
         
        ],
      },
    },
        {
      text: 'Evidence Photos',
      style: 'notesTitle',
    },
    {
  table: {
    widths: ['25%', '25%', '25%', '25%'],
    body: [
      [
          {
          image: '',       
       width: 125,
          },
          {
          image: '',
          width: 125,
          },
          {
          image: '',
          width: 125,
          },
          {
          image: '',
          width: 125,
          }
      ],
    ],
  },
  layout: 'noBorders', // This will remove the borders from the table
},

 {text: '', pageBreak: 'after'},
  {
      text: 'Other Details',
      style: 'notesTitle',
    },
    {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: ['15%', '35%', '15%', '35%'],
        body: [
          [
              {
              border: [false, true, false, true],
              text: 'Reporter',
              bold: true,
             
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '<EMAIL>',
              border: [false, true, false, true],
            
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Reviewer',
              bold: true,
              
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '<EMAIL>',
              border: [false, true, true, true],
             
              margin: [0, 5, 0, 5],
            }
            
          ],
        
       
         
         
        ],
      },
    },
    {
      text: 'Notification Information',
      style: 'notesTitle',
    },
      {
      layout: {
        defaultBorder: false,
        hLineWidth: function(i, node) {
          return 1;
        },
        vLineWidth: function(i, node) {
          return 1;
        },
        hLineColor: function(i, node) {
          return '#eaeaea';
        },
        vLineColor: function(i, node) {
          return '#eaeaea';
        },
        hLineStyle: function(i, node) {
          // if (i === 0 || i === node.table.body.length) {
          return null;
          //}
        },
        // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
        paddingLeft: function(i, node) {
          return 10;
        },
        paddingRight: function(i, node) {
          return 10;
        },
        paddingTop: function(i, node) {
          return 3;
        },
        paddingBottom: function(i, node) {
          return 3;
        },
        fillColor: function(rowIndex, node, columnIndex) {
          return '#fff';
        },
      },
      table: {
       
        widths: ['25%', '25%', '25%', '25%'],
        body: [
          [
              {
              border: [false, true, false, true],
              text: 'Medical Officer',
              bold: true,
             
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '21/11/2023 10:16',
              border: [false, true, false, true],
            
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Duty Engineer Manager',
              bold: true,
              
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '21/11/2023 10:16',
              border: [false, true, true, true],
             
              margin: [0, 5, 0, 5],
            }
            
          ],
          
           [
              {
              border: [false, true, false, true],
              text: 'Custodian',
              bold: true,
             
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '21/11/2023 13:33',
              border: [false, true, false, true],
            
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Insurance',
              bold: true,
              
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '21/11/2023 13:33',
              border: [false, true, true, true],
             
              margin: [0, 5, 0, 5],
            }
            
          ],
          
           [
              {
              border: [false, true, false, true],
              text: 'Investigation Team',
              bold: true,
             
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '21/11/2023 13:33',
              border: [false, true, false, true],
            
              margin: [0, 5, 0, 5],
            },
             {
              border: [false, true, false, true],
              text: 'Third Party',
              bold: true,
              
              fillColor: '#dadada',
              margin: [0, 5, 0, 5],
            },
            {
              text: '21/11/2023 13:33',
              border: [false, true, true, true],
             
              margin: [0, 5, 0, 5],
            }
            
          ],
        
       
         
         
        ],
      },
    },
  ],
  styles: {
    notesTitle: {
      fontSize: 12,
      bold: true,
      margin: [0, 20, 0, 3],
    },
    notesText: {
      fontSize: 10,
    },
  },
  defaultStyle: {
    columnGap: 20,
    //font: 'Quicksand',
  },
};