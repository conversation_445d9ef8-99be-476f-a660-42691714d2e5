import React from 'react'
import { Form } from 'react-bootstrap';
function SubActivityTitle({ item, onchangeText, i, j }) {
    console.log(i,j)


    // const onActivityImage11=(e, i, j) =>{
    //     onActivityImage1(e,i,j)
    // }
    return (
        
            <div className="form-group required">
                <label htmlFor="user_name">
                    {item.label}
                </label>
                <Form.Control
                    type="text"
                    onChange={(e) =>
                        onchangeText(e, j, i)
                    }
                    value={item.value}
                />
            </div>
           
        
    )
}

export default SubActivityTitle