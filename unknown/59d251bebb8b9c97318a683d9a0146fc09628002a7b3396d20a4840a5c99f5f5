import React, { Component } from "react";
// import YouTube from 'react-youtube';
// import ReactPlayer from 'react-player/youtube'
let fullURL = "";
class Youtube extends Component {
  constructor(props) {
    super(props);
    this.state = {
      toolType: "YOUTUBE",
      title: "",
      isEmpty: false

    };


  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue(stateFor, youtube_url) {
    switch (stateFor) {
      case "TITLE":
        var videoid = youtube_url.match(
          /(?:https?:\/{2})?(?:w{3}\.)?youtu(?:be)?\.(?:com|be)(?:\/watch\?v=|\/)([^\s&]+)/);
        var video_id = youtube_url.split('v=');
        console.log(video_id);
        videoid = video_id[1];
        var ampersandPosition = videoid.indexOf('&');
        if (ampersandPosition != -1) {
          videoid = videoid.substring(0, ampersandPosition);
        }

        if (videoid != null) {
          console.log(videoid);
          var link = "https://www.googleapis.com/youtube/v3/videos?id=" + videoid +
            "&key=AIzaSyA3UijkqMixMnqH4GlvLVmfdYV5Xy9Z_gM&part=snippet,contentDetails,status";
          console.log(link);
          var t_flag = true;
          fetch(link)
            .then(response => response.json())
            .then(data => {
              const t_flag = data.items[0].status.embeddable;

              console.log(t_flag);
              if (!t_flag) {
                console.log(1);

                alert("This YouTube Video's publisher doesn't allow embedding");
              } else {
                console.log(2);
                this.setState({ title: 'https://www.youtube.com/embed/' + videoid })

              }
            });



          //return videoid[1];
          //'<iframe width="auto" height="auto" src="https://www.youtube.com/embed/'+video_id+'" frameborder="0" allowfullscreen=""></iframe>'
        } else {

          // alert("The youtube url is not valid.");
          return false;
        }


        break;
      case "CAPTION":
        this.setState({ caption: youtube_url });
        break;

      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 1000);
  }
  createChecklistHandler() {
    this.setState({ mdShow: false });
  }
  render() {
    const opts = {
      height: '390',
      width: '640',
      playerVars: {
        // https://developers.google.com/youtube/player_parameters
        autoplay: 1,
      },
    };
    return (
      <>
        <div className="paragraph mb-3" style={this.state.title === '' && this.props.field.isEmpty ? { boxShadow: '0px 0px 12px 3px #dfdfdf', border: '1px solid red', borderRadius: 0 } : { boxShadow: '0px 0px 12px 3px #dfdfdf', borderRadius: 0 }}>
          <div className="card">
            <div className="card-header d-flex justify-content-between">
              <div><i className="fa fa-youtube-play  mr-1"></i> Youtube</div>


              <div className="">
                {this.props.index !== 0 ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveUp(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-up"></i>
                  </span>
                  : ""}
                {this.props.index !== this.props.length ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveDown(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-down"></i>
                  </span>
                  : ''}
                <span
                  className=""
                  onClick={() => this.props.removeField(this.props.index)}
                >
                  <i className="mdi mdi-close"></i>
                </span>
              </div>
            </div>

            <div className="card-body">
              <div className="form-group">
                <label className="label" htmlFor="title">
                 YouTube Url
                </label>
                <input
                  id="title"
                  defaultValue={this.state.title}
                  onChange={(e) => this.changeValue("TITLE", e.target.value)}
                  placeholder="Paste YouTube URL here. Click outside the box to Save."
                  className="form-control"
                  type="text"
                />
              </div>
              {this.state.title === '' ? '' :

                <iframe className='video'
                  title='Youtube player'
                  style={{width:'100%'}}
                  sandbox='allow-same-origin allow-forms allow-popups allow-scripts allow-presentation'
                  height={350}
                  src={this.state.title}
                  allowFullScreen
                  >
                </iframe>

              }



            </div>
          </div>
        </div>

      </>
    );
  }
}

export default Youtube;
