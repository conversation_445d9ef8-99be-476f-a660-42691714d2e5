import React, { useState, useEffect } from 'react';
import { InputNumber } from 'primereact/inputnumber';
function InvoiceForm({ addInvoiceItem, editingItem, setEditingItem, invoiceItems, handleEditItems, dropDownItems, currency }) {

    const [description, setDescription] = useState('');
    const [quantity, setQuantity] = useState(0);
    const [price, setPrice] = useState(0);
    const [remarks, setRemarks] = useState('');

    useEffect(() => {
        if (editingItem) {
            // setItem(editingItem.item);
            setDescription(editingItem.description);
            setQuantity(editingItem.quantity);
            setPrice(editingItem.price);
            setRemarks(editingItem.remarks);
        }
    }, [editingItem]);

    const handleAddItem = () => {
        const newItem = {

            description,
            quantity,
            price,
            remarks,
        };

        if (editingItem) {
            const updatedItems = invoiceItems.map((item) => (item === editingItem ? newItem : item));
            handleEditItems(updatedItems);
            setEditingItem(null);
        } else {
            addInvoiceItem(newItem);
        }

        // Clear input fields

        setDescription('');
        setQuantity(0);
        setPrice(0);
        setRemarks('');
    };

    return (
        <div className="d-flex align-items-center flex-wrap">
            {/* Description */}
            {dropDownItems && dropDownItems.length > 0 ? (
                <div className="flex-grow-1 me-2">
                    <label htmlFor="description" className="form-label">Description</label>
                    <select
                        id="description"
                        className="form-select"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                    >
                        <option value="">Select Description</option>
                        {dropDownItems.map((item, index) => (
                            <option key={index} value={item}>{item}</option>
                        ))}
                    </select>
                </div>
            ) : (
                <div className="flex-grow-1 me-2">
                    <label htmlFor="description" className="form-label">Description</label>
                    <input
                        type="text"
                        id="description"
                        className="form-control"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder="Enter description"
                    />
                </div>
            )}
            {/* Quantity */}
            <div className=" " style={{ width: '100px' }}>
                <label htmlFor="quantity" className="form-label">Quantity</label>
                <input type="number" id="quantity" className="form-control" value={quantity} min={0} onChange={(e) => setQuantity(e.target.value)} placeholder="0" />
            </div>
            {/* Price */}
            <div className="" style={{ width: '150px' }}>
                <label htmlFor="price" className="form-label">Unit Cost ({currency})</label>
                <InputNumber
                    id="price"
                    value={price}
                    onValueChange={(e) => setPrice(e.value)}
                    mode="decimal"
                    min={0}
                    className='w-100'
                    placeholder="0.00"
                    locale="en-US"
                    minFractionDigits={2}
                    maxFractionDigits={2}
                />
                {/* <input type="number" id="price" className="form-control" value={price} min={0} onChange={(e) => setPrice(e.target.value)} placeholder="0" /> */}
            </div>
            {/* Remarks */}
            <div className="flex-grow-1 ms-2">
                <label htmlFor="remarks" className="form-label">Remarks</label>
                <input type="text" id="remarks" className="form-control" value={remarks} onChange={(e) => setRemarks(e.target.value)} placeholder="Enter remarks" />
            </div>
            {/* Button */}
            <button className="btn btn-success ms-2" onClick={handleAddItem}>{editingItem ? 'Save' : '+'}</button>
        </div>

    );
}

export default InvoiceForm;
