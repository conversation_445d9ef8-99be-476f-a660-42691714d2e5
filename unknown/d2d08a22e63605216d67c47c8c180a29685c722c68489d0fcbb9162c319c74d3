import React from 'react';
import ActivityForm from './ActivityForm';

const TransportForm = () => {

    const activities = ['Forward', 'Turn', 'Lane change', 'Overtake', 'Junction', 'Revers', 'Stopping', 'Parked', 'Other'];
    const vehicles = [
        "Internal PM",
        "TTL",
        "RTG",
        "QC",
        "Forklift",
        "Reach stacker",
        "External Truck",
        "Cab",
        "SLPA cabs/Lorry/bike & other"
    ];

    return (<>
        <div>
            <ActivityForm title={'Activity'} activities={activities} vehicles={vehicles} />
            <ActivityForm title={'Cause'} activities={activities} vehicles={[
                "Over speed",
                "Didn't see / expect",
                "Other party's fault",
                "Mechanical",
                "Blind spot",
                "Road / Traffic condition",
                "Sleepy",
                "Drowsy / Drunk",
                "Natural Reasons",
                "Other"
            ]} />
            <ActivityForm title={'Impact'} activities={activities} vehicles={[
                "Collided on to rear",
                "Reverse and Collided",
                "Struck from behind",
                "Struck on to side",
                "Head-On",
                "Side-swiped",
                "Skidded",
                "Destroyed (Nature)",
                "Other"
            ]} />





            <div className='container form-group mt-5'>
                <label>Yard Volume</label>
                <input type='text' className='form-control' placeholder='Yard Volume' />
            </div>

            <div className='container form-group mt-5'>
                <label>PM head
                    <input type='checkbox' className='ms-2'/></label>
            </div>

            <div className='container form-group'>
                <label>PM Trailer
                    <input type='checkbox' className='ms-2'/></label>
            </div>

        </div>
    </>)
}

export default TransportForm;