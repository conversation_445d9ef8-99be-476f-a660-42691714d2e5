// @ts-nocheck
import React, { useState, useRef } from 'react';
import DataTables from '../tables/DataTables';
import { DOCUMENTS_URL, DOWNLOAD_DOCS_URL, DOCUMENTS_WITH_ID_URL, FILE_URL } from '../constants';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;

const customSwal = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-danger',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',

  },
  buttonsStyling: false
})

$(document).on('click', '.delete-document', function () {

  const id = $(this).data('id')
  customSwal.fire({
    title: 'Are you sure?',
    text: "You won't be able to revert this!",
    icon: 'warning',
    showCancelButton: true,
    reverseButtons: true,

    confirmButtonText: 'Delete'
  }).then((result) => {
    if (result.isConfirmed) {
      deleteDocument(id);

    }
  })


})

$(document).on('blur', '.edit-inline', function () {

  var column = $(this).data('column');
  var id = $(this).data('id');
  var data = $(this).text();

  editDocument(id, column, data)

})

const editDocument = async (id, column, data) => {
  const response = await fetch(DOCUMENTS_WITH_ID_URL(id), {

    // Adding method type 
    method: "PATCH",

    // Adding body or contents to send 
    body: JSON.stringify({
      [column]: data,

    }),

    // Adding headers to the request 
    headers: {
      "Content-type": "application/json; charset=UTF-8"
    }
  })
  if (response.ok) {
    //show success
    cogoToast.info('Updated', { position: 'top-right' })
  }
}

const deleteDocument = async (id) => {

  const response = await fetch(
    DOCUMENTS_WITH_ID_URL(id), {
    method: 'DELETE',
    headers: {
      "Content-type": "application/json; charset=UTF-8"
    }
  })

  if (response.ok) {
    customSwal2.fire(
      'Deleted!',
      '',
      'success'
    )
    $('#dataTable').DataTable().ajax.reload();
  }


}
const Documents = () => {

  const [mdShow, setMdShow] = useState(false)
  const [selectedFile, setSelectedFile] = useState();
  const [isLoading, setIsLoading] = useState(false);

  const dName = useRef();
  const dCategory = useRef();
  const dDesc = useRef();
  const dRef = useRef();
  const dVersion = useRef();

  const thead = [
    'Category',
    'Document Name',
    'Reference',
    'Version No',
    'Description',
    'Files',
    'Actions'
  ];

  const fileChangeHandler = (event) => {
    setSelectedFile(event.target.files[0]);
  }

  const createDocumentHandler = async () => {
    // @ts-ignore
    setIsLoading(true)
    const formData = new FormData();
    formData.append('file', selectedFile)

    const fileResponse = await fetch(FILE_URL, { method: 'POST', body: formData })

    if (fileResponse.ok) {

      const response = await fetch(
        DOCUMENTS_URL,
        {
          method: 'POST',
          body: JSON.stringify({
            name: dName.current.value,
            category: dCategory.current.value,
            description: dDesc.current.value,
            uniqueid: dRef.current.value,
            version: dVersion.current.value,
            files: selectedFile.name
          }),
          headers: { "Content-type": "application/json; charset=UTF-8",  "Authorization": `Bearer ${localStorage.getItem('access_token')}`}
        })

      if (response.ok) {
        $('#dataTable').DataTable().ajax.reload();
        customSwal2.fire(
          'Document Created!',
          '',
          'success'
        )
      } else {
        //show error
        customSwal2.fire(
          'Please Try Again!',
          '',
          'error'
        )
        setIsLoading(false)
      }

    } else {
      //Show Error
      customSwal2.fire(
        'Please Try Again!',
        '',
        'error'
      )
      setIsLoading(false)
    }

    dName.current.value = '';
    dCategory.current.value = '';
    dDesc.current.value = '';
    dRef.current.value = '';
    dVersion.current.value = '';
    setMdShow(false)
  }

  const options = {
    "ajax": {
      url: DOCUMENTS_URL,
      dataSrc: '',
      "beforeSend": function (xhr) {
        const token = localStorage.getItem('access_token');
        xhr.setRequestHeader('Authorization', 'Bearer ' + token);
      }
    },
    order: [[1, 'asc']],
    columnDefs: [
      { type: 'natural', targets: [0, 1, 2, 3] }
    ],
    "columns": [{
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="category" data-id="${data.id}" class="edit-inline" contenteditable="true"> ${data.category ? data.category : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="name" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.name ? data.name : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="uniqueid" data-id="${data.id}" class="edit-inline" contenteditable="true"> ${data.uniqueid ? data.uniqueid : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="version" data-id="${data.id}" class="edit-inline" contenteditable="true"> ${data.version ? data.version : ''} </div>`;

      }
    },
    {
      "data": null,
      "render": function (data, type, full, meta) {
        return `<div data-column="description" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.description ? data.description : ''} </div>`;

      }
    },

    {
      "data": null,

      "render": function (data, type, full, meta) {
        return `<a target="_blank" href="${DOWNLOAD_DOCS_URL(data.files)}"> ${data.files.length > 35 ? data.files.substr(0, 35) + '…' : data.files} </a>`;

      }
    },

    {
      "data": null,
      "render": function (data, type, full, meta) {

        return `<i style="color: red;font-size: 22px;" class="mdi mdi-delete delete-document" data-id="${data.id}" ></i>`;
      }
    },
    ]
  }

  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">

                <h4 className="card-title">Documents Library</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-note-plus-outline mr-2" /> Upload New Document</button>
                      <DataTables thead={thead} options={options} />

                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        show={mdShow}
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >

        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="document_name" >Document Name</label>
              <Form.Control type="text" ref={dName} id="document_name" placeholder="Enter Document Name" />
            </div>

            <div className="form-group">
              <label htmlFor="document_category" >Document Category</label>
              <Form.Control type="text" ref={dCategory} id="document_category" placeholder="Enter Document Category" />
            </div>

            <div className="form-group">
              <label htmlFor="document_description" >Description</label>
              <Form.Control type="text" ref={dDesc} id="document_description" placeholder="Enter Document Description" />
            </div>

            <div className="form-group">
              <label htmlFor="document_ref" >Reference</label>
              <Form.Control type="text" ref={dRef} id="document_ref" placeholder="Enter Document Reference" />
            </div>

            <div className="form-group">
              <label htmlFor="document_version" >Version No</label>
              <Form.Control type="text" ref={dVersion} id="document_version" placeholder="Enter Version No" />
            </div>

            <div className="form-group">

              <Form.Control type="file" id="document_file" onChange={fileChangeHandler} placeholder="Choose File" />
            </div>

          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">
          {
            isLoading ? <Loader /> : (
              <>
                <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>
                <Button variant="primary" onClick={createDocumentHandler}>Create</Button>
              </>
            )
          }

        </Modal.Footer>
      </Modal>
    </>
  )
}


export default Documents;
