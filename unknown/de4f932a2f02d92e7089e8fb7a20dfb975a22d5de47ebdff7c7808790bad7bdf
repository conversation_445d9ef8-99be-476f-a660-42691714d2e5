import React, { useState } from 'react';
import ToolBox from './components/ToolBox';
import FormContainer from './components/FormContainer';
import { STEP_WITH_ID_URL } from '../constants';
import API from '../services/API';
import { singlePopup } from './../notifications/Swal';
import { useLocation,useHistory } from 'react-router-dom/cjs/react-router-dom.min';
const Curation = (props) => {

    console.log(props)
    const location = useLocation();
    const history =useHistory();
    const [isLoading, setIsLoading] = useState(true);
    const [checklistData, setChecklistData] = useState({ name: '', value: {} });


    const myForm = async (form,type) => {
        console.log(form)

        const data = JSON.stringify(form)

        const response = await API.patch(STEP_WITH_ID_URL(location.state), {
                value: data
           
        })
        if (response.status ===204) {
            singlePopup.fire(
                'Saved!',
                '',
                'success'
            );
            if(type ==='exit'){
               history.goBack()
            }
        } else {
            //show error
            singlePopup.fire(
                'Please Try Again',
                '',
                'error'
            )
        }

    }
    const updateForm = async (callback) => {

        const response = await API.get(STEP_WITH_ID_URL(location.state));
        if (response.status === 200) {
            let form = JSON.parse(response.data.value);
            console.log(form)
            callback(form);
            setChecklistData(response.data)
            setIsLoading(false)
        }

    }
    const goBack =()=>{
        props.history.goBack();
     }
    return (
        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className="card-body p-0 " >
                        <div className="row">
                            <div className="col-md-9 pr-0">
                                <FormContainer
                                    loader={false}
                                    debug={false}
                                    updateOnMount={true}
                                    updateForm={updateForm}
                                    onSave={myForm}
                                    goBack ={goBack}
                                />
                            </div>
                            <div
                                className="col-md-3 pl-0">
                                <ToolBox />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );





}

export default Curation;