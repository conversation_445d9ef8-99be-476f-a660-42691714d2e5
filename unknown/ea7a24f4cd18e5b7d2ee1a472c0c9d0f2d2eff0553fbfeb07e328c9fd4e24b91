import React from 'react';
import moment from 'moment';

export const MedicalHRCard = ({ title, data }) => {

    const personnelImpactedList = (persons) => {
        const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => person && !person.internal)


        return (
            <>


                {(filteredPersons.length > 0 || externalPersons.length > 0) && <table className="table">
                    <thead className="table table-striped table-header-sm">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Name</th>
                            <th scope="col">Employee ID</th>
                            <th scope="col">Designation</th>
                            <th scope="col">Leave Date</th>
                            <th scope="col">Back to Work Date</th>
                            <th scope="col">Medical Officer Comments</th>
                            <th scope="col">Loss of Working Days</th>
                            <th scope="col">VMO Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredPersons.map((person, index) => {


                            return (
                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.selectedEmp.name}</td>
                                    <td>{person.selectedEmp.uniqueId}</td>
                                    <td>{person.selectedEmp.designation}</td>
                                    <td>{moment(person.medicalLeaveFromDate).format('DD/MM/YYYY')} - {moment(person.medicalLeaveToDate).format('DD/MM/YYYY')}</td>
                                    <td>{moment(person.backToWorkDate).format('DD/MM/YYYY')}</td>
                                    <td>{person.medicalOfficerSurveillance}</td>
                                    <td>{person.estimatedLossWorkingDays}</td>
                                    <td>{person.vmoComments}</td>

                                </tr>
                            )
                        })}

                        {externalPersons.map((person, index) => {



                            return (


                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.name}</td>
                                    <td>{person.empId}</td>
                                    <td>{person.designation}</td>
                                    <td>{moment(person.medicalLeaveFromDate).format('DD/MM/YYYY')} - {moment(person.medicalLeaveToDate).format('DD/MM/YYYY')}</td>
                                    <td>{moment(person.backToWorkDate).format('DD/MM/YYYY')}</td>
                                    <td>{person.medicalOfficerSurveillance}</td>
                                    <td>{person.vmoComments}</td>


                                </tr>
                            )
                        })}


                    </tbody>
                </table>}

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Personnel Injured</p>
                }

            </>
        );
    };

    return (
        <>
            <div className='row mb-3'>
                <div className='col-12'>
                    <label> {title} </label>
                    <div>{personnelImpactedList(data)}</div>
                </div>
            </div>
        </>
    )
}

export default MedicalHRCard;