import React, { useState, useEffect } from "react";
import { Nav, Tab, OverlayTrigger, Tooltip, Popover } from "react-bootstrap";
import MaterialTable from "material-table";
import { RISKASSESSMENT_LIST, RISK_WITH_ID_URL } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import CardOverlay from '../pages/CardOverlay';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';
import * as Icon from 'feather-icons-react';
import Box from '@mui/material/Box';
import RiskAssessment from "./RiskAssessment";
import Routine from "./Routine";
import ToolBoxTalk from "./ToolBoxTalk";
import NonRoutine from "./NonRoutine";
import HazardBased from "./HazardBased";
import Select from 'react-select'
import Actions from "./Actions";
import AppSwitch from "../pages/AppSwitch";
import HazardDash from "./HazardDash";
import Topical from "./Topical";
function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box >
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};
const Dashboard = (props) => {
  console.log(props)
  const user = useSelector((state) => state.login.user)
  console.log(user)
  const history = useHistory();
  const location = useLocation()
  const [data, setData] = useState([])
  const [risk, setRisk] = useState([])
  const [overdue, setOverdue] = useState([])
  const [additional, setAdditional] = useState([])
  const [aOverdue, setAOverdue] = useState([])
  const [access, setAccess] = useState(false)

  const [option, setOption] = useState([{ label: 'Observation', value: 'obs' }, { label: 'ePermit Work', value: 'eptw' }, { label: 'Document', value: 'doc' }, { label: 'Risk', value: 'ra' }, { label: 'Incident', value: 'ir' }])


  useEffect(() => {
    // getPermit();

  }, [])
  const customSwal = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-danger',
      cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
  })
  const customSwal2 = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
  })

  const getPermit = async () => {
    const uriString = { include: ["user"] };

    const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {
      //   const utc = moment()
      //   console.log(moment.utc().format('DD-MM-YYYY'))
      const over = response.data.filter(item =>
        moment().utc(item.nextdate, 'DD-MM-YYYY').isBefore(moment().utc().format())
      )

      setOverdue(over)
      const add = response.data.filter(item =>

        item.additional.includes('No')
      )
      console.log(add)
      setAdditional(add)

      const addControl = add.filter(item => {
        let check = []
        if (item.additionalDates) {

          item.additionalDates.map((item) => {

            item.map((item2) => {

              if (item2.date !== '') {

                const dateString = item2.date;
                const givenDate = moment(dateString);
                const today = moment();
                if (givenDate.isBefore(today)) {
                  console.log('The given date is before today.');
                  console.log(givenDate + 'before')
                  console.log(today + 'today')
                  check.push(true)
                } else if (givenDate.isAfter(today)) {
                  console.log('The given date is after today.');
                  console.log(givenDate + 'after')
                  console.log(today + 'today')
                  check.push(false)
                } else {
                  console.log('The given date is today.');
                }

              }

            })

          }

          )
        }
        return check.includes(true)

      })

      setAOverdue(addControl)
      setRisk(response.data)

    }

    if (user.length !== 0) {
      setAccess(user.roles.some(item => item.name === 'RA Team Leader'))
    }

  }
  const defaultMaterialTheme = createTheme();
  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };
  const viewRisk = (data) => {
    let id = data.id
    if (data.type.label === 'Hazard-Based') {
      history.push('/risk-assessment/viewhazard', { id })
    } else {
      history.push('/risk-assessment/viewrisk', { id })
    }
  }
  const editRisk = (data) => {
    let id = data.id
    if (data.type.label === 'Hazard-Based') {
      history.push('/risk-assessment/amendhazard', { id })
    } else {
      history.push('/risk-assessment/amendrisk', { id })
    }
  }

  const onDelete = async (id) => {

    customSwal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        const response = await API.delete(RISK_WITH_ID_URL(id));
        if (response.status === 204) {

          customSwal2.fire(
            'Deleted!',
            '',
            'success'
          )


        }
        getPermit();
      }
    })

  }

  const tableActions = [

    {
      icon: 'visibility',
      tooltip: 'View RA',
      onClick: (event, rowData) => {

        viewRisk(rowData)
        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      }
    },
    {
      icon: "modeEdit",
      tooltip: "Edit RA",
      onClick: (event, rowData) => {
        if (access) {
          editRisk(rowData)
        } else {
          customSwal2.fire(
            'You Cant Access',
            '',
            'success'
          )
        }
        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      },
    },
    {
      icon: "delete",
      tooltip: "Delete RA",
      onClick: (event, rowData) => {
        if (access) {
          onDelete(rowData.id)
        } else {
          customSwal2.fire(
            'You Cant Access',
            '',
            'success'
          )
        }

        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      },
    },
  ]
  const columns = [

    {
      field: 'meetid',
      title: 'RA No',


    },
    {
      field: 'activity',
      title: 'Process/Activity',
      render: (row) => {
        if (row.type.label === 'Hazard-Based') {
          return 'N/A';
        } else {
          if (row.activity === 'Others') {
            return row.activity + '-' + row.otherActivity
          } else {
            return row.activity
          }


        }
      }


    },
    {
      field: 'type.label',
      title: 'Type',

      // formatter: (cell, row) => {

      //   return (
      //     <>{row.contractor.name}</>
      //   );
      // }

    },
    {
      field: 'department',
      title: 'Initiated by',



    },


    {
      field: 'date',
      title: 'Published / Amended Date/Time',


    },
    {
      field: 'nextdate',
      title: 'Next Review Date',


    },
    {
      field: 'status',
      title: 'Status',


    },

    {
      field: 'user.firstName',
      title: 'RA Leader',


    },


  ];
  const [value, setValue] = useState(localStorage.getItem('ra_url') ? localStorage.getItem('ra_url') : 'MY ACTIONS');

  const TABS = {
    ACTIONS: "MY ACTIONS",
    DASHBOARD: "DASHBOARD",
    ROUTINE: "ROUTINE",
    NONROUTINE: "NONROUTINE",
    HAZARD: "HAZARD",
    TOPICAL: "TOPICAL",
    TOOLBOX: "TOOLBOX",
    // UNDER_INVESTIGATION: "UNDER_INVESTIGATION",
    // ACTIONS: "ACTIONS"

  };
  const handleChange = (event, newValue) => {

    setValue(newValue);
    localStorage.setItem('ra_url', newValue)
  };

  return (
    <>
      <AppSwitch value={{ label: 'Integrated Risk Management', value: 'ra' }} />

      <Tabs value={value} onChange={handleChange} aria-label="incident report table" className="risk">
        <MTab label="My Actions" value={TABS.ACTIONS} />

        <MTab label="Risk Register" value={TABS.DASHBOARD} />

        <MTab label={"Routine Risk Assessment"} value={TABS.ROUTINE} />
        <MTab label={"Non Routine Risk Assessment"} value={TABS.NONROUTINE} />
        <MTab label={"High-Risk Hazard Control Measure"} value={TABS.HAZARD} />

        <MTab label={"Topical Tool Box Talks"} value={TABS.TOPICAL} />

        <MTab label={"Toolbox Talk Records"} value={TABS.TOOLBOX} />


      </Tabs>

      <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
        <Actions />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.DASHBOARD}>
        <RiskAssessment />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.ROUTINE}>
        <Routine />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.NONROUTINE}>
        <NonRoutine />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.HAZARD}>
        <HazardDash />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.TOPICAL}>
        <Topical />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.TOOLBOX}>
        <ToolBoxTalk />
      </CustomTabPanel>


    </>
  );
};

export default Dashboard;
