import React, { useState, useMemo, useEffect } from 'react'

import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';

import { useSelector } from "react-redux";

import CardOverlay from './CardOverlay';
import IncidentTable from './IncidentTable'
import ClassifiedIncidentTable from './ClassifiedIncidentTable'
import AllIncident from './AllIncident';
import Action from './Action';
import AppSwitch from './AppSwitch';
import IncidentMasterData from './IncidentMasterData';
import IncidentRequestData from './IncidentRequestData';


function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box >
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `incident-tab-${index}`,
    'aria-controls': `incident-tabpanel-${index}`,
  };
}


const Incident = () => {

  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

  };



  const me = useSelector((state) => state.login.user)

  const isAirGroup = useMemo(() => {
    return me?.roles?.some(item => item.name === 'AIR Group') || false;
  }, [me]);

  const isAirInvestigator = useMemo(() => {
    return me?.roles?.some(item => item.name === 'AIR Investigator') || false;
  }, [me]);


  const isAirSupervisor = useMemo(() => {
    return me?.roles?.some(item => item.name === 'IR Supervisor') || false;
  }, [me]);


  const [value, setValue] = useState(0);


  const handleChange = (event, newValue) => {
    setValue(newValue);
  };




  const TABS = {
    ALL: "ALL",
    REVIEW: "REVIEW",
    INVESTIGATION: "INVESTIGATION",
    UNDER_INVESTIGATION: "UNDER_INVESTIGATION",
    ACTIONS: "ACTIONS",
    UNDER_REVIEW: "UNDER_REVIEW",
    MASTER_DATA: "MASTER_DATA",
    CLASSIFIED: "CLASSIFIED",
    REQUEST: "REQUEST",

  };



  useEffect(() => {
    setValue(TABS.ALL)

  }, [])

  return (
    <>




      <AppSwitch value={{ label: 'Incident', value: 'ra' }} />



      {/* <h4 className=''>Incident Reports</h4> */}

      <Tabs value={value} onChange={handleChange} aria-label="incident report table">
        <Tab label="My Actions" value={TABS.ACTIONS} />
        <Tab label="Classified Groups" value={TABS.CLASSIFIED} />
        {isAirGroup && <Tab label="All Incidents" value={TABS.ALL} />}

        {isAirInvestigator && <Tab label="To be Investigated by You" value={TABS.INVESTIGATION} />}
        <Tab label="Under Review" value={TABS.REVIEW} />
        <Tab label="Reviewed by You" value={TABS.UNDER_REVIEW} />
        {isAirSupervisor && <Tab label="Withdraw Request" value={TABS.REQUEST} />}
        {isAirSupervisor && <Tab label="IR Master Data" value={TABS.MASTER_DATA} />}





      </Tabs>



      {isAirGroup && <CustomTabPanel value={value} tabValue={TABS.ALL}>
        <AllIncident />
      </CustomTabPanel>}

      <CustomTabPanel value={value} tabValue={TABS.REVIEW}>
        <IncidentTable stage={'review'} />
      </CustomTabPanel>

      {isAirInvestigator && <CustomTabPanel value={value} tabValue={TABS.INVESTIGATION}>
        <IncidentTable stage={'trigger_investigation'} />
      </CustomTabPanel>}



      <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>

        <Action application={"AIR"} id={false} />
        {/* <CustomersDemo/> */}
      </CustomTabPanel>

      <CustomTabPanel value={value} tabValue={TABS.UNDER_REVIEW}>

        <IncidentTable mode={'reviewer'} stage={'classified_documents'} />
      </CustomTabPanel>

      <CustomTabPanel value={value} tabValue={TABS.CLASSIFIED}>
        <ClassifiedIncidentTable mode={'classified'} stage={'classified_documents'} />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.REQUEST}>

        <IncidentRequestData />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.MASTER_DATA}>

        <IncidentMasterData />
      </CustomTabPanel>





    </>
  )

}

export default Incident;