// @ts-nocheck
import React, { useState, useRef, useEffect } from "react";
import { useHistory } from "react-router-dom";
import {
    CHECKLIST_LIST_URL,
    CHECKLIST_WITH_ID_URL,
    TOOLBOX_WITH_ID_URL,
    TOOLBOXTALK_LIST,
    DYNAMIC_TITLES_URL,
    API_URL
} from "../constants";
import { Modal, Button, Form } from "react-bootstrap";
import cogoToast from "cogo-toast";
import Loader from "../shared/Loader";
import Swal from "sweetalert2";
import { useSelector } from "react-redux";
import { singlePopup, deletePopup } from "../notifications/Swal";
// @ts-ignore
import { Link } from "react-router-dom";
import MaterialTable from "material-table";
import API from "../services/API";
import { ThemeProvider, createTheme } from "@mui/material";

const defaultMaterialTheme = createTheme();
const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
};

const customSwal = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-danger",
        cancelButton: "btn btn-light",
    },
    buttonsStyling: false,
});

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});

const customSwal3 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
        cancelButton: "btn btn-light",
    },
    buttonsStyling: false,
});

const ToolBoxTalk = (props) => {

    const [mdShow, setMdShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [data, setData] = useState([]);
    const [title, setTitle] = useState([])
    const [smModal, setSmModal] = useState(false)
    const [dynamicTitle, setDynamicTitle] = useState([])
    const [titleThree, setTitleThree] = useState('')
    const [titleFour, setTitleFour] = useState('')
    const [dataTool, setDataTool] = useState('')
    const history = useHistory();

    const cName = useRef();
    const cDesc = useRef();
    const cRef = useRef();
    const cVersion = useRef();
    const eName = useRef();
    const eDesc = useRef();
    useEffect(() => {
        getChecklist();
        getDynamicTitle();
    }, []);
    const getChecklist = async () => {
        const uriString = {
            "include": [
                { "relation": "locationOne" },
                { "relation": "locationTwo" },
                { "relation": "locationThree" },
                { "relation": "locationFour" },
                { "relation": "locationFive" },
                { "relation": "locationSix" },
                { "relation": "riskAssessment" },
                {
                    "relation": "user",
                    "scope": {
                        "include": [{ "relation": "workingGroup" }, { "relation": "ghsOne" }, { "relation": "designation" }]
                    }
                }
            ]
        }
        const url = `${TOOLBOXTALK_LIST}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const response = await API.get(url);
        if (response.status === 200) {
            setData(response.data);
        }
    };
    const getDynamicTitle = async () => {

        const response = await API.get(DYNAMIC_TITLES_URL);
        if (response.status === 200) {
            let locationThree = response.data.filter(item => item.title === 'LocationThree')
            let locationFour = response.data.filter(item => item.title === 'LocationFour')
            setTitleThree(locationThree[0].altTitle)
            setTitleFour(locationFour[0].altTitle)
            console.log(locationFour, locationThree)
            setDynamicTitle(response.data)
        }
    };
    const deleteStep = (item) => {
        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await fetch(TOOLBOX_WITH_ID_URL(item.id), {
                    method: 'DELETE',
                    headers: {
                        "Content-type": "application/json; charset=UTF-8"
                    }
                })
                if (response.ok) {
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                    getChecklist();
                }
            }
        })
    }
    const openCurate = (item) => {
        console.log(item)
        setDataTool(item)
        setMdShow(true)




    }
    const handleTitle = async () => {
        const response = await API.patch(TOOLBOXTALK_LIST(title.id), {
            name: eName.current.value,
            description: eDesc.current.value
        });
        if (response.status === 204) {
            singlePopup.fire("Updated!", "", "success");

            setSmModal(false)
        }
        getChecklist();
    }

    const tableActions = [

        {
            icon: 'visibility',
            tooltip: 'View Records',
            onClick: (event, rowData) => {

                openCurate(rowData)

            }
        },
        // {
        //     icon: "modeEdit",
        //     tooltip: "Edit Risk",
        //     onClick: (event, rowData) => {
        //         setSmModal(true);
        //         setTitle(rowData);
        //     },
        // },
        {
            icon: "delete",
            tooltip: "Delete Records",
            onClick: (event, rowData) => {
                deleteStep(rowData)

            },
        },
    ]
    const columns1 = [
        {
            dataField: "name",
            text: "Name",
            sort: true,
        },
        {
            dataField: "description",
            text: "Description",
            sort: true,
        },

        {
            dataField: null,
            text: "View",
            sort: false,
            formatter: (cell, row) => {
                console.log(row);
                console.log(cell);
                var item = row;
                return (
                    <div>
                        <div className=" option-btn" >
                            <i
                                className="mdi mdi-delete"
                                onClick={() => deleteStep(item)}
                            ></i>
                            {/* <i
                                className="mdi mdi-border-color"
                                onClick={() => {
                                    setSmModal(true);
                                    setTitle(item);
                                }}
                            ></i>
                            <i
                                className="mdi mdi-forward"
                                onClick={() => openCurate(item)}
                            ></i> */}
                        </div>
                    </div>
                );
            },
        },
    ];
    const columns = [
        {
            field: "createdAt",
            title: "Date",
            sort: true,
        },
        {
            field: "locationThree.name",
            title: titleThree,
            sort: true,
        },
        {
            field: "locationFour.name",
            title: titleFour,
            sort: true,
        },
        {
            field: "riskAssessment.activity",
            title: "WorkActivity",
            sort: true,
        },
        {
            field: "user.firstName",
            title: "Conducted by",
            sort: true,
        },
        {
            field: "user.workingGroup.name ",
            title: "Shift",
            sort: true,
            render: (row) => {
                if (row.user) {
                    if (row.user.workingGroup) {
                        return (
                            <p>{row.user.workingGroup.name}</p>
                        )
                    }
                } else {
                    return ('')
                }
            }
        },
        {
            field: "user.ghsOne.name ",
            title: "Department",
            sort: true,
            render: (row) => {
                if (row.user) {
                    if (row.user.ghsOne) {
                        return (
                            <p>{row.user.ghsOne.name}</p>
                        )
                    }
                } else {
                    return ('')
                }
            }
        },
        {
            field: null,
            title: "Contractor",
            sort: true,
        },
        {
            field: "participants",
            title: "# of participants",
            sort: true,
        },
        {
            field: "image",
            title: "Image",
            sort: true,
            render: (row) => {
                return (
                    <a href={`${API_URL}/docs/` + row.image} target="_blank">View</a>
                )
            }
        },
        {
            field: "startTime",
            title: "Start Time",
            sort: true,
        },
        {
            field: "endTime",
            title: "End Time",
            sort: true,
        },
        {
            field: "name",
            title: "Additional Feedback",
            sort: true,
        },






    ];
    const createChecklistHandler = async () => {
        // @ts-ignore
        setIsLoading(true);

        const response = await fetch(CHECKLIST_LIST_URL, {
            method: "POST",
            body: JSON.stringify({
                name: cName.current.value,
                description: cDesc.current.value,
                uniqueid: "",
                version: "",
            }),
            headers: { "Content-type": "application/json; charset=UTF-8" },
        });

        if (response.ok) {
            customSwal2.fire("Checklist Created!", "", "success");
        } else {
            //show error
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
        getChecklist();
        setIsLoading(false);
        cName.current.value = "";
        cDesc.current.value = "";

        setMdShow(false);
    };

    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">
                                <h4 className="card-title">Toolbox Talk Records</h4>
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            {/* <button
                        type="button"
                        className="btn btn-primary btn-rounded mb-3 "
                        onClick={(e) => {
                          e.preventDefault();
                          setMdShow(true);
                        }}
                      >
                         Add
                        Checklist
                      </button> */}
                                            <ThemeProvider theme={defaultMaterialTheme}>
                                                <MaterialTable
                                                    columns={columns}
                                                    data={data}
                                                    title=""
                                                    style={tableStyle}
                                                    actions={tableActions}
                                                    options={{
                                                        actionsColumnIndex: -1,
                                                    }}
                                                />
                                            </ThemeProvider>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Modal
                show={mdShow}
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    <h4>Commenced on {dataTool.startTime}</h4>
                </Modal.Header>
                <Modal.Body>
                    {dataTool !== '' ?
                        <>
                            <div className="mb-3">
                                <h5>WorkActivity : {dataTool.riskAssessment.activity}</h5>
                                <h5>Department : {dataTool.riskAssessment.department}</h5>
                                <h5>Area: {dataTool.locationThree.name}</h5>
                                <h5>Zone : {dataTool.locationFour.name}</h5>
                                <h5>Participant : {dataTool.participants}</h5>
                            </div>
                            <div className="mb-3">
                                <h5>Upload an Image of the ToolBox Talk</h5>
                                <img src={`${API_URL}/docs/` + dataTool.image} width={'230px'} />
                            </div >
                            <div className="mb-2">
                                {/* <h4 className="">Sub Activity</h4>
                                <hr /> */}
                                {dataTool.checklist.map(item => {
                                    return (
                                        <>
                                            <h3>Sub Activity :{item.sub}</h3>
                                            <h6 className="boxShadow p-2">Hazards</h6>

                                            {item.data[0].value.map((h, j) => {
                                                return (<>
                                                    <h4>{h.name}</h4>
                                                    <img width={'50px'} src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${h.image}`} />
                                                    {h.options.map((ite) => {
                                                        if (ite.checked === 1) {
                                                            return (<>


                                                                <p>{ite.label}</p></>)
                                                        }

                                                    })}

                                                </>
                                                )
                                            })}
                                            <h6 className="boxShadow p-2">Current Control</h6>

                                            {item.data[1].option.map((h, j) => {
                                                return (<>
                                                    <h4>{h.current_type} - {h.value}</h4>
                                                    {h.img ?
                                                        <img width={'150px'} src={`https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/current_control/${h.img}`} />
                                                        : ''}
                                                    {h.options.map((ite) => {
                                                        if (ite.checked === 1) {
                                                            return (<p>{ite.label}</p>)
                                                        }

                                                    })}

                                                </>
                                                )
                                            })}
                                            {item.data[2].identified.map((h, j) => {
                                                if (h.checked === 1 && h.label === 'Yes') {
                                                    return (<>
                                                        <h4 className="boxShadow p-2">Identified Control</h4>
                                                        <p>{item.data[2].identifiedControls}</p>

                                                        <h4>Have You Briefed the Team about these Additional Control</h4>
                                                        {item.data[2].options.map((item) => {
                                                            if (item.checked === 1) {
                                                                return (<>{item.label}</>)
                                                            }
                                                        })}
                                                    </>)
                                                }

                                            })}




                                        </>
                                    )
                                })}

                            </div>
                            <div className="mb-3">
                                <h4 className="boxShadow p-2">Acknowledgement Users</h4>
                                <hr />
                                {dataTool.sign.map((item, i) => {
                                    return (
                                        <>
                                            <h6>{i + 1} - {item.name}</h6>
                                            <img src={`${API_URL}/docs/` + item.url} width={'100px'} />
                                        </>
                                    )
                                })}
                            </div>
                            {dataTool.closeOut[0].checkpoints.map((item, i) => {

                                if (item.label === 'Yes' && item.checked === 1) {


                                    dataTool.closeOut[0].unplanned.map((test, i) => {
                                        return (
                                            <>
                                                <p>Job :{test.job}</p>
                                                <p>Job :{test.remarks}</p>
                                            </>
                                        )
                                    })



                                }

                            })
                            }


                            <hr />
                        </>
                        : ''}

                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={() => setMdShow(false)}>
                            Cancel
                        </Button>

                    </>

                </Modal.Footer>
            </Modal >

            <Modal
                size="md"
                show={smModal}
                onHide={() => setSmModal(false)}
                aria-labelledby="example-modal-sizes-title-lg"
            >
                <Modal.Header closeButton>
                    <Modal.Title>Edit Step Name</Modal.Title>
                </Modal.Header>

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="checklist_name"> Name</label>
                            <Form.Control
                                type="text"
                                ref={eName}
                                id="checklist_name"
                                placeholder="Enter Form Name"
                                defaultValue={title.name}
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="checklist_description">Description</label>
                            <Form.Control
                                type="text"
                                ref={eDesc}
                                id="checklist_description"
                                placeholder="Enter Form Description"
                                defaultValue={title.description}
                            />
                        </div>
                    </form>
                </Modal.Body>

                <Modal.Footer>
                    <Button variant="light" onClick={() => setSmModal(false)}>
                        Cancel
                    </Button>
                    <Button variant="primary" onClick={handleTitle}>
                        Update
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default ToolBoxTalk;
