import React from 'react'
import Select from "react-select";

function RA({item,i,j,handleSelectChange1}) {
    const handleSelectChange = (e, j, i, k, name, variant)=>{
        handleSelectChange1(e, j, i, k, name, variant)
    }
    return (
        <div className="row">
            <div
                className="col-12"
                style={{
                    padding: 20,
                    boxShadow:
                        "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                }}
            >
                <div className="row mt-4 mb-4">
                    <div className="col-12">
                        <div
                            className="form-group mt-2 mb-1"
                            style={{
                                textAlign: "center",
                            }}
                        >
                            <p
                                htmlFor="user_name"
                                style={{ fontSize: 20 }}
                            >
                                RISK ASSESSMENT OF THIS SUB-ACTIVITY
                            </p>
                            <span
                                style={{ fontSize: 12 }}
                            >
                                (Risk level based on the
                                presence of the above
                                identified controls)
                            </span>
                        </div>
                    </div>
                </div>
                <div className="row">
                    <div className="col-4">
                        <div
                            className="form-group mt-2 mb-1"
                            style={{
                                textAlign: "center",
                            }}
                        >
                            <label
                                className="d-flex justify-content-between"
                                htmlFor="user_name"
                                style={{ fontSize: 20 }}
                            >
                                Severity

                                <div className="likelyhood_hover severity" >
                                    <i className="mdi mdi-information-outline"></i>
                                    <img s src={require('../../../assets/images/severity.png')} alt="tet" />
                                </div>
                            </label>
                            <p style={{ lineHeight: '18px', height: 40 }}>   Degree of harm or
                                impact that could
                                result from a
                                hazardous event or
                                situation.</p>
                        </div>

                        <div
                            className="row"
                            style={{
                                display: "flex",
                                justifyContent:
                                    "space-between",
                            }}
                        >
                            {item.severity.map(
                                (opt, k) => {
                                    if (
                                        opt.type === "select1"
                                    ) {
                                        return (
                                            <div className="col-12">
                                                <div className="form-group">
                                                    <Select
                                                        labelKey="label"
                                                        id="user_description"
                                                        onChange={(e) =>
                                                            handleSelectChange(
                                                                e,
                                                                j,
                                                                i,
                                                                k,
                                                                opt.label,
                                                                "severity"
                                                            )
                                                        }
                                                        options={
                                                            opt.value
                                                        }
                                                        placeholder="Choose ..."
                                                    />
                                                </div>
                                            </div>
                                        );
                                    }
                                }
                            )}
                        </div>
                    </div>
                    <div className="col-4">
                        <div
                            className="form-group mt-2 mb-1"
                            style={{
                                textAlign: "center",
                            }}
                        >
                            <label
                                className="d-flex justify-content-between"
                                htmlFor="user_name"
                                style={{ fontSize: 20 }}
                            >
                                Likelihood
                                <div className="likelyhood_hover" >
                                    <i className="mdi mdi-information-outline"></i>
                                    <img s src={require('../../../assets/images/likelyhood.png')} alt="tet" />
                                </div>


                            </label>
                            <p style={{ lineHeight: '18px', height: 40 }}> Frequency with which a
                                hazardous event or
                                situation could
                                happen.</p>
                        </div>

                        <div
                            className="row"
                            style={{
                                display: "flex",
                                justifyContent:
                                    "space-between",
                            }}
                        >
                            {item.likelyhood.map(
                                (opt, k) => {
                                    if (
                                        opt.type === "select1"
                                    ) {
                                        return (
                                            <div className="col-12">
                                                <div className="form-group">
                                                    <Select
                                                        labelKey="label"
                                                        id="user_description"
                                                        onChange={(e) =>
                                                            handleSelectChange(
                                                                e,
                                                                j,
                                                                i,
                                                                k,
                                                                opt.label,
                                                                "likelyhood"
                                                            )
                                                        }
                                                        options={
                                                            opt.value
                                                        }
                                                        placeholder="Choose ..."
                                                    />
                                                </div>
                                            </div>
                                        );
                                    }
                                }
                            )}
                        </div>
                    </div>
                    <div className="col-4 ">
                        <div
                            className="form-group "
                            style={{
                                textAlign: "center",
                                marginBottom: 6,
                            }}
                        >
                            <label
                                className="d-flex justify-content-between"
                                htmlFor="user_name"
                                style={{ fontSize: 20 }}
                            >
                                Risk Level
                                <div className="likelyhood_hover " >
                                    <i className="mdi mdi-information-outline"></i>
                                    <img s src={require('../../../assets/images/color.jpeg')} alt="tet" />
                                </div>
                            </label>
                            <p style={{ lineHeight: '18px', visibility: 'hidden', height: 40 }}>   Degree of harm or
                                impact that could
                                result from a
                                hazardous event or
                                situation.</p>
                        </div>
                        <div
                            className="row"
                            style={{
                                display: "flex",
                                justifyContent:
                                    "space-between",
                            }}
                        >
                            {item.risk.map((opt, k) => {
                                return (
                                    <div className="col-12">
                                        <label
                                            className={`${opt.color === ""
                                                ? ""
                                                : "black"
                                                }`}
                                            style={{
                                                padding: 10,
                                                background:
                                                    opt.color,
                                                textAlign: "center",
                                                boxShadow:
                                                    "0px 0px 13px 3px #e7e4e4",

                                                borderRadius: 3,
                                                display: "flex",
                                                height: 38,
                                                justifyContent:
                                                    "center",
                                                marginTop: 8
                                            }}
                                        >
                                            {opt.label} - {opt.color === '#8cc14b' ? 'Low' : opt.color === '#ffef00' ? 'Medium' : opt.color === '#ff1900' ? 'High' : ''}
                                        </label>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default RA