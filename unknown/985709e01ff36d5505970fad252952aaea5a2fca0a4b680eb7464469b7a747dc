// @ts-nocheck
import React, { useState, useRef, useEffect } from "react";
import { useHistory } from "react-router-dom";
import {
  CHECKLIST_LIST_URL,
  CHECKLIST_WITH_ID_URL
} from "../constants";
import { Modal, Button, Form } from "react-bootstrap";
import cogoToast from "cogo-toast";
import Loader from "../shared/Loader";
import Swal from "sweetalert2";
import { useSelector } from "react-redux";
import { singlePopup,deletePopup } from "../notifications/Swal";
// @ts-ignore
import { Link } from "react-router-dom";
import MaterialTable from "material-table";
import API from "../services/API";
import { ThemeProvider, createTheme } from "@mui/material";

const defaultMaterialTheme = createTheme({
  typography: {
    allVariants: {
      fontFamily: 'serif',
    
    },
  },
});
const tableStyle = {
  borderRadius: '0',
  boxShadow: 'none',
};

const customSwal = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-danger",
    cancelButton: "btn btn-light",
  },
  buttonsStyling: false,
});

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-primary",
  },
  buttonsStyling: false,
});

const customSwal3 = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-primary",
    cancelButton: "btn btn-light",
  },
  buttonsStyling: false,
});

const Checklist = (props) => {
 
  const [mdShow, setMdShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [title,setTitle] =useState([])
  const [smModal,setSmModal] =useState(false)
  const history = useHistory();

  const cName = useRef();
  const cDesc = useRef();
  const cRef = useRef();
  const cVersion = useRef();
  const eName = useRef();
  const eDesc = useRef();
  useEffect(() => {
    getChecklist();
  }, []);
  const getChecklist = async () => {
    const response = await API.get(CHECKLIST_LIST_URL);
    if (response.status === 200) {
      setData(response.data);
    }
  };
  const deleteStep=(item)=>{
    deletePopup.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
  }).then(async (result) => {
      if (result.isConfirmed) {
          //   deleteChecklist(id);

          const response = await fetch(CHECKLIST_WITH_ID_URL(item.id), {
              method: 'DELETE',
              headers: {
                  "Content-type": "application/json; charset=UTF-8"
              }
          })
          if(response.ok){
            singlePopup.fire(
              'Deleted!',
              '',
              'success'
          );
          getChecklist();
          }
        }})
  }
  const  openCurate =(item)=>{
   
      history.push('/checklist/curate/edit',{id:item.id,type:'checklist'})
    
  }
  const handleTitle =async()=>{
    const response = await API.patch(CHECKLIST_WITH_ID_URL(title.id), {
      name: eName.current.value,
      description:eDesc.current.value
    });
    if (response.status === 204) {
      singlePopup.fire("Updated!", "", "success");
    
      setSmModal(false)
    }
    getChecklist();
  }

  const tableActions = [

    {
      icon: 'forward',
      tooltip: 'View Risk',
      onClick: (event, rowData) => {

        openCurate(rowData)
       
      }
    },
    {
      icon: "modeEdit",
      tooltip: "Edit Risk",
      onClick: (event, rowData) => {
        setSmModal(true);
                  setTitle(rowData);
      },
    },
    {
      icon: "delete",
      tooltip: "Delete Risk",
      onClick: (event, rowData) => {
        deleteStep(rowData)

      },
    },
  ]
  const columns1 = [
    {
      dataField: "name",
      text: "Name",
      sort: true,
    },
    {
      dataField: "description",
      text: "Description",
      sort: true,
    },

    {
      dataField: null,
      text: "View",
      sort: false,
      formatter: (cell, row) => {
        console.log(row);
        console.log(cell);
        var item = row;
        return (
          <div>
            <div className=" option-btn" >
              <i
                className="mdi mdi-delete"
                onClick={() => deleteStep(item)}
              ></i>
              <i
                className="mdi mdi-border-color"
                onClick={() => {
                 setSmModal(true);
                  setTitle(item);
                }}
              ></i>
              <i
                className="mdi mdi-forward"
                onClick={() => openCurate(item)}
              ></i>
            </div>
          </div>
        );
      },
    },
  ];
 const columns = [
    {
      field: "name",
      title: "Name",
      sort: true,
    },
   
    {
      field: "created_at",
      title: "Created Date",
      sort: true,
    },


  ];
  const createChecklistHandler = async () => {
    // @ts-ignore
    setIsLoading(true);

    const response = await fetch(CHECKLIST_LIST_URL, {
      method: "POST",
      body: JSON.stringify({
        name: cName.current.value,
        description: cDesc.current.value,
        uniqueid: "",
        version: "",
      }),
      headers: { "Content-type": "application/json; charset=UTF-8" },
    });

    if (response.ok) {
      customSwal2.fire("Checklist Created!", "", "success");
    } else {
      //show error
      customSwal2.fire("Please Try Again!", "", "error");
      setIsLoading(false);
    }
    getChecklist();
    setIsLoading(false);
    cName.current.value = "";
    cDesc.current.value = "";
  
    setMdShow(false);
  };

  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">
                <h4 className="card-title">Checklists</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      <button
                        type="button"
                        className="btn btn-primary btn-rounded mb-3 "
                        onClick={(e) => {
                          e.preventDefault();
                          setMdShow(true);
                        }}
                      >
                         Add
                        Checklist
                      </button>
                      <ThemeProvider theme={defaultMaterialTheme}>
                        <MaterialTable
                          columns={columns}
                          data={data}
                          title=""
                          style={tableStyle}
                          actions={tableActions}
                          options={{
                            actionsColumnIndex: -1,
                          }}
                        />
                      </ThemeProvider>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        show={mdShow}
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="checklist_name"> Name</label>
              <Form.Control
                type="text"
                ref={cName}
                id="checklist_name"
                placeholder="Enter Form Name"
              />
            </div>

            <div className="form-group">
              <label htmlFor="checklist_description">Description</label>
              <Form.Control
                type="text"
                ref={cDesc}
                id="checklist_description"
                placeholder="Enter Form Description"
              />
            </div>
          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">
          {isLoading ? (
            <Loader />
          ) : (
            <>
              <Button variant="light" onClick={() => setMdShow(false)}>
                Cancel
              </Button>
              <Button variant="primary" onClick={createChecklistHandler}>
                Create
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>

      <Modal
        size="md"
        show={smModal}
        onHide={() => setSmModal(false)}
        aria-labelledby="example-modal-sizes-title-lg"
      >
        <Modal.Header closeButton>
          <Modal.Title>Edit Step Name</Modal.Title>
        </Modal.Header>

        <Modal.Body>
        <form className="forms">
            <div className="form-group">
              <label htmlFor="checklist_name"> Name</label>
              <Form.Control
                type="text"
                ref={eName}
                id="checklist_name"
                placeholder="Enter Form Name"
                defaultValue={title.name}
              />
            </div>

            <div className="form-group">
              <label htmlFor="checklist_description">Description</label>
              <Form.Control
                type="text"
                ref={eDesc}
                id="checklist_description"
                placeholder="Enter Form Description"
                defaultValue={title.description}
              />
            </div>
          </form>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="light" onClick={() => setSmModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleTitle}>
            Update
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default Checklist;
