import React, { useState } from "react";
import { useEffect } from "react";
import CheckboxTree from "react-checkbox-tree";
import "react-checkbox-tree/lib/react-checkbox-tree.css";
import API from "../services/API";
import { useSelector } from "react-redux";
import {
  ASSIGNED_AREA_BY_id,
  ASSIGNED_DOCUMENT_URL,
  ENTERPRISE_TIER1_URL,
  ENTERPRISE_USER_URL,
  ENTERPRISE_CHECKLIST_URL,
  ASSIGNED_DOCUMENT_BY_ID,
  GROUP_URL,
  GROUP_ASSIGNED_DOCUMENT_URL,
  GROUP_ASSIGNED_DOCUMENT_BY_ID,
  DOCUMENT_URL,
  USERS_URL
} from "../constants";
import Select from "react-select";
import { Form, Tab, Nav, Row, Col } from "react-bootstrap";
import cogoToast from "cogo-toast";
import { Link } from "react-router-dom/cjs/react-router-dom";
import Swal from "sweetalert2";
function Assignment() {
  const [checked, setChecked] = useState([]);
  const [expanded, setExpanded] = useState([]);
  const [notes, setNotes] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [filteredNode, setFilteredNode] = useState([]);
  const [user, setUser] = useState([]);
  const [group, setGroup] = useState([]);
  const [userId, setUserId] = useState("");
  const [type, setType] = useState("");

 
  const onCheckTree = async (checked) => {
    setChecked(checked);
  };
  const customSwal2 = Swal.mixin({
    customClass: {
      confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
  });

  useEffect(() => {
   
      getTierList();
      getUserList();
      getGroupList();
    
  }, []);
  const getGroupList = async () => {
    const response = await API.get(GROUP_URL);
    if (response.status === 200) {
      let user = [];
      response.data.map((item) => {
        user.push({ label: item.name, value: item.id, name: "group" });
      });
      setGroup(user);
    }
  };
  const getUserList = async () => {
    const response = await API.get(USERS_URL);
    if (response.status === 200) {
    //   const data = response.data.filter((item) => item.roles.includes("user"));
      let user = [];
      response.data.map((item) => {
        user.push({ label: item.firstName, value: item.id, name: "user" });
      });
      setUser(user);
    }
  };
  const getTierList = async () => {
    const response = await API.get(DOCUMENT_URL);
    if (response.status === 200) {
      const arr = response.data.map((item) => {
        return { value: item.id, label: item.name };
      });
      setNotes(arr);
      setFilteredNode(arr);
    }
  };
  const onFilterChange = (e) => {
    setFilterText(e.target.value);
    filterTree();
  };
  const filterTree = () => {
    if (!filterText) {
      setFilteredNode(notes);

      return;
    }
    setFilteredNode(notes.reduce(filterNodes, []));
    // this.setState({
    //     filteredNodes: nodes.reduce(this.filterNodes, []),
    // });
  };
  const filterNodes = (filtered, node) => {
    const children = (node.children || []).reduce(filterNodes, []);

    if (
      // Node's label matches the search string
      node.label.toLocaleLowerCase().indexOf(filterText.toLocaleLowerCase()) >
        -1 ||
      // Or a children has a matching node
      children.length
    ) {
      filtered.push({ ...node, children });
    }

    return filtered;
  };
  const onSelectUser = async (e) => {
    setUserId(e.value);
    setType(e.name);
    let arr = [];
    if (e.name === "user") {
      const response = await API.get(ASSIGNED_DOCUMENT_BY_ID(e.value));
      if (response.status === 200) {
        response.data.map((item) => {
          arr.push(item.documentsId);
        });
        setChecked(arr);
      }
    } else if (e.name === "group") {
      const response = await API.get(GROUP_ASSIGNED_DOCUMENT_BY_ID(e.value));
      if (response.status === 200) {
        response.data.map((item) => {
          arr.push(item.documentsId);
        });
        setChecked(arr);
      }
    }
  };
  const onSaveKa = async () => {
    if (type === "group") {
      const response = await API.post(GROUP_ASSIGNED_DOCUMENT_URL, {
        documentId: checked,
        groupid: userId,
      });
      if (response.status === 204) {
        customSwal2.fire("Document Allocated!", "", "success");
      }
    } else if (type === "user") {
      const response = await API.post(ASSIGNED_DOCUMENT_URL, {
        documentId: checked,
       
        userid: userId,
      });
      if (response.status === 204) {
        customSwal2.fire("Document Allocated!", "", "success");
      }
    }
  };
  const Option = [
    // {
    //   label: "Groups",
    //   options: group,
    // },
    {
      label: "Users",
      options: user,
    },
  ];
  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body ">
              <h4 className="card-title">Document Assignment</h4>
                  <Row>
                   

                    <Col xs={12}>
                      <div className="row">
                        <div className="col-6">
                          <Select
                            labelKey="label"
                            id="user_description"
                            options={Option}
                            placeholder="Select User"
                            onChange={(e) => onSelectUser(e)}
                          />
                        </div>
                        <div className="row">
                          <div className="col-6 mt-4">
                            <div className="form-group">
                              <Form.Control
                                type="text"
                                value={filterText}
                                id="document_name"
                                placeholder="Search..."
                                onChange={(e) => onFilterChange(e)}
                              />
                            </div>

                            <div
                              className="col-12"
                              style={{
                                border: "2px solid hsl(0,0%,80%)",
                                padding: "10px 20px",
                                height: 400,
                                overflow: "auto",
                              }}
                            >
                              <CheckboxTree
                                nodes={filteredNode}
                                checked={checked}
                                expanded={expanded}
                                onCheck={(checked) => onCheckTree(checked)}
                                onExpand={(expanded) => setExpanded(expanded)}
                              />
                            </div>
                            <button
                              type="button"
                              className="btn btn-primary mt-4 mb-3 "
                              onClick={(e) => {
                                e.preventDefault();
                                onSaveKa();
                              }}
                            >
                              Assign Document
                            </button>
                          </div>
                        </div>
                      </div>
                    </Col>
                  </Row>
               
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Assignment;
