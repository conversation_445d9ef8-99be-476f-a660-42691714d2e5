import React, { useEffect, useState } from "react";
import Select from "react-select";
import { Row, Col, Tab, Nav, Modal, Button, Table } from "react-bootstrap";
import {
    USERS_URL,
    TIER1_URL,
    KNOWLEDGE_SESSION_USER,
    CHECKLIST_LIST_URL,
    KNOWLEDGE_SESSION_KU,
    CHECKLIST_SESSION_USER,
    CHECKLIST_SESSION_KU,
} from "../constants";
import API from "../services/API";
import { useSelector } from "react-redux";

import pdfMake, { async } from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import { htmlToText } from "html-to-text";
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
pdfMake.vfs = pdfFonts.pdfMake.vfs;
const defaultMaterialTheme = createTheme();
const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
};
function Reports() {

    const [user, setUser] = useState([]);
    const [knowledge, setKnowledge] = useState([]);
    const [userRespnse, setUserResponse] = useState([]);
    const [KResponse, setKResponse] = useState([]);
    const [viewModal, setViewModal] = useState(false);
    const [session, setSession] = useState("");
    useEffect(() => {
        getUserList();
        getTierList();
    }, []);
    const getTierList = async () => {
        // const params = {
        //     include: [
        //         {
        //             relation: "topics",
        //             scope: { include: [{ relation: "units" }] },
        //         },
        //     ],
    

    // let url = `${TIER1_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`;
    const response = await API.get(CHECKLIST_LIST_URL);
    if (response.status === 200) {
        let arr = [];

        response.data.forEach((item) => {

            arr.push({ value: item.id, label: item.title });

        }
        );
        setKnowledge(arr);
    }
};
const getUserList = async () => {
    const response = await API.get(USERS_URL);
    if (response.status === 200) {
        // const data = response.data.filter((item) => item.roles.includes("user"));
        let user = [];
        response.data.map((item) => {
            user.push({ label: item.firstName, value: item.id, name: "user" });
        });
        setUser(user);
    }
};
const tableActions = [

    {
        icon: 'visibility',
        tooltip: 'View',
        onClick: (event, rowData) => {
            setViewModal(true);
            setSession(rowData);
            // viewRisk(rowData)
            // Do save operation
            // console.log(rowData)
            // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
        }
    },
    // {
    //     icon: "ArrowDownload';",
    //     tooltip: "Download",
    //     onClick: (event, rowData) => {
    //         pdfGenerateReport(rowData)
    //         // Do save operation
    //         // console.log(rowData)
    //         // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
    //     },
    // },

]
const columns = [
    {
        field: "user.firstName",
        title: "First Name",
        sort: true,
    },

    {
        field: "checklist.name",
        title: "Checklist Name",
        sort: true,
    },
    // {
    //     field: "topic.title",
    //     title: "Knowledge Topic",
    //     sort: true,
    // },
    // {
    //     field: "unit.title",
    //     title: "Knowledge Unit",
    //     sort: true,
    // },
    // {
    //     field: "insightIndex",
    //     title: "KI",
    //     sort: true,
    // },
    {
        field: "updatedAt",
        title: "Completed Date",
        sort: true,
    },

    // {
    //     dataField: null,
    //     text: "View",
    //     sort: false,
    //     formatter: (cell, row) => {
    //         var item = row;
    //         return (
    //             <div>
    //                 <div className=" option-btn">
    //                     <i
    //                         className="mdi mdi-eye"
    //                         onClick={() => {
    //                             setViewModal(true);
    //                             setSession(item);
    //                         }}
    //                     ></i>
    //                     <i className="mdi mdi-download"
    //                         onClick={() => pdfGenerateReport(item)}></i>
    //                 </div>
    //             </div>
    //         );
    //     },
    // },
];
const onSelectUser = async (e) => {
    const response = await API.get(CHECKLIST_SESSION_USER(e.value));
    if (response.status === 200) {

        const data = response.data.filter((item) => item.status === 'end')
        setUserResponse(data);
    }
};

const onSelectKA = async (e) => {
    const response = await API.get(CHECKLIST_SESSION_KU(e.value));

    if (response.status === 200) {
        const data = response.data.filter((item) => item.status === 'end')
        setKResponse(data);
    }
};
const getYoutube = (youtube_url) => {
    console.log(youtube_url)
    var videoid = youtube_url.match(
        /(?:https?:\/{2})?(?:w{3}\.)?youtu(?:be)?\.(?:com|be)(?:\/watch\?v=|\/)([^\s&]+)/);
    var video_id = youtube_url.split('v=');
    console.log(video_id);
    videoid = video_id[1];
    var ampersandPosition = videoid.indexOf('&');
    if (ampersandPosition != -1) {
        videoid = videoid.substring(0, ampersandPosition);
    }

    if (videoid != null) {
        console.log(videoid);
        var link = "https://www.googleapis.com/youtube/v3/videos?id=" + videoid +
            "&key=AIzaSyA3UijkqMixMnqH4GlvLVmfdYV5Xy9Z_gM&part=snippet,contentDetails,status";
        console.log(link);
        var t_flag = true;
        fetch(link)
            .then(response => response.json())
            .then(data => {
                const t_flag = data.items[0].status.embeddable;

                console.log(t_flag);
                if (!t_flag) {
                    console.log(1);

                    alert("This YouTube Video's publisher doesn't allow embedding");
                } else {
                    console.log(2);
                    return 'https://www.youtube.com/embed/' + videoid;

                }
            });
    }
}

const pdfGenerateReport = (item) => {
    console.log(item)

    const documentDefinition = {
        pageMargins: [40, 40, 40, 25],
        footer: function (currentPage, pageCount) {
            return {
                columns: [
                    // { text: pdf.meetid, alignment: 'left', margin: [10, 0], fontSize: 8 },
                    { text: moment().format('YYYY-MM-DD HH:mm'), alignment: 'center', margin: [10, 0], fontSize: 8 },
                    { text: 'Page ' + currentPage.toString() + ' of ' + pageCount, alignment: 'right', margin: [0, 0, 10, 0], fontSize: 8 }
                ],
                margin: [40, 0]
            };
        },
        header: (currentPage, pageCount) => {
            if (currentPage === 1) {
                return null; // No header on the first page
            }

            return [

                // { image: Logo, width: 30, height: 30, alignment: 'right' }

            ];
        },
        content: [
            // { image: Logo, style: 'logoImg' },
            { text: item.tierThree.title, style: 'header' },

            {
                style: 'tableExample',
                table: {
                    widths: [240, 240],
                    body: [
                        [{ text: 'FirstName', margin: [2, 2, 2, 2] }, { text: item.user.firstname }],
                        [{ text: 'Lastname', margin: [2, 2, 2, 2] }, { text: item.user.lastname }],
                        [{ text: 'KnowledgArea', margin: [2, 2, 2, 2] }, { text: item.tierOne.title }],
                        [{ text: 'KnowledgeTopic', margin: [2, 2, 2, 2], }, { text: item.tierTwo.title }],
                        [{ text: 'KnowledgeUnit', margin: [2, 2, 2, 2], }, { text: item.tierThree.title, }],
                        [{ text: 'EmpId', margin: [2, 2, 2, 2], }, { text: item.user.empId }],
                        [{ text: 'Duration', margin: [2, 2, 2, 2], }, { text: item.duration }],
                        [{ text: 'KnowledgeIndex', margin: [2, 2, 2, 2], }, { text: item.insightIndex }],
                        [{ text: 'Rating', margin: [2, 2, 2, 2], }, { text: item.rating }],
                        [{ text: 'Remarks', margin: [2, 2, 2, 2], }, { text: item.remarks }],
                        [{ text: 'Address', margin: [2, 2, 2, 2], }, { text: item.address }],
                        [{ text: 'CompletionDate', margin: [2, 2, 2, 2], }, { text: item.createdAt }],

                    ]
                },

            },
            { text: 'Step Response', style: 'header', margin: [2, 2, 2, 2] },

            JSON.parse(item.stepsData).map((ite) => {
                return ([
                    {
                        text: ite.name, alignment: 'left', fontSize: 14,
                        bold: true,
                        margin: [0, 20, 0, 0]
                    },
                    ite.value.map((item1) => {
                        return ([
                            item1.toolType === "MCQ" ? (
                                {
                                    text: htmlToText.toString(item1.title, {
                                        wordwrap: 130
                                    }),
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },
                                item1.radios.map((option) => {
                                    if (option.u_select === true) {
                                        return ([
                                            {
                                                text: option.value,

                                                margin: [0, 20, 0, 0]

                                            },
                                        ])
                                    }
                                })
                            ) : item1.toolType === "CHECK_INPUT" ? (
                                {
                                    text: htmlToText.toString(item1.title, {
                                        wordwrap: 130
                                    }),
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },
                                item1.radios.map((option) => {
                                    if (option.u_select === true) {
                                        return ([
                                            {
                                                text: option.value,

                                                margin: [0, 20, 0, 0]

                                            },
                                        ])
                                    }
                                })
                            ) : item1.toolType === "OPTION_INPUT" ? (
                                {
                                    text: htmlToText.toString(item1.title, {
                                        wordwrap: 130
                                    }),
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },
                                item1.radios.map((option) => {
                                    if (option.u_select === true) {
                                        return ([
                                            {
                                                text: option.value,

                                                margin: [0, 20, 0, 0]

                                            },
                                        ])
                                    }
                                })
                            ) : item1.toolType === "TEXT_INPUT" ? ([
                                {
                                    text: item1.title,
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },

                                {
                                    text: item1.value,

                                    margin: [0, 20, 0, 0]

                                },


                            ]) : item1.toolType === "SIGN_INPUT" ? ([
                                {
                                    text: item1.title,
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },

                                {
                                    text: item1.value,

                                    margin: [0, 20, 0, 0]

                                },


                            ]) : item1.toolType === "AUDIO_INPUT" ? ([
                                {
                                    text: item1.title,
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },

                                {
                                    text: item1.value,

                                    margin: [0, 20, 0, 0]

                                },


                            ]) : item1.toolType === "VIDEO_INPUT" ? ([
                                {
                                    text: item1.title,
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },

                                {
                                    text: item1.value,
                                    margin: [0, 20, 0, 0]

                                },


                            ]) : item1.toolType === "AUDIO_INPUT" ? ([
                                {
                                    text: item1.title,
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },

                                {
                                    text: item1.value,

                                    margin: [0, 20, 0, 0]

                                },


                            ]) : item1.toolType === "IMAGE_INPUT" ? ([
                                {
                                    text: item1.title,
                                    bold: true,
                                    margin: [0, 20, 0, 0]

                                },

                                {
                                    text: item1.value,

                                    margin: [0, 20, 0, 0]

                                },


                            ]) : ('')


                        ])
                    })

                ])
            })





        ],

        styles: {
            tableHeader: {
                bold: true,
                fontSize: 12,
                fillColor: '#F2F2F2',
                alignment: 'center'
            },
            logoImg: {
                alignment: 'center',
                width: 100
            },
            hazardName: {
                alignment: 'center',
                verticalAlignment: 'middle',
                margin: [0, 10, 0, 10]
            },
            hazardlogoImg: {
                height: 50,
                width: 50,
                alignment: 'center',
                verticalAlignment: 'middle'
            },
            header: {
                fontSize: 14,
                bold: true,
                margin: [0, 20, 0, 10],
                alignment: 'center'
            },
            subheader: {
                fontSize: 13,
                bold: true,
                margin: [0, 10, 0, 2]
            },
            subsubheader: {
                fontSize: 12,
                bold: true,
                margin: [0, 20, 0, 15],
                alignment: 'center'
            },
            tableYes: {
                margin: [0, 20, 0, 20],
                alignment: 'center'
            },
            tableExample: {
                margin: [0, 10, 0, 10],
            }
        },
        defaultStyle: {
            // alignment: 'justify'

            lineHeight: 1.2
        },
        layout: {
            headerLayout: {
                vLineWidth: () => 1,
                hAlign: 'center',
                paddingTop: () => 5,
                paddingBottom: () => 5
            },
        },

    }
    console.log(documentDefinition)
    pdfMake.createPdf(documentDefinition).download(`${item.tierThree.title + '-' + moment().format('YYYYMMDD HH:mm')}.pdf`);

}
return (
    <>
        <div>
            <div className="row">
                <div className="col-12">
                    <div className="card">
                        <div className="card-body tab-custom-pills-horizontal">
                            <h4 className="card-title">Checklist Response Reports</h4>
                            <Tab.Container id="left-tabs-example">
                                <Row>
                                    <Col xs={12}>
                                        <Nav variant="pills" className="flex-column">
                                            <Nav.Item>
                                                <Nav.Link
                                                    eventKey="first"
                                                    className="d-flex align-items-center"
                                                >
                                                    By User
                                                </Nav.Link>
                                            </Nav.Item>
                                            <Nav.Item>
                                                <Nav.Link
                                                    eventKey="second"
                                                    className="d-flex align-items-center"
                                                >
                                                    By Checklist
                                                </Nav.Link>
                                            </Nav.Item>
                                        </Nav>
                                    </Col>
                                    <Col xs={12}>
                                        <Tab.Content>
                                            <Tab.Pane eventKey="first">
                                                <div className="row">
                                                    <div className="col-6">
                                                        <Select
                                                            labelKey="label"
                                                            id="user_description"
                                                            options={user}
                                                            placeholder="Select User"
                                                            onChange={(e) => onSelectUser(e)}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-12">
                                                        <div>
                                                            {/* <SagoTable
                                                                    columns={columns}
                                                                    data={userRespnse}
                                                                /> */}
                                                            <ThemeProvider theme={defaultMaterialTheme}>
                                                                <MaterialTable
                                                                    columns={columns}
                                                                    data={userRespnse}
                                                                    title=""
                                                                    style={tableStyle}
                                                                    actions={tableActions}
                                                                    options={{
                                                                        actionsColumnIndex: -1,
                                                                    }}
                                                                />
                                                            </ThemeProvider>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Tab.Pane>
                                            <Tab.Pane eventKey="second">
                                                <div className="row">
                                                    <div className="col-6">
                                                        <Select
                                                            labelKey="label"
                                                            id="user_description"
                                                            options={knowledge}
                                                            placeholder="Select Unit"
                                                            onChange={(e) => onSelectKA(e)}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-12">
                                                        <div>
                                                            {/* <SagoTable columns={columns} data={KResponse} /> */}
                                                            <ThemeProvider theme={defaultMaterialTheme}>
                                                                <MaterialTable
                                                                    columns={columns}
                                                                    data={KResponse}
                                                                    title=""
                                                                    style={tableStyle}
                                                                    actions={tableActions}
                                                                    options={{
                                                                        actionsColumnIndex: -1,
                                                                    }}
                                                                />
                                                            </ThemeProvider>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Tab.Pane>
                                        </Tab.Content>
                                    </Col>
                                </Row>
                            </Tab.Container>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <Modal
            show={viewModal}
            size={"lg"}
            onHide={() => setViewModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
        >
            <Modal.Header closeButton>
                <Modal.Title>View Session</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {session !== "" ? (
                    <>
                        <Table bordered>
                            <tbody>
                                <tr>
                                    <td style={{ fontWeight: "bold" }}>FirstName</td>
                                    <td>{session.user.firstName}</td>
                                    {/* <td style={{ fontWeight: "bold" }}>LastName</td>
                                        <td>{session.user.lastname}</td> */}
                                    {/* <td style={{ fontWeight: "bold" }}>KnowledgeArea</td>
                                    <td>{session.area.title}</td>
                                    <td style={{ fontWeight: "bold" }}>KnowledgeTopic</td>
                                    <td>{session.topic.title}</td> */}
                                </tr>
                                <tr>
                                    {/* <td style={{ fontWeight: "bold" }}>KnowledgeUnit</td>
                                    <td>{session.unit.title}</td>
                                    {/* <td style={{ fontWeight: "bold" }}>ID</td>
                                        <td>{session.user.empId}</td> */}
                                    {/* <td style={{ fontWeight: "bold" }}>Email</td>
                                    <td>{session.user.email}</td>
                                    <td style={{ fontWeight: "bold" }}>Duration</td>
                                    <td>{session.duration}</td> */} 
                                </tr>
                                <tr>
                                    {/* <td style={{ fontWeight: "bold" }}>Knowledge Index</td>
                                    <td>{session.insightIndex}</td>
                                    <td style={{ fontWeight: "bold" }}>Rating</td>
                                    <td>{session.rating}</td>
                                    <td style={{ fontWeight: "bold" }}>Remarks</td>
                                    <td>{session.remarks}</td>
                                    <td style={{ fontWeight: "bold" }}>Address</td>
                                    <td>{session.address}</td> */}
                                </tr>
                                <tr>
                                    {/* <td style={{ fontWeight: "bold" }}>CompletionDate</td>
                                    <td>{session.updatedAt}</td> */}
                                </tr>
                            </tbody>
                        </Table>
                        <div className="step-title m-4">
                            <h2 style={{ fontSize: 20, textAlign: "center" }}>
                                Steps Response
                            </h2>
                        </div>
                        <div className="steps">
                            {console.log(JSON.parse(session.stepsData))}
                            {JSON.parse(session.stepsData).map((item1) => {
                              
                                return (
                                    <>
                                        {/* <h3
                                            className="title"
                                            style={{
                                                fontSize: 18,
                                                textAlign: "center",
                                                padding: 12,
                                                background: "#f5f5f5",
                                            }}
                                        >
                                            {item.name}
                                        </h3> */}

                                      
                                           
                                                <div className="col-12">
                                                    {item1.toolType === "MCQ" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="mdi mdi-chart-arc  mr-1"></i> IMCQ</div>
                                                            <div className="form-group card-body">
                                                                <div
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: item1.title,
                                                                    }}
                                                                />
                                                                {item1.radios.map((option) => {
                                                                    if (option.u_select === true) {
                                                                        if (option.selected === true) {
                                                                            return <p style={{ color: 'green' }}>{option.value}</p>;
                                                                        } else {
                                                                            return <p style={{ color: 'red' }}>{option.value}</p>;
                                                                        }
                                                                    }
                                                                })}
                                                            </div>
                                                        </div>
                                                    ) : item1.toolType === "BANK" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="mdi mdi-chart-arc  mr-1"></i> Ques Bank</div>
                                                            <div className="form-group card-body">
                                                                <div
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: item1.title,
                                                                    }}
                                                                />

                                                                {item1.question.map((ite) => {
                                                                    return (<>
                                                                        <div
                                                                            style={{ fontSize: 16, fontWeight: "bold" }}
                                                                            dangerouslySetInnerHTML={{
                                                                                __html: ite.title,
                                                                            }}
                                                                        />
                                                                        {ite.radios.map((option) => {
                                                                            if (option.u_select === true) {
                                                                                if (option.selected === true) {
                                                                                    return <p style={{ color: 'green' }}>{option.value}</p>;
                                                                                } else {
                                                                                    return <p style={{ color: 'red' }}>{option.value}</p>;
                                                                                }
                                                                            }
                                                                        })}

                                                                    </>)

                                                                })}

                                                            </div>
                                                        </div>
                                                    ) : item1.toolType === "PARAGRAPH" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-file-text-o  mr-1"></i> Paragraph</div>
                                                            <div className="form-group card-body">
                                                                <label
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: item1.content,
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>

                                                    ) : item1.toolType === "CHECK_INPUT" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-check-square-o  mr-1"></i> CheckPoints</div>
                                                            <div className="form-group card-body">
                                                                <label
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: item1.title,
                                                                    }}
                                                                />
                                                                {item1.radios.map((option) => {
                                                                    if (option.selected === true) {
                                                                        return <p>{option.value}</p>;
                                                                    }
                                                                })}
                                                            </div>
                                                        </div>
                                                    ) : item1.toolType === "OPTION_INPUT" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-list-alt mr-1"></i> Option</div>
                                                            <div className="form-group card-body">
                                                                <label
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: item1.title,
                                                                    }}
                                                                />
                                                                {item1.radios.map((option) => {
                                                                    if (option.selected === true) {
                                                                        return <p>{option.value}</p>;
                                                                    }
                                                                })}
                                                            </div>
                                                        </div>
                                                    ) : item1.toolType === "YOUTUBE" ? (<></>
                                                        // <div className="col-12 mb-3 step-type card">
                                                        //   <div class="card-header"><i class="fa fa-youtube-play mr-1"></i> Youtube</div>
                                                        //   <div className="form-group card-body">
                                                        //     <iframe
                                                        //       width="420"
                                                        //       height="315"
                                                        //       title="test"
                                                        //       style={{ width: "100%" }}
                                                        //       src={item1.title}
                                                        //     ></iframe>
                                                        //   </div>
                                                        // </div>
                                                    ) : item1.toolType === "VIDEO" ? (<></>
                                                        // <div className="col-12 mb-3 step-type card">
                                                        //   <div class="card-header"><i class="fa fa-film mr-1"></i> Video</div>
                                                        //   <div className="form-group card-body">
                                                        //     <iframe
                                                        //       width="420"
                                                        //       height="315"
                                                        //       title="test"
                                                        //       style={{ width: "100%" }}
                                                        //       src={'https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/form_video/' + item1.title}
                                                        //     ></iframe>
                                                        //   </div>
                                                        // </div>
                                                    ) : item1.toolType === "TEXT_INPUT" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-film mr-1"></i> TextBox</div>
                                                            <div className="form-group card-body">
                                                                <h5>{item1.title}</h5>
                                                                <p>{item1.value}</p>
                                                            </div>
                                                        </div>
                                                    ) : item1.toolType === "PDF" ? (<></>
                                                        // <div className="col-12 mb-3 step-type card">
                                                        //   <div class="card-header"><i class="fa fa-film mr-1"></i> Pdf</div>
                                                        //   <div className="form-group card-body">
                                                        //     <iframe
                                                        //       width="420"
                                                        //       height="315"
                                                        //       title="test"
                                                        //       style={{ width: "100%" }}
                                                        //       src={'https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/pdf_uploads/' + item1.title}
                                                        //     ></iframe>
                                                        //   </div>
                                                        // </div>
                                                    ) : item1.toolType === "EMBEDCODE" ? (<></>
                                                        // <div className="col-12 mb-3 step-type card">
                                                        //   <div class="card-header"><i class="fa fa-film mr-1"></i> Embed Code</div>
                                                        //   <div className="form-group card-body">
                                                        //     <div dangerouslySetInnerHTML={{ __html: this.state.title }} style={{ width: '100%' }} />
                                                        //   </div>
                                                        // </div>
                                                    ) : item1.toolType === "AUDIO" ? (<></>
                                                        // <div className="col-12 mb-3 step-type card">
                                                        //   <div class="card-header"><i class="fa fa-volume-up mr-1"></i> Audio</div>
                                                        //   <div className="form-group card-body">
                                                        //     <audio controls>
                                                        //       <source
                                                        //         src={
                                                        //           "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/form_audio/" +
                                                        //           item1.title
                                                        //         }
                                                        //       />
                                                        //     </audio>
                                                        //   </div>
                                                        // </div>
                                                    ) : item1.toolType === "IMAGE" ? (<></>
                                                        // <div className="col-12 mb-3 step-type card">
                                                        //   <div class="card-header"><i class="fa fa-picture-o mr-1"></i> Image</div>
                                                        //   <div className="form-group card-body">
                                                        //     <img alt="test" style={{ width: '300px', height: '300px', margin: 'auto' }}
                                                        //       src={
                                                        //         "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/form_image/" +
                                                        //         item1.title
                                                        //       }
                                                        //     />

                                                        //   </div>
                                                        // </div>
                                                    ) : item1.toolType === "WEB_LINK" ? (<></>
                                                        // <div className="col-12 mb-3 step-type card">
                                                        //   <div class="card-header"><i class="fa fa-link mr-1"></i> Weblink</div>
                                                        //   <div className="form-group card-body">
                                                        //     <a target={'_blank'} href={item1.value} rel="noreferrer">{item1.title}</a>

                                                        //   </div>
                                                        // </div>
                                                    ) : item1.toolType === "SIGN_INPUT" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-pencil-square-o mr-1"></i> Sign Input</div>
                                                            <div className="form-group card-body">
                                                                <label
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}>{item1.title}</label>
                                                                {item1.value ?
                                                                    <img alt="test" style={{ width: '300px', height: '300px', margin: 'auto' }}
                                                                        src={
                                                                            item1.value
                                                                        }
                                                                    />
                                                                    : <p>No Submittion</p>}

                                                            </div>
                                                        </div>

                                                    ) : item1.toolType === "AUDIO_INPUT" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-microphone mr-1"></i> Audio Input</div>
                                                            <div className="form-group card-body">
                                                                <label
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}>{item1.title}</label>
                                                                {item1.value ?
                                                                    <audio controls>
                                                                        <source
                                                                            src={
                                                                                item1.value
                                                                            }
                                                                        />
                                                                    </audio> :
                                                                    <p>No Submittion</p>}

                                                            </div>
                                                        </div>

                                                    ) : item1.toolType === "VIDEO_INPUT" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-video-camera  mr-1"></i> Video Input</div>
                                                            <div className="form-group card-body">
                                                                <label
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}>{item1.title}</label>
                                                                {item1.value ?
                                                                    <iframe
                                                                        width="420"
                                                                        height="315"
                                                                        title="test"
                                                                        style={{ width: "100%" }}
                                                                        src={item1.value}
                                                                    ></iframe>
                                                                    : <p>No Submittion</p>}
                                                            </div>
                                                        </div>

                                                    ) : item1.toolType === "IMAGE_INPUT" ? (
                                                        <div className="col-12 mb-3 step-type card">
                                                            <div class="card-header"><i class="fa fa-camera-retro  mr-1"></i> Image Input</div>
                                                            <div className="form-group card-body">
                                                                <label
                                                                    style={{ fontSize: 16, fontWeight: "bold" }}>{item1.title}</label>
                                                                {item1.value ?
                                                                    <img alt="test" style={{ width: '300px', height: '300px', margin: 'auto' }}
                                                                        src={
                                                                            item1.value
                                                                        }
                                                                    />
                                                                    : <p>No Submittion</p>}

                                                            </div>
                                                        </div>

                                                    ) : (
                                                        ""
                                                    )}
                                                </div>
                                            
                                        
                                    </>
                                );
                            })}
                        </div>
                    </>
                ) : (
                    ""
                )}
            </Modal.Body>

            <Modal.Footer className="flex-wrap">
                <>
                    <Button variant="light" onClick={() => setViewModal(false)}>
                        Cancel
                    </Button>
                </>
            </Modal.Footer>
        </Modal>
    </>
);
}

export default Reports;
