import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { AIR_WITH_ID_URL, PUBLIC_AIR_WITH_ID_URL, STATIC_URL } from '../constants';
import API from '../services/API';
import { useState } from 'react';
import { IRPdf } from "./IRPdf";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import cogoToast from 'cogo-toast';
import AWS from 'aws-sdk';
import { Document, Page, pdfjs } from "react-pdf";
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;



pdfMake.vfs = pdfFonts.pdfMake.vfs;
AWS.config.update({
    accessKeyId: 'YOUR_ACCESS_KEY_ID',
    secretAccessKey: 'YOUR_SECRET_ACCESS_KEY',
    region: 'YOUR_REGION'
});

const DownloadPDF = () => {

    const [numPages, setNumPages] = useState(null);
    const [scale, setScale] = useState(1);
    function onDocumentLoadSuccess({ numPages }) {
        setNumPages(numPages);
    }

    const zoomIn = () => {
        setScale(scale + 0.1);
    };

    const zoomOut = () => {
        if (scale > 0.1) setScale(scale - 0.1);
    };
    const { id } = useParams();
    const [incidentData, setIncidentData] = useState({})
    useEffect(() => {
        // Function to handle PDF download


        const getReportIncident = async (id) => {

            const uriString = {
                include: [
                    'locationOne',
                    'locationTwo',
                    'locationThree',
                    'locationFour',
                    'locationFive',
                    'locationSix',
                    'lighting',
                    'surfaceCondition',
                    'surfaceType',
                    'workActivityDepartment',
                    'workActivity',
                    'reporter',
                    'workingGroup',
                    'weatherCondition',
                    'reviewer',
                    'drivers',
                    'surveyor',
                    'estimator',
                    'trainee',
                    'gmOps',
                    'thirdParty',
                    'security',
                    'costReviewer',
                    'financer',
                    'dutyEngManager',
                    'incidentTypeName'
                ]
            };

            const url = `${PUBLIC_AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

            const response = await API.get(url);
            if (response.status === 200) {

                const data = response.data;

                data.evidence = response.data.evidence ? response.data.evidence.map(i => {
                    return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
                }) : []


                setIncidentData(data)

            } else {
                cogoToast.error('Something Went Wrong')
            }
        }
        getReportIncident(id)

    }, [id]); // Depend on 'id' so effect runs when it changes

    const [pdfUrl, setPdfUrl] = useState('')

    const downloadPDF = async () => {
        // Use the 'id' to determine which PDF to download
        // ... Your existing logic ...
        try {
            IRPdf(incidentData).then(IRData => {
                const pdfDocGenerator = pdfMake.createPdf(IRData);

                pdfDocGenerator.getBuffer((buffer) => {
                    const pdfBlob = new Blob([buffer], { type: 'application/pdf' });
                    const tempUrl = URL.createObjectURL(pdfBlob);
                    // window.location = tempUrl;  
                    setPdfUrl(tempUrl);
                });
            });
        } catch (e) {
            console.log(e);
        }
    };

    useEffect(() => {
        if (Object.keys(incidentData).length > 0) {
            downloadPDF();
        }
    }, [incidentData]);

    useEffect(() => {
        return () => {
            if (pdfUrl) {
                URL.revokeObjectURL(pdfUrl);
            }
        };
    }, [pdfUrl]);
    const fonts = {
        Roboto: {
            normal: 'Roboto-Regular.ttf',
            bold: 'Roboto-Medium.ttf',
            italics: 'Roboto-Italic.ttf',
            bolditalics: 'Roboto-MediumItalic.ttf'
        }
    };




    return (
        pdfUrl && (
            <>
                <div className="pdf-container">
                    <div className="zoom-controls">
                        <button onClick={zoomOut}>-</button>
                        <button onClick={zoomIn}>+</button>
                    </div>
                    <Document
                        file={pdfUrl}
                        onLoadSuccess={onDocumentLoadSuccess}
                    >
                        {Array.from(new Array(numPages), (el, index) => (
                            <div key={`page_${index + 1}`} className="page-container">
                                <Page
                                    pageNumber={index + 1}
                                    scale={scale}
                                    renderTextLayer={false}
                                    renderAnnotationLayer={false}
                                />
                            </div>
                        ))}
                    </Document>
                    <style jsx>{`
        .pdf-container {
          overflow: auto; // Allows for scrolling when zoomed in
        }

        .page-container {
          box-shadow: 0 4px 5px rgba(0, 0, 0, 0.2);
          margin-bottom: 20px;
          break-after: page; // For printing
        }

        .zoom-controls {
          position: fixed;
          top: 10px;
          right: 10px;
          background: white;
          border: 1px solid #ddd;
          padding: 5px;
          z-index: 100000;
        }
      `}</style>
                </div>
            </>
        )
    );
};

export default DownloadPDF;
