/* eslint-disable jsx-a11y/anchor-is-valid */
// @ts-nocheck
import React, { useState, useRef, useEffect } from "react";
import ListBoxItem from "./ListBoxItem";
import { Form, Modal, Button } from "react-bootstrap";
import Editable from "react-bootstrap-editable";
import autoAnimate from "@formkit/auto-animate";
import { K_EDIT_TIER_URL,   } from "../constants";
import DatePicker from "react-datepicker";
import Select from 'react-select';
import moment from 'moment';

import { singlePopup } from "../notifications/Swal";
import { Accordion } from 'react-bootstrap';
// import { dateRegex } from "../utils/Regex";
import uuid from "react-uuid";
import { deletePopup } from './../notifications/Swal';
import cogoToast from "cogo-toast";
import K_ListBoxItem from "./K_ListBoxItem";

const K_ListBox = (props) => {
    console.log(props)
    const animateListItem = useRef();
    const animateTestItem = useRef();
    const [showModal, setShowModal] = useState(false);
    const [tierDetails, setTierDetails] = useState({ title: '', certificate_date: new Date(), documents: [] });


    const title = useRef();
    const itemName = useRef();
    const itemDescription = useRef();
    const itemModel = useRef();
    const itemSerial = useRef();
  

    const customStyles = {

    }

    const handleConfigurationModal = (id) => {

        getTierDetails(props.mode, id);

    }

    const handleDocumentChange = (newValue, newAction) => {
        setTierDetails((prev) => (
            {
                ...prev,
                documents: newValue
            }
        ))
    }

    const handleChecklistChange = (newValue, newAction, id) => {
     
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.checklistId = newValue.value;
            }

            return i;
        }))
    }

    const parseDate = (value) => {
        const dateArray = value.split("/");
        return new Date(parseInt(dateArray[2]), parseInt(dateArray[1]) - 1, parseInt(dateArray[0]));
    }

    const getTierDetails = async (mode, id) => {
        const response = await fetch(K_EDIT_TIER_URL(mode, id));
        if (response.ok) {
            const tierData = await response.json();
            // const newDocumentData = tierData.documents ? await tierData.documents.map((i) => {

            //     return props.documents.find(document => document.value === parseInt(i))
            // }) : [];




            // tierData.documents = newDocumentData;
            // getTestCase(mode, id);
            setTierDetails(tierData);
            setShowModal(true);
        }
    }

    // const handleCreateTest = async () => {
        
    //     const newTestCase = {
    //         name: 'Enter Description',
    //         tierId: tierDetails.id,
    //         tierMode: props.mode,
    //         locationId: props.location,
    //         status: 0,
    //         maskId: uuid(),

    //     }
    //     const response = await fetch(NEW_TEST_CASE_URL, {
    //         method: 'POST',
    //         body: JSON.stringify(newTestCase),
    //         headers: {
    //             "Content-type": "application/json; charset=UTF-8"
    //         }

    //     });

    //     if(response.ok) {
    //         const newTestCaseData = await response.json();
    //         setTestCase(prev => [...prev, {...newTestCaseData, date: '', duration: '', units: '', imca: '', nextDate: '', checklistId: '' }])
    //         cogoToast.info('Created', { position: 'top-right' })
    //     }
    // }

    const [testCase, setTestCase] = useState([])
    // const getTestCase = async (mode, id) => {
    //     const uriString = {
    //         "where": { tierMode: mode, tierId: id }
    //     }

    //     const url = `${TEST_CASE_URL}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
    //     const response = await fetch(url);
    //     if (response.ok) {
    //         setTestCase(await response.json())
    //     }

    // }

    const handleCreateItem = (e) => {
        e.preventDefault();
        // @ts-ignore
        props.onHandleCreateItem(title.current.value);
        // @ts-ignore
        title.current.value = '';
        

    }

    // const handleTestUpdate = async (id) => {
    //     let testCaseData = testCase.find(i => i.id === id);
        
    //     if(!testCaseData.nextDate || !dateRegex.test(testCaseData.nextDate)) {
    //         testCaseData.nextDate = calculateNextDate(id);
    //     }
    //     testCaseData.maskId = testCaseData.maskId ? testCaseData.maskId : uuid();
    //     testCaseData.locationId = props.location;
    //     const response = await fetch(TEST_CASE_WITH_ID_URL(id), {
    //         method: 'PATCH',
    //         body: JSON.stringify({
    //             checklistId: testCaseData.checklistId,
    //             date: testCaseData.date,
    //             duration: testCaseData.duration,
    //             imca: testCaseData.imca || '',
    //             locationId: testCaseData.locationId,
    //             maskId: testCaseData.maskId,
    //             name: testCaseData.name,
    //             nextDate: testCaseData.nextDate,
    //             units: testCaseData.units,
    //         }),
    //         headers: {
    //             "Content-type": "application/json; charset=UTF-8"
    //         }
    //     });

    //     if (response.ok) {
        
    //         singlePopup.fire(
    //             'Updated!',
    //             '',
    //             'success'
    //         );
    //     }

    // }

    // const handleTestDelete = async (id) => {
    //     deletePopup.fire({
    //         title: 'Are you sure?',
    //         text: "You won't be able to revert this!",
    //         icon: 'warning',
    //         showCancelButton: true,
    //         reverseButtons: true,
        
    //         confirmButtonText: 'Delete'
    //       }).then(async (result) => {
    //         if (result.isConfirmed) {
    //         //   deleteChecklist(id);

    //             const response = await fetch(NEW_TEST_CASE_WITH_ID_URL(id), {
    //                 method: 'DELETE',
    //                 headers: {
    //                     "Content-type": "application/json; charset=UTF-8"
    //                 }
    //             })
    //             if(response.ok) {
    //                 setTestCase(prev => prev.filter(i => i.id !== id));
    //                 singlePopup.fire(
    //                     'Deleted!',
    //                     '',
    //                     'success'
    //                 );
    //             }
                
    //         }
    //       })
    // }
    const handleCardUpdate = async () => {
        const response = await fetch(K_EDIT_TIER_URL(props.mode, tierDetails.id), {
            method: 'PATCH',
            body: JSON.stringify({
                title: itemName.current.value,
              
            }),
            headers: {
                "Content-type": "application/json; charset=UTF-8"
            }
        });

        if (response.ok) {
            props.changeTitle(tierDetails.id, itemName.current.value)
            singlePopup.fire(
                'Updated!',
                '',
                'success'
            );
        }
        setShowModal(false)
    }

    useEffect(() => { animateListItem.current && autoAnimate(animateListItem.current) }, [animateListItem])
    useEffect(() => { animateTestItem.current && autoAnimate(animateTestItem.current) }, [animateTestItem])

    const handleLastDate = (date, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.date = moment(date).format('DD/MM/YYYY');
            }

            return i;
        }))
    }

    const handleNextDate = (date, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.nextDate = moment(date).format('DD/MM/YYYY');
            }

            return i;
        }))
    }

    const handleImcaRef = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.imca = event.target.value;
            }

            return i;
        }))
    }

    const handleTest = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.name = event.target.value;
            }

            return i;
        }))
    }


    const handleDuration = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.duration = event.target.value;
            }

            return i;
        }))


    }


    const handleUnits = (event, id) => {
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.units = event.target.value;
            }

            return i;
        }))

    }

    const calculateNextDate = (id) => {
        const testCaseData = testCase.find(i => i.id === id);
        const lastDate = parseDate(testCaseData.date);
        let nextGeneratedDate = '';
        switch (testCaseData.units) {
            case 'Hrs':
                nextGeneratedDate = moment(lastDate.setTime(lastDate.getTime() + parseInt(testCaseData.duration) * 60 * 60 * 1000)).format('DD/MM/YYYY');

                break;
            case 'Days':
                nextGeneratedDate = moment(lastDate.setTime(lastDate.getTime() + parseInt(testCaseData.duration) * 24 * 60 * 60 * 1000)).format('DD/MM/YYYY');
                break;

            case 'Months':
                nextGeneratedDate = moment(lastDate.setMonth(lastDate.getMonth() + parseInt(testCaseData.duration))).format('DD/MM/YYYY');
                break;
            default: break;
        }
        setTestCase(prev => prev.map(i => {
            if (i.id === id) {
                i.nextDate = nextGeneratedDate
            }

            return i;
        }))

        return nextGeneratedDate;
    }

    const handleDeleteItem = (id) => {
        props.handleDeleteItem(props.mode, id)
    }

    const handleClone= (id) => {
        props.handleClone(props.mode, id)
    }
    const handleCurate= (id) => {
        props.handleCurate(props.mode, id)
    }
    const handleActivity= (id) => {
        props.handleActivity(props.mode, id)
    }

    return (<>
        <div className="card">
            <div className="card-body p-0">
                <div className="card-title pencil-icon p-3 pb-0 mb-0">
                    <Editable initialValue={props.title} onSubmit={(value) => props.onEditTitle(value, props.mode)} className="d-flex" mode="inline" />
                </div>

                {
                    props.selected && (<>
                        <Form.Group className="form-group p-3 mb-0">
                            <div className="input-group">
                                <Form.Control type="text" ref={title} className="form-control mb-0" placeholder="Enter new item to list" aria-label="item" aria-describedby="basic-addon2" />
                                <div className="input-group-append">
                                    <button className="btn btn-primary btn-icon mb-0" type="button" onClick={(e) => { handleCreateItem(e) }}><i className="mdi mdi-plus-circle"></i></button>
                                </div>
                            </div>
                        </Form.Group>
                        <div className="h-250" ref={animateListItem}>
                            {
                                props.lists.map((i) => {
                                    return <K_ListBoxItem key={i.id} mode={props.mode}  onHandleActivity={handleActivity} onHandleClone={handleClone} onHandleCurate={handleCurate} onHandleDelete={handleDeleteItem} selectedId={props.selectedItem.id} data={i} onHandleSelect={(id) => props.handleSelect(id)} onHandleConfigure={handleConfigurationModal} />
                                })
                            }


                        </div>
                    </>)
                }

                {!props.selected && (<p className="p-3">Please select a item from left pane to continue</p>)}


            </div>

        </div>

        <Modal
            size="md"
            show={showModal}
            onHide={() => setShowModal(false)}
            aria-labelledby="example-modal-sizes-title-lg"
        >
            <Modal.Header closeButton>
                <Modal.Title>{tierDetails.title}</Modal.Title>
            </Modal.Header>

            <Modal.Body>
                <form className="forms-sample">



                  
                    <div className="form-group">
                        <label htmlFor="title">Item Name</label>
                        <input type="text" ref={itemName} defaultValue={tierDetails.title} className="form-control p-input" id="title" placeholder="Item Name" />
                    </div>
                   


                 

                </form>
            </Modal.Body>

            <Modal.Footer>

                <Button variant="light" onClick={() => setShowModal(false)}>Cancel</Button>
                <Button variant="primary" onClick={handleCardUpdate}>Update Changes</Button>
            </Modal.Footer>
        </Modal>
    </>);
}

export default K_ListBox;