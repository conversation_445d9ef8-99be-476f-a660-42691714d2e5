import React, { Component } from "react";
import Jo<PERSON><PERSON><PERSON>or from "jodit-react";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
class Paragraph extends Component {
  constructor(props) {
    super(props);
    this.state = {
      toolType: "PARAGRAPH",
      content: "",
      mdShow: false,
      isEmpty: false
    };

    this.config = {
      askBeforePasteHTML: false,
      askBeforePasteFromWord: false,
      defaultActionOnPaste: "insert_clear_html",
      toolbarAdaptive: false,
      toolbarButtonSize: "large",
      toolbarSticky: false,
      buttons:
        "|,bold,underline,italic,|,font,fontsize,|,superscript,subscript,|,ul,ol,|,outdent,indent,|,align,paste,image,|",
      enableDragAndDropFileToEditor: true,
    };
  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue(stateFor, value) {
    switch (stateFor) {
      case "TITLE":
        this.setState({ title: value });
        break;
      case "CONTENT":
        this.setState({ content: value });
        break;

      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }
  createChecklistHandler() {
    this.setState({ mdShow: false });
  }
  render() {
    console.log(this.state.mdShow)
    return (
      <>
        <div className="paragraph mb-3" style={this.state.content === '' && this.props.field.isEmpty ? { boxShadow: '0px 0px 12px 3px #dfdfdf', border: '1px solid red', borderRadius: 0 } : { boxShadow: '0px 0px 12px 3px #dfdfdf', borderRadius: 0 }}>
          <div className="card">
            <div className="card-header d-flex justify-content-between">
              <div className="">
              <i className="fa fa-file-text-o  mr-1"></i> Paragraph
              </div>
              
              
          <div className="">
          {this.props.index !== 0 ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveUp(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-up"></i>
                  </span>
                  : ""}
                {this.props.index !== this.props.length ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveDown(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-down"></i>
                  </span>
                  : ''}
                <span
                  className=""
                  onClick={() => this.props.removeField(this.props.index)}
                >
                  <i className="mdi mdi-close"></i>
                </span>
              </div>
            </div>

            <div className="card-body">
              <div className="col-12 text-center">
                <Button
                  variant="primary"
                  onClick={() => this.setState({ mdShow: true })}
                >
                  Edit Text
                </Button>
              </div>

              {this.state.content === "" ? (
                ""
              ) : (
                <div className="col-12 mt-3 mb-3 paragraph">
                  <p dangerouslySetInnerHTML={{ __html: this.state.content }} />
                </div>

              )}

              {/* <div className="form-group">
              <label className="label" htmlFor="paragraph">
                Paragraph
              </label>
              <textarea
                id="paragraph"
                value={this.state.content}
                onChange={(e) => this.changeValue("CONTENT", e.target.value)}
                className="form-control editor"
              />
           
            </div> */}
            </div>
          </div>
        </div>
        <Modal
          show={this.state.mdShow}
          onHide={() => this.setState({ mdShow: false })}
          size="lg"
          aria-labelledby="example-modal-sizes-title-md"
        >
          <Modal.Body>
            <JoditEditor
              ref={(l) => (this.tooList = l)}
              value={this.state.content}
              config={this.config}

              onBlur={(newContent) => this.changeValue("CONTENT", newContent)}
              onChange={(newContent) => this.changeValue("CONTENT", newContent)}
            />
          </Modal.Body>

          <Modal.Footer className="flex-wrap">
            <>
              <Button
                variant="light"
                onClick={() => this.setState({ mdShow: false })}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={() => this.createChecklistHandler()}
              >
                Create
              </Button>
            </>
          </Modal.Footer>
        </Modal>
      </>
    );
  }
}

export default Paragraph;
