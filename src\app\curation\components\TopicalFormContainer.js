import React, { Component } from "react";
import SingleField from "./Types/SingleField";
import SelectField from "./Types/SelectField";
import CheckBoxes from "./Types/CheckBoxes";
import Preview from "./Preview";
import RadioButtons from "./Types/RadioButtons";
import Paragraph from "./Types/Paragraph";
import DurationPicker from "./Types/DurationPicker";
import Images from "./Types/Images";
import Youtube from "./Types/Youtube";
import Videos from "./Types/Videos";
import Weblink from "./Types/Weblink";
import Audios from "./Types/Audio";
import PDF from "./Types/PDF";
import EmbedCode from "./Types/EmbedCode";
import MCQ from "./Types/MCQ";
import TextInput from "./Types/TextInput";
import ImageInput from "./Types/ImageInput";
import VideoInput from "./Types/VideoInput";
import AudioInput from "./Types/AudioInput";
import OptionInput from "./Types/OptionInput";
import SignInput from "./Types/SignInput";
import CheckInput from "./Types/CheckInput";
import Multimedia from "./Types/MultiMedia";
import Bank from "./Types/Bank";
import cogoToast from "cogo-toast";
import { Modal, Button } from "react-bootstrap";
class TopicalFormContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dragActive: false,
      fields: [],
      orders: [],
      change: false,
      nameDuplicate: false,
      modal: false
    };
    this.popForm = this.popForm.bind(this);
    this.catchField = this.catchField.bind(this);
    this.resetStateOrder = this.resetStateOrder.bind(this);
    this.debugStateOrder = this.debugStateOrder.bind(this);
    this.checkRequiredField = this.checkRequiredField.bind(this);
  }

  componentWillMount() {
    if (this.props.updateOnMount === true) {
      this.props.updateForm((form) => {
        this.setState({
          fields: form,
          orders: form,
        });
      });
    }
    
  }

  resetStateOrder() {
    let order = [];
    let $ = window.$;
    let self = this;
    let list = this.tooList;
    let states = self.state.fields;
    $(list)
      .children()
      .each((i, l) => {
        let index = $(l).attr("data-index");
        order.push(states[index]);
      });
    self.setState({
      orders: order,
    });
  }

  ifDuplicated() {
    if (this.state.nameDuplicate) {
      return {
        backgroundColor: "rgb(255, 255, 255)",
        border: "3px solid rgba(37, 45, 42, 0.13)",
      };
    } else {
      return {
        backgroundColor: "inherit",
      };
    }
  }


  render() {
    console.log(this.props)
    return (
      <div className="toolbox" ref={(c) => (this._toolBoxContainer = c)}>
        {this.props.debug === true ? (
          <pre>{JSON.stringify(this.debugStateOrder(), null, 2)}</pre>
        ) : (
          <span hidden={true}></span>
        )}



        <div className="card " >

          <div
            className={
              this.state.dragActive ? "dragActive card-body" : "card-body"
            }
            style={{ height: '75vh', overflowY: 'auto' }}
          >
            {/* {this.state.nameDuplicate ?
                            <p className="alert alert-danger">
                                <strong>Please resolve following errors.</strong>
                                <ul>
                                    <li>Name field cannot be empty</li>
                                    <li>Remove whitespaces from name field</li>
                                    <li>Duplicate name field found</li>
                                </ul>
                            </p> : ''
                        } */}
            <div ref={(l) => (this.tooList = l)} className="list-group">
              {this.state.fields.length > 0 ? (
                this.state.fields.map((field, index) => {
                  return this.renderToolBoxItems(field, index);
                })
              ) : (
                <div>
                  <p
                    style={{
                      textAlign: "center",
                      padding: "2em",
                      fontSize: "18pt",
                      fontWeight: "bold",
                      textTransform: "uppercase",
                      color: "#aaa",
                      backgroundColor: "#eee",
                    }}
                  >
                    Drag a Field
                  </p>
                </div>
              )}
            </div>


          </div>
          <div className="col-12 text-center" style={{ padding: 20 }}>



            <button
              type="button"
              className="btn btn-primary btn-rounded mb-3 "
              onClick={() => this.popForm('save')}
            >
              
              Save
            </button>
          </div>
        </div>
        <Modal
          show={this.state.modal}
          onHide={() => this.setState({ modal: false })}
          size="md"
          aria-labelledby="example-modal-sizes-title-md"
        >
          <Modal.Body>
            <Preview
              previews={this.props.custom}
              fields={this.state.orders}
              id="previewModal"
            />
          </Modal.Body>

          <Modal.Footer className="flex-wrap">
            <>
              <Button
                variant="light"
                onClick={() => this.setState({ modal: false })}
              >
                Cancel
              </Button>

            </>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }

  popForm(type) {
    let order = [];
    let $ = window.$;
    let self = this;
    let list = this.tooList;
    let states = self.state.fields;
    $(list)
      .children()
      .each((i, l) => {
        let index = $(l).attr("data-index");
        order.push(states[index]);
      });
    self.setState({
      orders: order,
    });

    let d = order.filter((data) => {
      return data !== null && data !== undefined;
    });
    let c = d.map((ele) => {
      if (ele.toolType === "MCQ") {
        ele.tab = "";
        ele.mdShow = false;
      } if (ele.toolType === "BANK") {
        ele.tab = "";
        ele.mdShow = false;
      } else if (ele.toolType === "OPTION_INPUT") {
        ele.tab = "";
      } else if (ele.toolType === "PARAGRAPH") {
        ele.mdShow = false;
      }
      return ele;
    });
    // if (this.checkRequiredField(c)) {
    //   console.log(true);
    return this.props.onSave(c, type);
    // } else {
    //   console.log(false);
    //   cogoToast.error("Please fill the empty fields !", {
    //     position: "top-right",
    //   });
    // }


  }
  checkRequiredField = (data) => {
    let requiredCheck = true;
    data.forEach((field) => {
      if (field.toolType === "WEB_LINK") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "IMAGE") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "YOUTUBE") {
        console.log(field)
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "VIDEO") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "PARAGRAPH") {
        if (field.content === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "AUDIO") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "PDF") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "EMBEDCODE") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "MCQ") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "BANK") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "TEXT_INPUT") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "IMAGE_INPUT") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "VIDEO_INPUT") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "AUDIO_INPUT") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "OPTION_INPUT") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "SIGN_INPUT") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "CHECK_INPUT") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else if (field.toolType === "MULTIMEDIA") {
        if (field.title === "") {
          field.isEmpty = true;
          requiredCheck = false;
        }
      } else {
        field.isEmpty = false;
        requiredCheck = true;
      }
    });
    return requiredCheck;
  };
  debugStateOrder() {
    let states = this.state.orders;
    let d = states.filter((data) => {
      return data !== null && data !== undefined;
    });
    return d;
  }

  componentDidMount() {
    let list = this.tooList;
    let toolBoxContainer = this._toolBoxContainer;
    let self = this;
    var $ = window.$;
    $(function () {
      $(toolBoxContainer).droppable({
        drop: function (event, ui) {
          let tool = $(ui.draggable[0]).attr("data-tool");
          if (tool !== undefined) {
            self.catchField(tool);
          }
        },
        over: function (event, ui) {
          self.setState({
            dragActive: true,
          });
        },
        out: function (event, ui) {
          self.setState({
            dragActive: false,
          });
        },
      });
      $(list).sortable({
        update: function (event, ui) {
          self.setState({
            dragActive: false,
          });
          self.resetStateOrder();
        },
        out: function (event, ui) {
          self.setState({
            dragActive: false,
          });
        },
      });
      $(list).disableSelection();
    });

  }

  renderToolBoxItems(field, index) {
    return (
      <div
        key={index}
        data-index={index}
        className="ui-sortable-handle ui-draggable ui-draggable-handle"
      >
        {this.renderTool(field, index)}
      </div>
    );
  }

  renderTool(field, index) {
    if (field.toolType === "WEB_LINK") {
      return (
        <Weblink
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}

        />
      );
    } else if (field.toolType === "IMAGE") {
      return (
        <Images
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "YOUTUBE") {
      return (
        <Youtube
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "VIDEO") {
      return (
        <Videos
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          key={index}
          index={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "PARAGRAPH") {
      return (
        <Paragraph
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          key={index}
          index={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "AUDIO") {
      return (
        <Audios
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "PDF") {
      return (
        <PDF
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "EMBEDCODE") {
      return (
        <EmbedCode
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "MCQ") {
      return (
        <MCQ
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "BANK") {
      return (
        <Bank
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "TEXT_INPUT") {
      return (
        <TextInput
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "IMAGE_INPUT") {
      return (
        <ImageInput
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "VIDEO_INPUT") {
      return (
        <VideoInput
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "AUDIO_INPUT") {
      return (
        <AudioInput
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "OPTION_INPUT") {
      return (
        <OptionInput
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "SIGN_INPUT") {
      return (
        <SignInput
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "CHECK_INPUT") {
      return (
        <CheckInput
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    } else if (field.toolType === "MULTIMEDIA") {
      return (
        <Multimedia
          changeState={(e, index) => this.changeChildState(e, index)}
          field={field}
          index={index}
          key={index}
          removeField={() => this.remove(index)}
          moveUp={() => this.moveUpByOne(index)}
          moveDown={() => this.moveDownByOne(index)}
          length={this.state.fields.length - 1}
        />
      );
    }
  }

  changeChildState(e, index) {
    console.log(e)
    if (index !== -1) {
      let fields = this.state.fields;
      fields[index] = e;
      this.setState({ fields: fields, change: this.state.change });
    }
    this.resetStateOrder();
    this.nameDuplicateReflector();
  }

  moveUpByOne(index) {
    let fields = this.state.fields;
    let temp = fields[index]
    fields[index] = fields[index - 1]
    fields[index - 1] = temp
    this.setState({ fields: fields, change: this.state.change, dragActive: true });
    this.resetStateOrder();
    this.nameDuplicateReflector();
  }
  moveDownByOne(index) {
    let fields = this.state.fields;
    let temp = fields[index]
    fields[index] = fields[index + 1]
    fields[index + 1] = temp
    this.setState({ fields: fields, change: this.state.change, dragActive: true });
    this.resetStateOrder();
    this.nameDuplicateReflector();
  }

  nameDuplicateReflector() {
    // duplicate names
    let f = this.state.fields;
    var arr = [];
    f.forEach((i) => {
      if (
        i.name !== undefined &&
        i.name.trim() !== "" &&
        i.name.indexOf(" ") === -1
      ) {
        arr.push(i.name);
      }
    });
    let unique = arr.filter(function (value, index, self) {
      return self.indexOf(value) === index;
    });
    if (f.length !== unique.length) {
      this.setState({
        nameDuplicate: true,
      });
    } else {
      this.setState({
        nameDuplicate: false,
      });
    }
  }

  remove(indexR) {
    let fields = this.state.fields;
    fields.splice(indexR, 1);
    this.setState({
      fields: fields,
      change: this.state.change,
      dragActive: true
    });
    this.resetStateOrder();
    this.nameDuplicateReflector();
  }

  catchField(data) {
    if (this.props.custom) {
      let toolItem = this.props.custom.filter((tool) => {
        if (tool.toolbox.name === data) {
          return tool;
        } else {
          return false;
        }
      })[0];

      if (toolItem) {
        let fields = this.state.fields;
        fields.push(toolItem.states);
        this.setState({
          dragActive: false,
          fields: fields,
        });
        // this.resetStateOrder();
        this.nameDuplicateReflector();
        return;
      }
    }

    let tools = [
      "PARAGRAPH",
      "MULTIMEDIA",
      "CHECK_INPUT",
      "SIGN_INPUT",
      "OPTION_INPUT",
      "AUDIO_INPUT",
      "VIDEO_INPUT",
      "IMAGE_INPUT",
      "TEXT_INPUT",
      "MCQ",
      "EMBEDCODE",
      "PDF",
      "AUDIO",
      "WEB_LINK",
      "VIDEO",
      "YOUTUBE",
      "IMAGE",
      "BANK"
    ];
    if (tools.indexOf(data) === -1) {
      this.setState({
        dragActive: false,
      });
      return;
    }
    var meta = {};
    if (data === "MULTIMEDIA") {
      meta = {
        toolType: "MULTIMEDIA",
        title: "",
        allowgallery: false,
        validation: {
          isRequired: false,
        },
        isEmpty: false,
      };
    } else if (data === "CHECK_INPUT") {
      meta = {
        multiple: false,
        toolType: "CHECK_INPUT",
        title: "",
        defaultValue: "",
        validation: {
          isRequired: false,
        },
        radios: [{
          value: "Yes",
          selected: false,
        }, {
          value: "No",
          selected: false,
        }, {
          value: "Not Applicable",
          selected: false,
        }],
        duplicate: false,
        isEmpty: false,
      };
    } else if (data === "SIGN_INPUT") {
      meta = {
        toolType: "SIGN_INPUT",
        title: "",
        validation: {
          isRequired: false,
        },
        isEmpty: false,
      };
    } else if (data === "OPTION_INPUT") {
      meta = {
        multiple: false,
        toolType: "OPTION_INPUT",
        title: "",
        defaultValue: "",
        validation: {
          isRequired: false,
        },
        radios: [],
        duplicate: false,
        isEmpty: false,
      };
    } else if (data === "AUDIO_INPUT") {
      meta = {
        toolType: "AUDIO_INPUT",
        title: "",
        allowgallery: false,
        validation: {
          isRequired: false,
        },
        isEmpty: false,
      };
    } else if (data === "VIDEO_INPUT") {
      meta = {
        toolType: "VIDEO_INPUT",
        title: "",
        allowgallery: false,
        validation: {
          isRequired: false,
        },
        isEmpty: false,
      };
    } else if (data === "IMAGE_INPUT") {
      meta = {
        toolType: "IMAGE_INPUT",
        title: "",
        allowgallery: false,
        validation: {
          isRequired: false,
        },
        isEmpty: false,
      };
    } else if (data === "TEXT_INPUT") {
      meta = {
        toolType: "TEXT_INPUT",
        title: "",
        validation: {
          isRequired: false,
        },
      };
    } else if (data === "MCQ") {
      meta = {
        multiple: false,
        toolType: "MCQ",
        title: "",
        defaultValue: "",
        validation: {
          isRequired: false,
        },
        radios: [],
        duplicate: false,
        slider: 0,
        isEmpty: false,
      };
    } else if (data === "BANK") {
      meta = {
        multiple: false,
        toolType: "BANK",
        title: "",
        question: [{ title: '', radios: [], multiple: false }],
        defaultValue: "",
        validation: {
          isRequired: false,
        },

        duplicate: false,
        slider: 0,
        isEmpty: false,
      };
    } else if (data === "EMBEDCODE") {
      meta = {
        toolType: "EMBEDCODE",
        title: "",
        h5p: false,
        isEmpty: false,
      };
    } else if (data === "PDF") {
      meta = {
        toolType: "PDF",
        title: "",
        caption: "",
        isEmpty: false,
        loader: false
      };
    } else if (data === "AUDIO") {
      meta = {
        toolType: "AUDIO",
        title: "",
        caption: "",
        isEmpty: false,
        loader: false
      };
    } else if (data === "WEB_LINK") {
      meta = {
        toolType: "WEB_LINK",
        title: "",

        isEmpty: false,
      };
    } else if (data === "VIDEO") {
      meta = {
        toolType: "VIDEO",
        title: "",
        caption: "",
        isEmpty: false,
        loader: false
      };
    } else if (data === "IMAGE") {
      meta = {
        toolType: "IMAGE",
        title: "",
        caption: "",
        isEmpty: false,
        loader: false
      };
    } else if (data === "YOUTUBE") {
      meta = {
        toolType: "YOUTUBE",
        title: "",
        isEmpty: false,
      };
    } else if (data === "PARAGRAPH") {
      meta = {
        toolType: "PARAGRAPH",
        title: "",
        content: "",
        isEmpty: false,
      };
    }
    let fields = this.state.fields;
    fields.push(meta);
    this.setState({
      dragActive: false,
      fields: fields,
    });
    // this.resetStateOrder();
    this.nameDuplicateReflector();
  }
}

export default TopicalFormContainer;
