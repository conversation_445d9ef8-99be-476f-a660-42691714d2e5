import React, { useState } from 'react';


const ActivityForm = ({ title, activities, vehicles }) => {
    // Initialize state with an object that has keys for each activity
    // and values as objects with keys for each vehicle type.
    const [formState, setFormState] = useState(
        activities.reduce((acc, activity) => {
            acc[activity] = vehicles.reduce((innerAcc, vehicle) => {
                innerAcc[vehicle] = false;
                return innerAcc;
            }, {});
            return acc;
        }, {})
    );

    // Handle change event for the checkboxes
    const handleChange = (activity, vehicle) => {
        // Toggle the state for the corresponding activity and vehicle
        setFormState(prevState => ({
            ...prevState,
            [activity]: {
                ...prevState[activity],
                [vehicle]: !prevState[activity][vehicle],
            }
        }));
    };

    // Function to render form rows
    const renderRows = () => {
        return activities.map(activity => (
            <tr key={activity}>
                <td>{activity}</td>
                {vehicles.map(vehicle => (
                    <td key={vehicle}>
                        <input
                            type="checkbox"
                            checked={formState[activity][vehicle]}
                            onChange={() => handleChange(activity, vehicle)}
                        />
                    </td>
                ))}
            </tr>
        ));
    };

    // Submit form data
    const handleSubmit = (event) => {
        event.preventDefault();
        console.log(formState);
        // Here you would handle the submission of the form state, such as sending to an API
    };

    return (
        <form className='container mt-5' onSubmit={handleSubmit}>
            <h2>{title}</h2>
            <div className='table-responsive'>

                <table className='table table-responsive table-bordered'>
                    <thead>
                        <tr>
                            <th>Activity</th>
                            {vehicles.map(vehicle => (
                                <th key={vehicle}>{vehicle}</th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {renderRows()}
                    </tbody>
                </table>
            </div>
            {/* <button type="submit">Submit</button> */}
        </form>
    );
};

export default ActivityForm;
