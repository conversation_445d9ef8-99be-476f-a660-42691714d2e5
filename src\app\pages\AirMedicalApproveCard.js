import React, { useState, useEffect } from "react";
import { Mo<PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";
import { AIR_DRIVERS_WITH_ID_URL, AIR_SURVEYORS_URL, AIR_WITH_ID_URL, AIR_MEDICAL_WITH_ID_URL, AIR_COST_ESTIMATOR_URL, AIR_REVIEW_RETURN_WITH_ID_URL, AIR_MEDICAL_OFFICER_URL, AIR_APPROVE_REPORTS_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import { DropzoneArea } from 'material-ui-dropzone';
const AirMedicalApproveCard = ({ showModal, setShowModal, data }) => {


    const handleReject = async () => {

    }

    const handleContinue = () => {

    }





    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }


    const [allComments, setAllComments] = useState(
        data.personInvolved.map(person => ({ name: person.name, comments: '' }))
    );

    const handleCommentChange = (name, value) => {
        setAllComments(prevComments => {
            return prevComments.map(comment => {
                if (comment.name === name) {
                    return { ...comment, comments: value };
                } else {
                    return comment;
                }
            });
        });
    }



    const steps = [
        {
            label: 'Approve Medical Report',
            description: (<>

                {
                    <>
                        


                    </>
                }




            </>)
        }
    ]

    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    const handleSubmit = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_APPROVE_REPORTS_WITH_ID_URL(data.id, data.actionId), {
             

            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            // Sending POST requests for each driver using for...of loop to ensure each request completes before the next

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            // Proceed to the next step
            setActiveStep((prevActiveStep) => prevActiveStep + 1);

        } catch (error) {
            console.error('An error occurred:', error);

        }
    };

    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    IR Information
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div>
                        <div className="col-md-6">
                            <Box>
                                <Stepper activeStep={activeStep} orientation="vertical">
                                    {steps.map((step, index) => (
                                        <Step key={step.label}>
                                            <StepLabel>
                                                {step.label}
                                            </StepLabel>
                                            <StepContent>
                                                <Typography>{step.description}</Typography>
                                                <Box sx={{ mb: 2 }}>
                                                    <div>

                                                        {index === steps.length - 1 ? (
                                                            <>
                                                                <div className='form-group'>
                                                                    {/* <select onChange={(e) => setSelectedReviewer(e.target.value)} className='form-control'>
                                                                <option value={''}>Choose Incident Owner</option>
                                                                {
                                                                    incidentReviewer.map(user => {
                                                                        return (
                                                                            <option value={user.id}>{user.firstName}</option>
                                                                        )
                                                                    })
                                                                }
                                                            </select> */}
                                                                </div>
                                                                <Button
                                                                    variant="light"
                                                                    className='me-2 mt-2'
                                                                    onClick={handleSubmit}
                                                                    sx={{ mt: 1, mr: 1 }}
                                                                >
                                                                    Approve
                                                                </Button>
                                                            </>

                                                        ) : (

                                                            <Button
                                                                variant="light"
                                                                className='me-2 mt-2'
                                                                onClick={handleNext}
                                                                sx={{ mt: 1, mr: 1 }}
                                                            >
                                                                Continue
                                                            </Button>
                                                        )}

                                                        <Button
                                                            disabled={index === 0}
                                                            className='mt-2'
                                                            onClick={handleBack}
                                                            sx={{ mt: 1, mr: 1 }}
                                                        >
                                                            Back
                                                        </Button>
                                                    </div>
                                                </Box>
                                            </StepContent>
                                        </Step>
                                    ))}
                                </Stepper>
                                {activeStep === steps.length && (
                                    <Paper square elevation={0} sx={{ p: 3 }}>
                                        <Typography>Submitted! Action Card will be disappeared from the list!</Typography>

                                    </Paper>
                                )}
                            </Box>
                        </div>
                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirMedicalApproveCard;