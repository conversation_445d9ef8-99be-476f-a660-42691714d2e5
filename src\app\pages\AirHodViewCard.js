import React, { useState, useEffect, useCallback } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import Step<PERSON>abel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";
import { AIR_MEDICAL_WITH_ID_URL, AIR_WITH_ID_URL, ALL_LOCATIONTHREE_LOCATIONFOUR_URL, PPES_URL, INJURY_URL, GENERAL_USER_URL, EQUIPMENT_CATEGORIES_URL, GHS_ONE_URL, GHS_TWO_URL, LIGHTING_URL, LOCATION3_URL, SURFACE_CONDITION_URL, SURFACE_TYPE_URL, TIER2_TIER3_URL, WEATHER_CONDITION_URL, WORKING_GROUP_URL, WORK_ACTIVITIES_URL, GET_USER_BY_ROLES_URL, AIR_HOD_REVIEW_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import { DropzoneArea } from 'material-ui-dropzone';
import { BodyComponent } from "reactjs-human-body";
import Select from "react-select";
import DatePicker from "react-datepicker";
import moment from 'moment';
import "react-datepicker/dist/react-datepicker.css";
import ActionCardNew from './ActionCardNew'

// Import required date-fns functions if needed for formatting
import { format } from 'date-fns';

const AirHodViewCard = ({ showModal, setShowModal, data }) => {


    function initializeDateWithOffset(date) {
        return moment(date).utcOffset('+0530');
    }
    const [supervisor, setSupervisor] = useState([])
    const [skillTrainer, setSkillTrainer] = useState([])
    const [hod, setHod] = useState([])
    const [generalUsers, setGeneralUsers] = useState([]);



    useEffect(() => {
        const fetchUsers = async () => {
            try {

                const supervisors = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'ir-supervisor' });
                setSupervisor(supervisors.data);

                const skillTrainers = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'ir-skill-trainer' });
                setSkillTrainer(skillTrainers.data);

                const hods = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'ir-hr' });
                setHod(hods.data);
            } catch (error) {
                console.error('Error fetching data', error);
            }
        };

        fetchUsers();
        // setSelectedReviewer(incidentData.incidentOwnerId)
        getGeneralUsers()



    }, [data])


    const getGeneralUsers = useCallback(async () => {
        const response = await API.get(GENERAL_USER_URL);
        if (response.status === 200) {
            setGeneralUsers(response.data);
        }
    }, []);





    const [witnessInvolved, setWitnessInvolved] = useState('')


    useEffect(() => {

        if (data && data.hodComments) {

            const updatedPersonInvolved = data.hodComments?.personInvolved?.filter(i => i).map(person => ({
                ...person,

            }));

            // Similarly for personnelImpacted
            const updatedPersonnelImpacted = data.hodComments?.personnelImpacted?.filter(i => i).map(person => ({
                ...person,

            }));


            setWitnessInvolved({ witnessInvolved: data.witnessInvolved, personnelImpacted: updatedPersonnelImpacted, personInvolved: updatedPersonInvolved })

        }
    }, [data]);




    const handleSubmit = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL

            const response = await API.patch(AIR_HOD_REVIEW_WITH_ID_URL(data.id, data.actionId), {
                hodComments: witnessInvolved,

            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };

    const displayText = (label, text) => (
        <div className="mb-3">
            <strong>{label}: </strong> {text || "N/A"}
        </div>
    );

    const handleMedicalOfficerSurveillanceChange = (index, value, type) => {
        setWitnessInvolved(prevState => {
            const updatedList = prevState[type].map((item, i) => {
                if (i === index) {
                    return { ...item, medicalOfficerSurveillance: value };
                }
                return item;
            });

            return { ...prevState, [type]: updatedList };
        });
    };

    const handleInputChange = (type, index, fieldName, value) => {
        setWitnessInvolved(prevState => {
            // Clone the array to avoid direct mutation
            const updatedArray = [...prevState[type]];

            // Clone the specific object to be updated
            const updatedObject = { ...updatedArray[index] };

            // Update the field in the object
            updatedObject[fieldName] = value;

            // Update the object in the array
            updatedArray[index] = updatedObject;

            // Return the updated state
            return { ...prevState, [type]: updatedArray };
        });
    };

    const renderPersonFields = (action, index, type) => {
        const isInternal = action.internal;
        const isPersonInjured = action.injured;

        const commonFields = (
            <>

                <Box>

                    <div className="row mt-4">
                        <div className="col-4">
                            <div className="form-group">
                                <label>If any actions from the immediate supervisor?</label>
                                <input className="form-control" type="text" name="supervisorAction"
                                    disabled
                                    value={action.supervisorAction}
                                    onChange={e => handleInputChange(type, index, 'supervisorAction', e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Due Date</label>
                                <input className="form-control" type="date" name="supervisorDueDate"
                                    disabled
                                    value={action.supervisorDueDate}
                                    onChange={e => handleInputChange(type, index, 'supervisorDueDate', e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Person Responsible</label>
                                <select className="form-select" value={action.supervisorResponsible}
                                    disabled
                                    onChange={e => handleInputChange(type, index, 'supervisorResponsible', e.target.value)}>
                                    <option value={''}>Choose</option>
                                    {
                                        supervisor.map(i => <option key={i.id} value={i.id}>{i.firstName}</option>)
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="row mt-4">
                        <div className="col-4">
                            <div className="form-group">
                                <label>Any training required?</label>
                                <input className="form-control" type="text" name="trainingNeed"
                                    value={action.trainingNeed}
                                    disabled
                                    onChange={e => handleInputChange(type, index, 'trainingNeed', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Due Date</label>
                                <input className="form-control" type="date" name="trainingDueDate"
                                    disabled
                                    value={action.trainingDueDate}
                                    onChange={e => handleInputChange(type, index, 'trainingDueDate', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Person Responsible</label>
                                <select className="form-select" value={action.trainingResponsible}
                                    disabled
                                    onChange={e => handleInputChange(type, index, 'trainingResponsible', e.target.value)}>
                                    <option value={''}>Choose</option>
                                    {
                                        skillTrainer.map(i => <option key={i.id} value={i.id}>{i.firstName}</option>)
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="row mt-4">
                        <div className="col-4">
                            <div className="form-group">
                                <label>Any HR actions to be Taken?</label>
                                <input className="form-control" type="text" name="hrAction"
                                    value={action.hrAction}
                                    disabled
                                    onChange={e => handleInputChange(type, index, 'hrAction', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">

                                <label>Due Date</label>
                                <input className="form-control" type="date" name="hrDueDate"
                                    value={action.hrDueDate}
                                    disabled
                                    onChange={e => handleInputChange(type, index, 'hrDueDate', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Person Responsible</label>
                                <select className="form-select" value={action.hrResponsible}
                                    disabled
                                    onChange={e => handleInputChange(type, index, 'hrResponsible', e.target.value)}>
                                    <option value={''}>Choose</option>
                                    {
                                        hod.map(i => <option key={i.id} value={i.id}>{i.firstName}</option>)
                                    }
                                </select>
                            </div>
                        </div>
                    </div>


                </Box>

            </>
        );

        if (isInternal) {

            // const selectedUser = generalUsers.length > 0 ? generalUsers.find(user => user.id === action?.selectedEmp?.id) : {};
            return (
                <div className="mb-4">
                    {displayText("Person Involved", action?.selectedEmp?.name ?? '')}
                    {commonFields}
                </div>
            );
        } else {
            return (
                <div className="mb-4">
                    {displayText("NIC", action.empId)}
                    {displayText("Name", action.name)}
                    {displayText("Remarks / Comments", action.comments)}
                    {commonFields}
                </div>
            );
        }
    };




    return (
        <>
            {data &&
                <div className="row">
                    {/* <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div> */}

                    <Form>


                        <div className="row">
                            <div>
                                <Form.Label> Person Involved </Form.Label>
                                {witnessInvolved.personInvolved && witnessInvolved.personInvolved.filter(i => i).map((action, index) =>
                                    <div className="form-group" key={index}>
                                        {renderPersonFields(action, index, "personInvolved")}
                                        <br />
                                    </div>
                                )}
                            </div>
                            <div>
                                <Form.Label> Personnel Injured </Form.Label>
                                {witnessInvolved.personnelImpacted && witnessInvolved.personnelImpacted.filter(i => i).map((action, index) =>
                                    <div className="form-group" key={index}>
                                        {renderPersonFields(action, index, "personnelImpacted")}
                                        <br />
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="row">
                            <div className="col-12">

                                <ActionCardNew application={'AIR'} id={data.id} type={['take_investigation_actions',
                                    'verify_actions',
                                    'retake_actions',
                                    'air_hod_review',
                                ]} />
                            </div>
                        </div>


                    </Form>
                </div>

            }
        </>
    )
}

export default AirHodViewCard;