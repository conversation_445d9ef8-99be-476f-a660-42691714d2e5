import $ from "jquery";
import React, { Component, createRef, useRef, useEffect } from "react";
import Swal from "sweetalert2";
import { useHistory } from 'react-router-dom';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;

// @ts-ignore
require("jquery-ui-sortable");
// @ts-ignore
require("formBuilder");

const FormBuilder = (props) => {
  const history = useHistory();

  const fb = useRef();

  useEffect(() => {
    const fields = [{
      label: 'Media Picker',
      attrs: {
        type: 'multimedia'
      },
      icon: '<i class="mdi mdi-filmstrip"></i>',

    }, {
      label: 'Signature',
      attrs: {
        type: 'sign'
      },
      icon: '<i class="mdi mdi-pen"></i>',

    }];
    const templates = {
      multimedia: function (fieldData) {
        return {
          field: '<span id="' + fieldData.name + '">',
          onRender: function () {
            $(document.getElementById(fieldData.name));
          }
        };
      },
      sign: function (fieldData) {
        return {
          field: '<span id="' + fieldData.name + '">',
          onRender: function () {
            $(document.getElementById(fieldData.name));
          }
        };
      }
    };
    const options = {
      fields,
      templates,
      stickyControls: {
        enable: true
      },
      controlPosition: 'left',
      disabledActionButtons: ['data'],
      actionButtons: [{
        id: 'go-back',
        className: 'btn btn-light',
        label: 'Back',
        type: 'button',
        events: {
          click: function () {
            history.push('/checklist')
          }
        }
      }],
      disabledAttrs: [
        'access',
        'className',

        'inline',



        'name',

        'other',


        'rows',
        'step',
        'style',
        'subtype',
        'toggle'

      ],
      disableFields: ['autocomplete', 'button', 'hidden', 'number',
        'select', 'starRating', 'text',
        'file'
      ],
      onSave: function (evt, formData) {

        props.onSubmit(formData);

      },
    };
    // @ts-ignore
    let formBuilderPlugin = $(fb.current).formBuilder(options);
    setTimeout(() => formBuilderPlugin.actions.setData(props.values), 500)

  }, [])

  return (
    <>

      <div id="fb-editor" ref={fb} />
    </>


  );
}


export default FormBuilder