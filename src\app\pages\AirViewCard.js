import React, { useState, useEffect } from "react";
import { <PERSON>dal, Button, Form } from 'react-bootstrap';
import GalleryPage from "../apps/Gallery";
import IncidentStory from "./IncidentStory";


const AirViewCard = ({ showModal, setShowModal, data, setData }) => {

    // const personsInvolved = data.personInvolved ? JSON.parse(data.personInvolved).map(person => person.name).join(', ') : null;
    // const witnesses = data.witnessInvolved ? JSON.parse(data.witnessInvolved).map(witness => witness.name).join(', ') : null;
    // const bodyParts = data.bodyPartInjured ? data.bodyPartInjured.map(part => `${part.slug} (intensity: ${part.intensity})`).join(', ') : null;
  
    return (
        <>
            {data && <Modal
                show={showModal}
                size="md"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    IR Information
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-12">
                           <IncidentStory data={data} />
                        </div>

                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirViewCard;