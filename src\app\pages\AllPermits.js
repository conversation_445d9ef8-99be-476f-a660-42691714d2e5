import React, { useState, useEffect } from "react";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import PermitModal from './PermitModal';
import { ALL_PERMITS_URL, PERMIT_REPORT_WITH_ID, STATIC_URL } from "../constants";
import API from "../services/API";
import moment from "moment";
import "primereact/resources/themes/saga-blue/theme.css";  // Choose your theme
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";

export const AllPermits = ({ status, data }) => {
    const [permits, setPermits] = useState([]);
    const [filterData, setFilterData] = useState([]);

    const [showReportModal, setShowReportModal] = useState(false);
    const [reportData, setReportData] = useState(null);

    useEffect(() => {
        getPermits();
    }, []);

    const getPermits = async () => {
        try {
            const response = await API.get(ALL_PERMITS_URL);
            if (response.status === 200) {
                const reversedData = response.data.reverse();
                setPermits(reversedData);
                setFilterData(reversedData);
            }
        } catch (error) {
            console.error("Error fetching permits:", error);
            // Optionally handle errors (e.g., show a toast notification or update an error state)
        }
    };


    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        const filteredData = permits.filter(item => {
            return (
                (locationOneId === '' || item.locationOneId === locationOneId) &&
                (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
                (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
                (locationFourId === '' || item.locationFourId === locationFourId)
            );
        });
        setFilterData(filteredData);
    };

    const viewObservationReport = async (id) => {
        const params = {
            include: [
                { relation: "locationOne" },
                { relation: "locationTwo" },
                { relation: "locationThree" },
                { relation: "locationFour" },
                { relation: "locationFive" },
                { relation: "locationSix" },
              
            ]
        };
        const response = await API.get(`${PERMIT_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

        if (response.status === 200) {
            const actionUploads = (response.data.permitReportActions && response.data.permitReportActions.length) ? response.data.permitReportActions.flatMap(obj => obj.uploads) : [];

            response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
                return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 };
            }) : [];

            setReportData(response.data);
            setShowReportModal(true);
        }
    };

    const renderLocation = (rowData) => {
        return `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''}`;
    };

    const renderClosureStatus = (rowData) => {
        return rowData.closure ? (rowData.closure.status ? rowData.closure.status : 'N/A') : 'N/A';
    };

    const renderCloseoutDate = (rowData) => {
        return rowData.closure && rowData.closure.closeoutDate
            ? moment(rowData.closure.closeoutDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A')
            : 'N/A';
    };

    const renderStatus = (rowData) => {
        return rowData.status ? rowData.status : 'N/A';
    };

    const actionBodyTemplate = (rowData) => {
        return (
            <Button
                icon="pi pi-eye"
                className="p-button-rounded p-button-info"
                tooltip="View Report"
                onClick={() => viewObservationReport(rowData.maskId)}
            />
        );
    };
    const maskIdBodyTemplate = (row) => {

        return (
            <div className='maskid' onClick={() => viewObservationReport(row.id)}>
                {row.maskId}
            </div>
        );

    }
    return (
        <>
            {/* Optional filter component here */}

            <DataTable
                value={filterData}
                paginator
                rows={20}
                // header="Permits List"
                responsiveLayout="scroll"
            >
                {/* Define columns */}
                <Column field="maskId" body={maskIdBodyTemplate} header="ID" sortable style={{ padding: '1.125rem 1.375rem' }}></Column>
                <Column field="level" header="Level" sortable style={{ padding: '1.125rem 1.375rem' }}></Column>
                <Column body={renderLocation} header="Location" style={{ padding: '1.125rem 1.375rem' }}></Column>
                <Column body={renderClosureStatus} header="Closure Status" style={{ padding: '1.125rem 1.375rem' }}></Column>
                <Column body={renderCloseoutDate} header="Closeout Date" style={{ padding: '1.125rem 1.375rem' }}></Column>
                <Column body={renderStatus} header="Status" style={{ padding: '1.125rem 1.375rem' }}></Column>

                {/* Actions Column */}
                {/* <Column body={actionBodyTemplate} header="Actions" style={{ textAlign: 'center', width: '150px' }}></Column> */}
            </DataTable>

            <PermitModal
                reportData={reportData}
                showReportModal={showReportModal}
                setShowReportModal={(status) => setShowReportModal(status)}
            />
        </>
    );
};

export default AllPermits;
