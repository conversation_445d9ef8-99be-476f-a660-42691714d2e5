import React, { useState } from 'react'
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import AppSwitch from '../pages/AppSwitch';
import Invest from './Invest';
import Investication from './Investication';


const customFontStyle = {
    fontFamily: 'Lato, sans-serif',
    display: "flex",
    alignItems: 'center',
    justifyContent: 'center'
};

function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && (
                <Box bgcolor={'#fff'}>
                    {children}
                </Box>
            )}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};
const Dashboard = () => {

    const [value, setValue] = useState('MY ACTIONS');

    const TABS = {
        ACTIONS: "MY ACTIONS",
        DASHBOARD: "DASHBOARD",
        ROUTINE: "ROUTINE",
        NONROUTINE: "NONROUTINE",
        HAZARD: "HAZARD",
        TOOLBOX: "TOOLBOX",

    };

    const handleChange = (event, newValue) => {

        setValue(newValue);
    };
    return (
        <>
            <AppSwitch value={{ label: 'Observation', value: 'observation' }} />

            <Tabs value={value} onChange={handleChange} aria-label="incident report table" className="risk">

                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        My Actions
                    </Typography>
                } value={TABS.ACTIONS} />

                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Details of Incident & Consequences
                    </Typography>
                } value={TABS.DASHBOARD} />

                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Investigation
                    </Typography>
                } value={TABS.ROUTINE} />

            </Tabs>

            <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
                <></>
            </CustomTabPanel>
            <CustomTabPanel value={value} tabValue={TABS.DASHBOARD}>
                <Invest />
            </CustomTabPanel>
            <CustomTabPanel value={value} tabValue={TABS.ROUTINE}>
                <Investication />
            </CustomTabPanel>

        </>
    )
}

export default Dashboard