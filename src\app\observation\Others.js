import React, { Component, useState, useEffect, useRef } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { OBSERVATION_REPORT_BY_OTHERS_URL, OBSERVATION_REPORT_URL, USERS_URL, OBSERVATION_REPORT_WITH_ID, STATIC_URL } from '../constants';
import { Button } from 'primereact/button';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import ObservationModal from '../pages/ObservationModal';
// import { observationColumns, tableOptions } from './TableColumns';
import CardOverlay from '../pages/CardOverlay';

import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';

import { Checkbox } from '@mui/material';
import moment from 'moment'
import { Badge } from 'react-bootstrap';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { Calendar } from 'primereact/calendar';

import * as XLSX from 'xlsx';
import { saveAs } from "file-saver";
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const Other = ({ obsdata }) => {
  console.log(obsdata)
  const dt = useRef()
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [showOverdue, setShowOverdue] = useState(false);
  const [project, setProject] = useState([])
  const [user, setUser] = useState([])
  const [users, setUsers] = useState([])
  const [filters, setFilters] = useState(null);
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const [dataFilter, setDataFilter] = useState([]);
  const history = useHistory();
  const thead = [
    'id',
    'Type',
    'Category',
    'Description',
    'Rectified Status',
    'Remarks',
    'Action Taken',
  ];
  const defaultMaterialTheme = createTheme();

  const [data, setData] = useState(obsdata);
  const [filterData, setFilterData] = useState(obsdata);
  const [assignee, setAssignee] = useState([])
  const [source, setSource] = useState([])
  const [depart, setDepart] = useState([])
  const [category, setCategory] = useState([{ name: 'Health', value: 'Health' }, { name: 'Safety', value: 'Safety' }, { name: 'Environment', value: 'Environment' }])
  useEffect(() => {
    const preprocessObservationData = () => {
      if (!obsdata) return;

      // Preprocess the data to add `obsSourceName`
      const preprocessedData = obsdata.map(item => {
        const remarksData = JSON.parse(item.remarks || '{}');
        return {
          ...item,
          obsSourceName: remarksData.obsSourceName || '' // Adding obsSourceName directly to data
        };
      });

      // Set the preprocessed data
      setData(preprocessedData);
      setFilterData(preprocessedData);

      // Initialize filters
      initFilters();
    };

    // Call external data fetching functions
    const fetchInitialData = async () => {
      await getAllUsers();   // Assuming this is an async call
      await getObservationData(); // Assuming this is also an async call
    };

    // Only run when obsdata exists
    if (obsdata) {
      fetchInitialData();
      preprocessObservationData();
    }

  }, [obsdata]);

  const getAllUsers = async () => {
    const response = await API.get(USERS_URL);
    setUsers(response.data)


  }
  function getName(id) {
    if (id) {
      const user = users.find(user => user.id === id)
      return id ? user?.firstName || '' : ''
    }
  }
 const getCloseActionDate = (item) => {

    if (item.status === 'At Risk - Closed' || item.status === 'Action Verified - Closed' || item.status === 'Reported & Closed') {
      if (item.actions) {
        const last = item.actions[item.actions.length - 1]
        console.log(last)

        return moment(last.createdDate).format('Do MMM YYYY')
      }

    } else {
      return ''
    }

  }
  const uploadOBS = (item) => {
      const prefixedUploads = item.map(upload => STATIC_URL + '/' + upload);
      return prefixedUploads.join(',')
    }
  function getDepartment(id) {
    if (id) {
      const user = users.find(user => user.id === id)
      return id ? user?.department || '' : ''
    }
  }
  const initFilters = () => {
    setFilters({
      global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
      category: { value: null, matchMode: FilterMatchMode.IN },
      type: { value: null, matchMode: FilterMatchMode.IN },
      severity: { value: null, matchMode: FilterMatchMode.IN },
      dueDate: { value: null, matchMode: FilterMatchMode.IN },
      color: { value: null, matchMode: FilterMatchMode.IN },
      'locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
      created: { value: null, matchMode: FilterMatchMode.CUSTOM },
      status: { value: null, matchMode: FilterMatchMode.IN },
      'submitted.firstName': { value: null, matchMode: FilterMatchMode.IN },
      actionOwnerId: { value: null, matchMode: FilterMatchMode.IN },
      'obsSourceName': { value: null, matchMode: FilterMatchMode.IN },
      'workActivityDepartment.name': { value: null, matchMode: FilterMatchMode.IN }
    })
    setGlobalFilterValue('');
  }

  const onDateSearch = () => {
    if (!startDate && !endDate) return; // No date filter if both are null

    const start = startDate ? moment(startDate).startOf('day') : null;
    const end = endDate ? moment(endDate).endOf('day') : null;

    const searchData = data.filter(item => {
      const itemDate = moment(item.created, ['DD-MM-YYYY HH:mm', 'Do MMM YYYY', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

      if (start && end) {
        return itemDate.isBetween(start, end, null, '[]');
      } else if (start) {
        return itemDate.isSameOrAfter(start);
      } else if (end) {
        return itemDate.isSameOrBefore(end);
      } else {
        return true;
      }
    });

    setFilterData(searchData);
  };
  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }

  const getObservationData = async () => {

    const obs = obsdata.map(item => {
      return { name: item.locationFour?.name || '-', value: item.locationFour?.name || '' }
    })
    setProject(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
    const user = obsdata.map(item => {
      return { name: item.submitted?.firstName || '-', value: item.submitted?.firstName || '' }
    })
    setUser(user.filter((ele, ind) => ind === user.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    const de = obsdata.map(item => {
      return { name: item.workActivityDepartment?.name || '-', value: item.workActivityDepartment?.name || '' }
    })
    setDepart(de.filter((ele, ind) => ind === de.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))


    const obs1 = obsdata.map(item => {
      return { name: getName(item.actionOwnerId), value: item.actionOwnerId || '-' }
    })
    setAssignee(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))


    const obs12 = obsdata.map(item => {
      // Parse the 'remarks' JSON string
      const remarksData = JSON.parse(item.remarks);

      // Extract obsSourceName and actionOwnerId
      return {
        name: remarksData.obsSourceName || '',
        value: remarksData.obsSourceName || ''
      };
    });

    // Filter to remove duplicates based on both value and name
    setSource(obs12.filter((ele, ind) =>
      ind === obs12.findIndex(elem => elem.value === ele.value && elem.name === ele.name)
    ));

    // const assign = obsdata.map(item => {
    //   return { name: getName(item.assignee) || '-', value: getName(item.assignee) || '' }
    // })
    // setAssignee(assign.filter((ele, ind) => ind === assign.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
    // const params = {
    //   "include": [{ "relation": "submitted" }]

    // };
    // const response = await API.get(`${OBSERVATION_REPORT_BY_OTHERS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    // if (response.status === 200) {

    //   const preprocessedData = response.data.map(item => ({
    //     ...item,
    //     'submitted.firstName': item.submitted ? item.submitted.firstName : '',
    //     'color': moment().isAfter(moment(item.dueDate, 'DD-MM-YYYY')) ? 'Overdue' : 'Upcoming',
    //     'created': moment(item.created).format('Do MMM YYYY hh:mm A') // Ensure this is a string
    //   }));
    //   setData(preprocessedData)
    //   setFilterData(preprocessedData)
    //   setTotalOtherObservation(preprocessedData.length)
    // }
    // await obsdata.map(item => {
    //   item.assignee = getName(item.assignee)
    // })
  }

  const [showReportModal, setShowReportModal] = useState(false);
  const [reportData, setReportData] = useState(null);

  const viewObservationReport = async (id) => {

    const params = {
      "include": [{ "relation": "actions" }, { "relation": "workActivityDepartment" }, { "relation": "ghsOne" }, { "relation": "ghsTwo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }]

    };
    const response = await API.get(`${OBSERVATION_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

    if (response.status === 200) {

      // const actionUploads = (response.data.actions && response.data.actions.length) ? response.data.actions.flatMap(obj => obj.uploads) : [];

      // response.data.uploads = [...response.data.uploads]

      // response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
      //   return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      // }) : []

      // response.data.evidences = response.data.evidences ? response.data.evidences.map(i => {
      //   return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      // }) : []

      // response.data.evidence = [...response.data.evidence, ...actionUploads]
      // response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
      //   return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      // }) : []
      setReportData(response.data)
      setShowReportModal(true)
    }

  }

const exportToExcel = () => {
    // 1) Check if filterData is valid
    if (!Array.isArray(filterData) || filterData.length === 0) {
      console.warn("No valid data to export.");
      return;
    }

    // 2) Use your filtered data
    let data = dataFilter.length !== 0 ? dataFilter : filterData;

    // 3) Build an array of plain objects to export
    let allExcelData = data.map((item, index) => {
      const remarksData = item.remarks ? JSON.parse(item.remarks) : {};
      return {
        "S.No": index + 1, // ✅ Serial Number
        Date: item.created,
        Team: item.category,
  
        // ✅ Fix: Correct string formatting for `Location`
        Location: `${remarksData.location3 || ''} ${remarksData.location4 || ''}`,
  
        "UnsafeCondition / UnsafeAct":
          remarksData.unsafeCondition ? "Unsafe Condition"
          : remarksData.unsafeAct ? "Unsafe Act"
          : "None",
  
        "Reported by (Department)": item.workActivityDepartment?.name || "",
        Observation: item.description,
        Severity: item.severity,
        Type: remarksData.obsSourceName || "",
  
        // ✅ Fix: Ensure `getDepartment()` and `getName()` exist, or provide default values
        "Responsible Department":
          typeof getDepartment === "function"
            ? getDepartment(item.actionOwnerId)
            : "N/A",
        "Responsible Owner":
          typeof getName === "function"
            ? getName(item.actionOwnerId)
            : "N/A",
  
        // ✅ Fix: Ensure `uploadOBS()` exists
        Evidence:
          typeof uploadOBS === "function"
            ? uploadOBS(item.uploads)
            : "",
  
        Status: item.newStatus,
  
        ClosedDate:
          typeof getCloseActionDate === "function"
            ? getCloseActionDate(item)
            : "",
      };
    });

    if (allExcelData.length === 0) {
      console.warn("No data to export.");
      return;
    }

    // 4) Convert data to a worksheet
    const ws = XLSX.utils.json_to_sheet(allExcelData);

    // 5) Create a new workbook and append the worksheet
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Observation Data");

    // 6) Write the workbook to an in-memory binary array
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });

    // 7) Create a Blob from that array
    const dataBlob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    // 8) Generate a timestamped file name
    const fileName = `Observation_Data_${moment().format("DD-MM-YYYY_HH-mm")}.xlsx`;

    // 9) Use FileSaver to trigger a download in the browser
    saveAs(dataBlob, fileName);
  };


  
  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };

  const tableActions = [
    {
      icon: 'visibility',
      tooltip: 'View Report',
      onClick: (event, rowData) => {
        // Do save operation

        viewObservationReport(rowData.id)
      }
    }
  ]
  const viewBodyTemplate = (row) => {
    return (
      <div className="table-action d-flex ">
        <i className="mdi mdi-eye" onClick={() => viewObservationReport(row.id)}></i>

      </div>
    )
  }
  const localization = {
    header: {
      actions: 'View'
    }
  };
  // useEffect(()=>{
  //   const filteredData = data.filter(item => {
  //     return (
  //       (locationOneId === '' || item.locationOneId === locationOneId) &&
  //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
  //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
  //       (locationFourId === '' || item.locationFourId === locationFourId)
  //     );
  //   });

  //   setFilterData(filteredData);
  //   setTotalOtherObservation(filterData.length)

  // },[locationOneId,locationTwoId,locationThreeId,locationFourId])


  // const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
  //   const filteredData = data.filter(item => {
  //     return (
  //       (locationOneId === '' || item.locationOneId === locationOneId) &&
  //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
  //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
  //       (locationFourId === '' || item.locationFourId === locationFourId)
  //     );
  //   });

  //   setFilterData(filteredData);
  // };
  useEffect(() => {
    let filteredData = data; // Assuming response.data is your fetched data

    if (showOverdue) {
      const currentDate = moment();
      filteredData = filteredData.filter(item => {
        return moment(item.dueDate, 'DD-MM-YYYY').isBefore(currentDate);
      });
    }

    setFilterData(filteredData);
  }, [showOverdue]);
  const exportCSV = () => {
    dt.current.exportCSV();
  };
  const renderHeader = () => {
    // const value = filters['global'] ? filters['global'].value : '';

    return (
      <div className='d-flex justify-content-between align-items-center'>
        <div className="">
          <span className='me-3'>Date Filter :</span>
          <Calendar className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="dd/mm/yy" showIcon />
          <Calendar className="w-full me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="dd/mm/yy" showIcon />

          <Button className='me-3' rounded text raised severity="success" aria-label="Search" label='Apply' onClick={() => onDateSearch()} />
          <Button rounded text raised severity="danger" aria-label="Cancel" label='Clear All Filter' onClick={() => { setFilterData(data); initFilters(); setStartDate(null); setEndDate(null) }} />

        </div>
        {/* <h5 className='m-0'> A listing of all observations reported on the platform for the selected location(s) and time frame.</h5> */}
        <span className="p-input-icon-left">
          <i className="fa fa-search" />
          <InputText type="search" value={globalFilterValue} onChange={(e) => onGlobalFilterChange(e)} />
        </span>
        <Button type="button" icon="pi pi-file-excel" severity="success" rounded onClick={exportToExcel} data-pr-tooltip="XLS" />
      </div>
    );
  };

  const header = renderHeader();
  const onGlobalFilterChange = (event) => {
    const value = event.target.value;
    let _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
    setGlobalFilterValue(value)
  };
  const maskIdBodyTemplate = (row) => {

    return (
      <div className='maskid' onClick={() => viewObservationReport(row.id)}>
        {row.maskId}
      </div>
    );

  }
  const dateBodyTemplate = (row) => {

    return (<>{row.created}</>)

  }
  const statusBodyTemplate = (rowData) => {
    return <Badge pill bg={getSeverity(rowData.color)} >{rowData.color}</Badge>
  };

  const categoryFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={category} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const typeFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={[{ name: 'Unsafe Act', value: 'Unsafe Act' }, { name: 'Unsafe Condition', value: 'Unsafe Condition' }, { name: 'Positive', value: 'Positive' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const currentStatusFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={[{ name: 'Under Review', value: 'Under Review' }, { name: 'Action Reassigned', value: 'Action Reassigned' }, { name: 'Action Verified - Closed', value: 'Action Verified - Closed' }, { name: 'Reported & Closed', value: 'Reported & Closed' }, { name: 'Reported & Rectified on Spot', value: 'Reported & Rectified on Spot' }, { name: 'Actions Assigned', value: 'Actions Assigned' }, { name: 'Archived without actions', value: 'Archived without actions' }, { name: 'Actions Taken - Pending Verification', value: 'Actions Taken - Pending Verification' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const sourceFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={source} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const representativesItemTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.value}</span>
      </div>
    );
  };

  const dueDateTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.dueDate
          ? moment(option.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
          : '-'}</span>
      </div>
    );
  };

  const statusFilterTemplate = (options) => {
    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={statuses} itemTemplate={statusItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    )
    // <Dropdown value={options.value} options={statuses} onChange={(e) => options.filterCallback(e.value, options.index)} itemTemplate={statusItemTemplate} placeholder="Select One" className="p-column-filter" showClear />;
  };
  const statusItemTemplate = (option) => {
    return <Badge pill bg={getSeverity(option.value)}>{option.name}</Badge>
  };
  const statuses = [{ name: 'Overdue', value: 'Overdue' }, { name: 'Upcoming', value: 'Upcoming' }, { name: 'Due Soon', value: 'Due Soon' }, { name: 'None', value: 'None' }];
  const getSeverity = (status) => {
    switch (status) {
      case 'Overdue':
        return 'danger';

      case 'Upcoming':
        return 'info';

      case 'Due Soon':
        return 'warning';


      case 'None':
        return null;
    }
  };
  const reportFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={user} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const assigneeFilterTemplate = (options) => {
    return <>
      <MultiSelect value={options.value} options={assignee} itemTemplate={assigneeItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
    </>
  }

  const assigneeItemTemplate = (option) => {
    return getName(option.value)
  }
  const severityFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={[{ name: 'Medium', value: 'Medium' }, { name: 'Low', value: 'Low' }, { name: 'High', value: 'High' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const departmentFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={depart} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const assigneeTemplate = (row) => {
    return getName(row.actionOwnerId)
  }

  const sourceBodyTemplate = (row) => {
    // Parse the 'remarks' JSON string
    const remarksData = JSON.parse(row.remarks);

    // Return the obsSourceName
    return remarksData.obsSourceName || '';
  };


  return (
    <>

      {/* <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} /> */}
      {/* <Checkbox
        checked={showOverdue}
        onChange={e => setShowOverdue(e.target.checked)}
        color="primary"
      />
      Show Overdue */}
      {/* <ThemeProvider theme={defaultMaterialTheme}>
        <MaterialTable
          columns={observationColumns}
          data={filterData}
          title="EHS Observation Reported By Others"
          style={tableStyle}
          actions={tableActions}
          options={{ pageSize: 20, exportButton: true, ...tableOptions }}
          localization={localization}
        />
      </ThemeProvider> */}
      <DataTable ref={dt} value={filterData} paginator rows={10} onValueChange={filteredData => setDataFilter(filteredData)} header={header} filters={filters} globalFilterFields={["maskId"]} onFilter={(e) => { console.log(e); setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        rowsPerPageOptions={[10, 25, 50]}
        emptyMessage="No Data found." >

        {/* <Column body={viewBodyTemplate} header="Action" ></Column> */}
        <Column field='color' body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field='maskId' body={maskIdBodyTemplate} header="Observation ID" headerStyle={{ width: '15%' }}></Column>

        <Column field='created' body={dateBodyTemplate} header="Reported On" sortable headerStyle={{ width: '16%' }}></Column>

        {/* <Column field="submitted.firstName" header="Reported By" sortable  ></Column> */}

        <Column field="category" header="Domain" sortable filter filterElement={categoryFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="type" header="Category" sortable filter filterElement={typeFilterTemplate} showFilterMatchModes={false}></Column>

        {/* <Column field="description" header="Brief Description" sortable  ></Column> */}

        <Column field="status" header="Current Status" filter filterElement={currentStatusFilterTemplate} showFilterMatchModes={false}></Column>
        <Column field='obsSourceName' header="Source" filter filterElement={sourceFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="submitted.firstName" header="Reported By" filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="actionOwnerId" header="ActionAssignee" body={assigneeTemplate} filter filterElement={assigneeFilterTemplate} showFilterMatchModes={false}></Column>

        {/* <Column field="status" header="Status" sortable  ></Column>  */}

        {/* <Column field="locationFour.name" header="Project/DC name" filter filterElement={projectFilterTemplate} showFilterMatchModes={false}></Column> */}

        <Column field="severity" header="Severity" filter filterElement={severityFilterTemplate} showFilterMatchModes={false}></Column>
        <Column field="workActivityDepartment.name" header="Department" filter filterElement={departmentFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="dueDate" body={dueDateTemplate} header="Action Due" sortable></Column>

        <Column field="closeDate" header="Action Closed" sortable ></Column>

      </DataTable>

      <ObservationModal reportData1={reportData} showReportModal={showReportModal} setShowReportModal={(status) => setShowReportModal(status)} />



    </>
  )
}

export default Other;
