import React, { Component } from "react";
import * as _ from "lodash";
import JoditEditor from "jodit-react";
import { <PERSON><PERSON>, Button, Accordion } from "react-bootstrap";
class Bank extends Component {
    constructor(props) {
        super(props);
        this.state = {
            tab: "",
            toolType: "BANK",
            title: "",
            question: [{ title: '', radios: [], multiple: false }],
            defaultValue: "",
            validation: {
                isRequired: false,
            },
            display: 0,
            slider: 0,
            duplicate: false,
            mdShow: false,
            isEmpty: false
        };
        this.config = {
            askBeforePasteHTML: false,
            askBeforePasteFromWord: false,
            defaultActionOnPaste: "insert_clear_html",
            toolbarAdaptive: false,
            toolbarButtonSize: "large",
            toolbarSticky: false,
            buttons: "|,bold,underline,italic,|,font,fontsize,|,superscript,subscript,|,ul,ol,|,outdent,indent,|,align,paste,image,|",
            enableDragAndDropFileToEditor: true,
            uploader: {
                url: 'https://app.acuizen.com/Webservice/uploadEditorImage',
                format: 'json',
                pathVariableName: 'path',
                // filesVariableName: function (e) {
                //   return "images";
                // },
                // filesVariableName: 'images',
                prepareData: function (data) {
                    console.log(data);
                    return data;
                },
                isSuccess: function (resp) {
                    return !resp.error;
                },
                getMsg: function (resp) {
                    return resp.msg.join !== undefined ? resp.msg.join(' ') : resp.msg;
                },
                process: function (resp) {
                    console.log(resp);
                    return {
                        files: resp['images'] || [],
                        path: resp.path,
                        baseurl: resp.baseurl,
                        error: resp.error,
                        msg: resp.msg
                    };
                },
                error: function (e) {
                    console.log(e)
                    this.events.fire('errorPopap', [e.getMessage(), 'error', 4000]);
                },
                defaultHandlerSuccess: function (data, resp) {
                    console.log(data);
                    console.log(resp);
                    var i, field = 'files';
                    if (data[field] && data[field].length) {
                        console.log(data[field]);
                        for (i = 0; i < data[field].length; i += 1) {
                            this.selection.insertImage(data.baseurl + data[field][i]);
                        }
                    }
                },
                defaultHandlerError: function (resp) {
                    this.events.fire('errorPopap', [this.options.uploader.getMsg(resp)]);
                }
            },
        };
        this.removeOption = this.removeOption.bind(this);
    }

    componentWillMount() {
        this.setState(this.props.field);
    }

    changeValue(stateFor, i, value) {
        switch (stateFor) {
            case "NAME":
                this.setState({ name: value });
                break;
            case "DISPLAY":
                this.setState({ display: value });
                break;
            case "TITLE":
                this.setState({ title: value });
                break;
            case "DESCRIPTION":
                this.setState({ description: value });
                break;
            case "DEFAULT_VALUE":
                this.setState({ defaultValue: value });
                break;
            case "IS_REQUIRED":
                this.setState({
                    validation: { ...this.state.validation, isRequired: value },
                });
                break;
            case "IS_READONLY":
                this.setState({
                    validation: { ...this.state.validation, isReadOnly: value },
                });
                break;
            case "MAX":
                this.setState({ validation: { ...this.state.validation, max: value } });
                break;
            case "MIN":
                this.setState({ validation: { ...this.state.validation, min: value } });
                break;
            case "INLINE":
                this.setState({ inline: value });
                break;
            case "MULTIPLE":
                let radios = this.state.question;
                radios[i].radios.forEach((ele) => {
                    return (ele.selected = false);
                });
                radios[i].multiple = value
                this.setState({
                    question: radios
                });
                break;
            default:
                return;
        }
        setTimeout(() => {
            return this.props.changeState(this.state, this.props.index);
        }, 0);
    }

    removeOption(i, index) {
        // let radios = this.state.radios;
        // radios.splice(index, 1);
        // this.setState({
        //     radios: radios,
        // });
        // this.duplicates();


        const q = this.state.question


        const ques = q.map((item, ii) => {
            if (i === ii) {
                item.radios.splice(index, 1);

            }
            return item
        })

        this.setState({
            question: ques,
        });
        setTimeout(() => {
            return this.props.changeState(this.state, this.props.index);
        }, 0);
    }

    render() {
        console.log(this.props)
        return (
            <div
                className="card mb-3"
                style={this.state.title === '' && this.props.field.isEmpty ? { boxShadow: '0px 0px 12px 3px #dfdfdf', border: '1px solid red', borderRadius: 0 } : { boxShadow: '0px 0px 12px 3px #dfdfdf', borderRadius: 0 }}
            >
                <div className="card-header d-flex justify-content-between" >
                    <div> <i className="fa fa-question-circle-o mr-1"></i> Question Bank</div>

                    <div className="">
                        {this.props.index !== 0 ?
                            <span
                                className="" style={{ paddingRight: 5 }}
                                onClick={() => this.props.moveUp(this.props.index)}
                            >
                                <i className="mdi mdi-arrow-up"></i>
                            </span>
                            : ""}
                        {this.props.index !== this.props.length ?
                            <span
                                className="" style={{ paddingRight: 5 }}
                                onClick={() => this.props.moveDown(this.props.index)}
                            >
                                <i className="mdi mdi-arrow-down"></i>
                            </span>
                            : ''}
                        <span
                            className=""
                            onClick={() => this.props.removeField(this.props.index)}
                        >
                            <i className="mdi mdi-close"></i>
                        </span>
                    </div>
                </div>
                <div className="card-body">



                    <div className="row">
                        <div className="col-12 ">
                            <div className="form-group">
                                <label htmlFor="title">Title</label>
                                <textarea
                                    value={this.state.title}
                                    onChange={(e) =>
                                        this.changeValue("TITLE", 0, e.target.value)
                                    }
                                    className="form-control"
                                ></textarea>
                            </div>

                            <Button
                                variant="primary"
                                onClick={() => this.setState({ mdShow: true })}
                            >
                                Add Question
                            </Button>
                        </div>


                    </div>
                    <div className="row">
                        <div className="col-12">

                            <div className="form-check">
                                <input
                                    defaultChecked={this.state.validation.isRequired}
                                    onChange={(e) =>
                                        this.changeValue("IS_REQUIRED", '0', e.target.checked)
                                    }
                                    className="form-check-input"
                                    type="checkbox"
                                    id="isRequired"
                                />
                                <label className="form-check-label" htmlFor="isRequired">
                                    Required
                                </label>
                            </div>

                            <div className="form-group">
                                <label className="form-check-label" htmlFor="isRequired">
                                    Display Question Count
                                </label>
                                <input
                                    value={this.state.display}
                                    onChange={(e) =>
                                        this.changeValue("DISPLAY", 0, e.target.value)
                                    }
                                    className="form-control"
                                    type="text"

                                />

                            </div>
                        </div>
                    </div>

                    {this.state.question.length !== 0 ?
                        this.state.question.map((item, i) => {
                            console.log(item)
                            return (

                                <p
                                    dangerouslySetInnerHTML={{ __html: item.value }}
                                />
                            )
                        })


                        : ''}





                </div>
                <Modal
                    show={this.state.mdShow}
                    onHide={() => this.setState({ mdShow: false })}
                    size="lg"
                    aria-labelledby="example-modal-sizes-title-md"
                >
                    <Modal.Body>
                        <Accordion>
                            {this.state.question.map((item, i) => {

                                return (
                                    <Accordion.Item
                                        eventKey={i}
                                        className="mt-3"
                                        style={{
                                            boxShadow: "0px 1px 15px 3px #bcbfc452",
                                            position: "relative",
                                        }}>
                                        <Accordion.Header>
                                            {`Question - ${i + 1}`}
                                        </Accordion.Header>
                                        <Accordion.Body>
                                            <div className="">
                                                <JoditEditor
                                                    ref={(l) => (this.tooList = l)}
                                                    value={item.title}
                                                    config={this.config}
                                                    onBlur={(newContent) => item.title = newContent}
                                                    onChange={(newContent) => item.title = newContent}
                                                    style={{ width: '100%' }}
                                                />
                                            </div>
                                            <div className="form-check">
                                                <input
                                                    defaultChecked={item.multiple}
                                                    onChange={(e) => this.changeValue('MULTIPLE', i, e.target.checked)

                                                    }
                                                    className="form-check-input"
                                                    type="checkbox"
                                                    id="multiple"
                                                />
                                                <label className="form-check-label" htmlFor="isRequired">
                                                    Multiple Selection
                                                </label>
                                            </div>

                                            <div className="row">

                                                {item.radios ? (
                                                    <table className="table text-center">
                                                        <tbody>
                                                            {item.radios.map((checkbox, index) => {
                                                                return (
                                                                    <tr key={index}>
                                                                        {item.multiple ? (
                                                                            <td style={{ verticalAlign: "middle" }}>
                                                                                <div className="radio">
                                                                                    {

                                                                                        <div className="form-check">
                                                                                            <label className="form-check-label text-muted">
                                                                                                <input
                                                                                                    onChange={(e) =>
                                                                                                        this.changeOptionValue(
                                                                                                            i,
                                                                                                            index,
                                                                                                            e.target.checked,
                                                                                                            "SELECTED"
                                                                                                        )
                                                                                                    }

                                                                                                    type="checkbox"

                                                                                                    className="form-check-input"

                                                                                                    checked={
                                                                                                        item.radios[index].selected
                                                                                                    }
                                                                                                />
                                                                                                <i className="input-helper"></i>

                                                                                            </label>
                                                                                        </div>

                                                                                    }
                                                                                </div>
                                                                            </td>
                                                                        ) : (
                                                                            <td hidden={true}></td>
                                                                        )}

                                                                        <td>
                                                                            <input
                                                                                placeholder="Value"
                                                                                value={item.radios[index].value}
                                                                                onChange={(e) =>
                                                                                    this.changeOptionValue(
                                                                                        i,
                                                                                        index,
                                                                                        e.target.value,
                                                                                        "VALUE"
                                                                                    )
                                                                                }

                                                                                id={checkbox.value}
                                                                                type="text"
                                                                                className="form-control"
                                                                            />
                                                                        </td>
                                                                        {!item.multiple ? (
                                                                            <td style={{ verticalAlign: "middle" }}>
                                                                                <div className="form-check">
                                                                                    <label className="form-check-label text-muted">
                                                                                        <input
                                                                                            onChange={(e) =>
                                                                                                this.changeOptionValue(
                                                                                                    i,
                                                                                                    index,
                                                                                                    e.target.checked,
                                                                                                    "SELECTED"
                                                                                                )
                                                                                            }

                                                                                            type="radio"
                                                                                            id={checkbox.value}
                                                                                            className="form-check-input"

                                                                                            checked={
                                                                                                item.radios[index].selected
                                                                                            }
                                                                                        />
                                                                                        <i className="input-helper"></i>

                                                                                    </label>
                                                                                </div>


                                                                            </td>
                                                                        ) : (
                                                                            <td hidden={true}></td>
                                                                        )}
                                                                        <td style={{ verticalAlign: "middle" }}>
                                                                            <i
                                                                                onClick={() => this.removeOption(i, index)}
                                                                                className="mdi mdi-close pull-right"
                                                                            >

                                                                            </i>
                                                                        </td>
                                                                    </tr>
                                                                );
                                                            })}
                                                        </tbody>
                                                    </table>
                                                ) : (
                                                    <span></span>
                                                )}
                                                <button
                                                    onClick={() => this.addOption(i)}
                                                    className="btn form-control btn-sm btn-dark"
                                                >
                                                    Add Option
                                                </button>
                                            </div>


                                        </Accordion.Body>
                                    </Accordion.Item>


                                )
                            })}
                            <div className="d-flex justify-content-center">
                                <button
                                    onClick={() => this.addQuestion()}
                                    className="btn btn-primary "
                                >
                                    Add Question
                                </button>
                            </div>
                        </Accordion>

                        {/* <JoditEditor
              ref={(l) => (this.tooList = l)}
              value={this.state.title}
              config={this.config}
              onBlur={(newContent) => this.changeValue("TITLE", newContent)}
              onChange={(newContent) => this.changeValue("TITLE", newContent)}
            /> */}
                    </Modal.Body>

                    <Modal.Footer className="flex-wrap">
                        <>
                            <Button
                                variant="light"
                                onClick={() => this.setState({ mdShow: false })}
                            >
                                Cancel
                            </Button>
                            <Button
                                variant="primary"
                                onClick={() => this.setState({ mdShow: false })}
                            >
                                Create
                            </Button>
                        </>
                    </Modal.Footer>
                </Modal>
            </div>
        );
    }
    addQuestion() {
        let radios = this.state.question;

        radios.push({ title: '', radios: [], multiple: false })

        this.setState({ question: radios })


    }

    changeOptionValue(i, index, value, state) {
        let radios = this.state.question;
        console.log(radios)


        // if (state === "DEFAULT_VALUE") {
        //     this.setState({
        //         defaultValue: index,
        //     });
        // }
        if (state === "SELECTED") {
            console.log(radios[i])
            if (radios[i].multiple) {

                radios[i].radios[index].selected = !radios[i].radios[index].selected


            } else {
                radios[i].radios.forEach((ele) => {
                    return (ele.selected = false);
                });

                radios[i].radios[index].selected = !radios[i].radios[index].selected


            }
        } else if (state === "VALUE") {


            radios[i].radios[index].value = value


        } else {
            // radio = {
            //     ...radios[index],
            // };
        }



        this.setState({
            question: radios,
        });
        this.duplicates();
        setTimeout(() => {
            return this.props.changeState(this.state, this.props.index);
        }, 0);
    }

    duplicates() {
        let radios = this.state.radios;
        let u = _.uniqBy(radios, "value");
        if (!_.isEqual(radios, u)) {
            this.setState({
                duplicate: true,
            });
        } else {
            this.setState({
                duplicate: false,
            });
        }
    }

    addOption(i) {
        const q = this.state.question
        let radio = {
            value: "",
            selected: false,
            u_select: false
        };

        const ques = q.map((item, ii) => {
            if (i === ii) {
                item.radios.push(radio)


            }
            return item
        })
        // console.log(item)
        // console.log(i)

        // let radios = item.option;
        // radios.push(radio);
        this.setState({
            question: ques,
        });
        this.duplicates();
        setTimeout(() => {
            return this.props.changeState(this.state, this.props.index);
        }, 0);
    }
}

export default Bank;
