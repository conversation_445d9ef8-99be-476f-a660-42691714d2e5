import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';

import API from "../services/API";
import { DropzoneArea } from 'material-ui-dropzone';

import { AIR_COST_REVIEWER_URL, AIR_COST_REVIEWER_WITH_ID_URL, AIR_DUTY_MANAGER_ESTIMATION_WITH_ID_URL, AIR_FINANCER_WITH_ID_URL, AIR_FINANCE_URL, API_URL, GET_USER_ROLE_BY_MODE, STATIC_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';
import Switch from "react-switch";
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import Grid from '@mui/material/Grid'; // Import Grid
import FormControl from '@mui/material/FormControl'; // Import FormControl
import FormLabel from '@mui/material/FormLabel'; // Import FormLabel
import RadioGroup from '@mui/material/RadioGroup'; // Import RadioGroup
import FormControlLabel from '@mui/material/FormControlLabel'; // Import FormControlLabel
import Radio from '@mui/material/Radio'; // Import Radio
import TextField from '@mui/material/TextField'; // Import TextField
import InvoiceComponent from "./InvoiceComponent";
import Typography from '@mui/material/Typography';
import axios from "axios";
import { InputNumber } from 'primereact/inputnumber';
import ThirdPartyForm from "./ThirdPartyForm";

const AirFinancerCard = ({ showModal, setShowModal, data, setData }) => {


    const [showLeftPane, setShowLeftPane] = useState(true);

    const [combinedInvoiceItems, setCombinedInvoiceItems] = useState([]);
    const [damagedData, setDamagedData] = useState({})
    const handleUpdateApiData = (updatedApiData) => {
        setApiData(updatedApiData);
    };
    const handleAddCombinedInvoiceItem = (item, title) => {
        setCombinedInvoiceItems([...combinedInvoiceItems, { ...item, title }]);
    };

    const [apiData, setApiData] = useState(data.costActual);
    const TotalCostCalculator = (data) => {
        // Assuming data.ActualCost is the array similar to the one you've provided

        // Function to calculate total for each costEstimation
        const calculateTotalForCostEstimation = (costEstimation) => {
            return costEstimation?.reduce((total, current) => {
                // Calculate the total for each item inside the "items" array
                const itemsTotal = current.items.reduce((itemTotal, item) => {
                    return itemTotal + (parseFloat(item.price) * parseFloat(item.quantity));
                }, 0);

                // Accumulate the total for this costEstimation category
                return total + itemsTotal;
            }, 0);
        };

        // Calculate the grand total for all items
        const grandTotal = data.reduce((total, currentItem) => {
            // Calculate the total for the costEstimation of the current item
            const itemTotal = calculateTotalForCostEstimation(currentItem.costEstimation);

            // Accumulate the grand total
            return total + itemTotal;
        }, 0);
        console.log(grandTotal.toFixed(2))
        return grandTotal.toFixed(2);
    };
    const [totalIncidentCost, setTotalIncidentCost] = useState(0)
    useEffect(() => {
        setTotalIncidentCost(TotalCostCalculator(data.costActual))

    }, [data.costActual])


    const [isRecovery, setIsRecovery] = useState(false);
    const [isAcknowledged, setIsAcknowledged] = useState(false);

    const [percentageRecovery, setPercentageRecovery] = useState(0);
    const [insuranceRefNo, setInsuranceRefNo] = useState('');
    const [insuranceClaimAmount, setInsuranceClaimAmount] = useState(0.00);
    const [claimStatus, setClaimStatus] = useState('');
    const [claimPaymentRef, setClaimPaymentRef] = useState('');
    const [claimPaymentAmount, setClaimPaymentAmount] = useState(0.00);
    const [hod, setHod] = useState([]);
    const [selectedHod, setSelectedHod] = useState('')
    const [thirdParty, setThirdParty] = useState(null);
    const updateForms = (forms) => {
        setThirdParty(forms)
    }

    useEffect(() => {
        getHod();
    }, [])

    const getHod = async () => {
        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ir-hod'
        });
        if (response.status === 200) {


            setHod(response.data)

        }
    }
    const uploadFiles = async (files) => {
        const formData = new FormData();
        files.forEach(file => {
            formData.append('file', file);
        });
        const token = localStorage.getItem('access_token');
        try {
            const response = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });
            if (response.status === 200) {
                return response.data.files.map(file => file.originalname);  // Return file names
            } else {
                throw new Error('Failed to upload files');
            }
        } catch (error) {
            console.error('Error uploading files:', error);
            throw error;
        }
    };
    const handleSubmit = async () => {
        const uploadedFileNames = await uploadFiles(files);
        const parsedIncidentCost = parseFloat(totalIncidentCost);
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_FINANCER_WITH_ID_URL(data.id, data.actionId), {
                incidentCost: isNaN(parsedIncidentCost) ? 0 : parsedIncidentCost,
                isRecoveryFromOtherParty: isRecovery,
                percentageRecovery: parseFloat(percentageRecovery),
                isAcknowledgementRecevied: isAcknowledged,
                insuranceRefNo: insuranceRefNo,
                insuranceClaimAmount: insuranceClaimAmount,
                claimStatus: claimStatus,
                claimPaymentRef: claimPaymentRef,
                claimPaymentAmount: claimPaymentAmount,
                hodCheckId: selectedHod,
                claimsCoordinatorFiles: uploadedFileNames,
                thirdPartyForm: thirdParty

            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };
    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {
        if (data.thirdPartyForm !== undefined) {
            setThirdParty(data.thirdPartyForm);
        }

        if (data.isRecoveryFromOtherParty !== undefined) {
            setIsRecovery(data.isRecoveryFromOtherParty);
        }

        if (data.percentageRecovery !== undefined) {
            setPercentageRecovery(data.percentageRecovery);
        }

        if (data.isAcknowledgementReceived !== undefined) {
            setIsAcknowledged(data.isAcknowledgementReceived);
        }

        if (data.insuranceRefNo !== undefined) {
            setInsuranceRefNo(data.insuranceRefNo);
        }

        if (data.insuranceClaimAmount !== undefined) {
            setInsuranceClaimAmount(data.insuranceClaimAmount);
        }

        if (data.claimStatus !== undefined) {
            setClaimStatus(data.claimStatus);
        }

        if (data.claimPaymentRef !== undefined) {
            setClaimPaymentRef(data.claimPaymentRef);
        }

        if (data.claimPaymentAmount !== undefined) {
            setClaimPaymentAmount(data.claimPaymentAmount);
        }
    }, [data]);

    const handleReturn = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL
            // const response = await API.patch(AIR_FINANCER_RETURN_WITH_ID_URL(data.id, data.actionId), {


            // }

            // );

            // If the patch request fails, no need to proceed further
            // if (response.status !== 204) {
            //     console.error('Failed to patch data. Status:', response.status);
            //     return;  // or handle this error appropriately
            // }

            cogoToast.success(`Action for IR ${data.maskId} Returned`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };

    useEffect(() => {
        if (data?.costActual) {


            setDamagedData(data.costActual)
        }
    }, [data]);
    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0">IR Information</h2>
                        <div className="text-center mt-0">
                            <Button variant="primary" onClick={() => setShowLeftPane(!showLeftPane)}>
                                {showLeftPane ? "Hide Left Pane" : "Show Left Pane"}
                            </Button>
                        </div>
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">


                        <div className={`animate-col-md-6 col-md-6 ${showLeftPane ? "" : "animate d-none d-none"}`}>
                            <IncidentStory data={data} />
                        </div>
                        <div className={`col-md-6 ${showLeftPane ? "col-md-8-left-padding" : "col-md-12"}`}>
                            <Box>

                                <div className="row">
                                    <div className="table-responsive">
                                        <label>Actual Cost Estimated</label>

                                        <div className="col-md-12">
                                            <div className="row">
                                                {damagedData?.length > 0 && (
                                                    damagedData.map((item, index) => (
                                                        <Accordion key={index} TransitionProps={{ unmountOnExit: true }} className="border mb-3">
                                                            <AccordionSummary
                                                                expandIcon={<ExpandMoreIcon />}
                                                                aria-controls={`panel${index}a-content`}
                                                                id={`panel${index}a-header`}

                                                            >
                                                                <Typography><strong>Equipment #{index + 1}:</strong> {item.category}</Typography>
                                                                <Typography style={{ marginLeft: 'auto', opacity: 0.7 }}>Click to expand</Typography>
                                                            </AccordionSummary>
                                                            <AccordionDetails>
                                                                <Box sx={{ width: '100%' }}>
                                                                    <Typography variant="body2" paragraph>
                                                                        Detailed information for <strong>{item.category}</strong>:
                                                                    </Typography>
                                                                    <Typography variant="body2" paragraph>
                                                                        Uploaded Documents:

                                                                        {item?.files?.map(i => {
                                                                            return (
                                                                                <div>
                                                                                    <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                                                </div>
                                                                            )
                                                                        })}

                                                                    </Typography>
                                                                    <Grid container spacing={2}>
                                                                        <Grid item xs={6} sm={3}>
                                                                            <Typography><strong>Number:</strong> {item.number}</Typography>
                                                                        </Grid>
                                                                        <Grid item xs={6} sm={3}>
                                                                            <Typography><strong>Damage Type:</strong> {item.damageType}</Typography>
                                                                        </Grid>
                                                                        <Grid item xs={12} sm={6}>
                                                                            <Typography><strong>Cost Details:</strong> {item.costDetails}</Typography>
                                                                        </Grid>
                                                                        <Grid item xs={6} sm={3}>
                                                                            <Typography><strong>Work Order Number:</strong> {item.workOrderNumber}</Typography>
                                                                        </Grid>
                                                                        <Grid item xs={6} sm={3}>
                                                                            <Typography><strong>Operational:</strong> {item.operational ? "Yes" : "No"}</Typography>
                                                                        </Grid>
                                                                        <Grid item xs={12} sm={6}>

                                                                        </Grid>
                                                                        <Grid item xs={6} sm={3}>
                                                                            <TextField
                                                                                label="Inspection Date"
                                                                                type="date"
                                                                                value={damagedData[index].inspectionDate}
                                                                                style={{ color: 'black' }}
                                                                                disabled={true}
                                                                                InputLabelProps={{ shrink: true }}
                                                                                fullWidth
                                                                            />
                                                                        </Grid>
                                                                        <Grid item xs={12} sm={9}>
                                                                            <TextField
                                                                                label="Inspection Remarks"
                                                                                type="text"
                                                                                value={damagedData[index].inspectionRemarks}
                                                                                style={{ color: 'black' }}
                                                                                disabled={true}
                                                                                variant="outlined"
                                                                                fullWidth
                                                                            />
                                                                        </Grid>
                                                                        <Grid item xs={12}>
                                                                            <InvoiceComponent editable={false} costData={damagedData[index].costEstimation} />
                                                                        </Grid>
                                                                    </Grid>
                                                                </Box>
                                                            </AccordionDetails>
                                                        </Accordion>
                                                    ))
                                                )}
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                {/* <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="">Percentage of Recovery </label>
                                            <input type='number' value={percentageRecovery} onChange={(e) => setPercentageRecovery(parseInt(e.target.value))} className='form-control' />
                                        </div>
                                    </div>
                                </div>

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group d-flex align-items-center'>
                                            <label htmlFor="" className='m-0 me-3'>Is Recovery From Other Party?</label>

                                            <Switch onChange={(value) => setIsRecovery(value)} checked={isRecovery} />
                                        </div>
                                    </div>
                                </div> */}
                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <Typography variant="body2" paragraph>
                                                Uploaded Documents By Cost Estimator:

                                                {data.costEstimationFiles?.map(i => {
                                                    return (
                                                        <div>
                                                            <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                        </div>
                                                    )
                                                })}

                                            </Typography>
                                        </div>
                                    </div>
                                </div>


                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <Typography variant="body2" paragraph>
                                                Uploaded Documents by Duty Engineer Manager:

                                                {data.dutyEngineerManagerFiles?.map(i => {
                                                    return (
                                                        <div>
                                                            <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                        </div>
                                                    )
                                                })}

                                            </Typography>
                                        </div>
                                    </div>
                                </div>
                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group d-flex align-items-center'>
                                            <label htmlFor="" className='m-0 me-3 font-lg'><strong>Total Incident Cost: {totalIncidentCost} LKR</strong></label>


                                        </div>
                                    </div>
                                </div>

                                {
                                    data.hodReturnComments && <div className='row'>
                                        <div className='col'>
                                            <div className='form-group d-flex align-items-center'>
                                                <label htmlFor="" className='m-0 me-3 font-lg'><strong>HOD Return Comments:</strong> {data.hodReturnComments} </label>


                                            </div>
                                        </div>
                                    </div>
                                }
                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group d-flex align-items-center'>
                                            <label htmlFor="" className='m-0 me-3'>Is Recovery From Other Party?</label>

                                            <Switch onChange={(value) => setIsRecovery(value)} checked={isRecovery} />
                                        </div>
                                    </div>
                                </div>

                                {
                                    isRecovery && <div className='row'>
                                        <label htmlFor="" className='m-0 me-3'>Recovery Parties</label>

                                        <ThirdPartyForm updateForms={updateForms} values={thirdParty} />




                                    </div>
                                }

                                {/* <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="insuranceRefNo">Percentage of Recovery</label>
                                            <InputNumber
                                                id="percentage"
                                                value={percentageRecovery}
                                                onValueChange={(e) => setPercentageRecovery(e.value)}

                                                min={0}


                                                suffix="%"

                                                tooltip="Enter percentage"
                                            />

                                        </div>
                                    </div>
                                </div> */}

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group d-flex align-items-center'>
                                            <label htmlFor="" className='m-0 me-3'>Is Acknowledgement Received?</label>

                                            <Switch onChange={(value) => setIsAcknowledged(value)} checked={isAcknowledged} />
                                        </div>
                                    </div>
                                </div>


                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="insuranceRefNo">Insurance Ref No</label>
                                            <input
                                                type='text'
                                                value={insuranceRefNo}
                                                onChange={(e) => setInsuranceRefNo(e.target.value)}
                                                className='form-control'
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="insuranceClaimAmount">Insurance Claim Amount</label>
                                            <InputNumber
                                                id="insuranceClaimAmount"
                                                value={insuranceClaimAmount}
                                                onValueChange={(e) => setInsuranceClaimAmount(parseInt(e.value))}
                                                mode="decimal"
                                                min={0}
                                                className='w-100'
                                                placeholder="0.00"
                                                locale="en-US"
                                                minFractionDigits={2}
                                                maxFractionDigits={2}
                                            />

                                        </div>
                                    </div>
                                </div>

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="claimStatus">Claim Status</label>
                                            <input
                                                type='text'
                                                value={claimStatus}
                                                onChange={(e) => setClaimStatus(e.target.value)}
                                                className='form-control'
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="claimPaymentRef">Claim Payment Ref</label>
                                            <input
                                                type='text'
                                                value={claimPaymentRef}
                                                onChange={(e) => setClaimPaymentRef(e.target.value)}
                                                className='form-control'
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label htmlFor="claimPaymentAmount">Claim Payment Amount</label>
                                            <InputNumber

                                                id="claimPaymentAmount"
                                                value={claimPaymentAmount}
                                                onValueChange={(e) => setClaimPaymentAmount(parseInt(e.value))}
                                                mode="decimal"
                                                min={0}
                                                className='w-100'
                                                placeholder="0.00"
                                                locale="en-US"
                                                minFractionDigits={2}
                                                maxFractionDigits={2}
                                            />

                                        </div>
                                    </div>
                                </div>
                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <DropzoneArea
                                                acceptedFiles={[
                                                    'application/pdf',
                                                    'image/jpeg',
                                                    'image/png'

                                                ]}
                                                dropzoneText={"Drag and drop files / documents / pictures"}
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                onChange={handleFileChange}
                                            />
                                        </div>
                                    </div>
                                </div>

                                <p className="h5 mb-4">Assign HOD</p>
                                <select className='form-select' onChange={(e) => setSelectedHod(e.target.value)}>
                                    <option value={''}>
                                        Choose HOD
                                    </option>
                                    {
                                        hod.map(i => <option key={i.id} value={i.id}>{i.firstName}</option>)
                                    }
                                </select>

                                <div className="row">
                                    <div className="col-3">
                                        <Button variant="primary" onClick={handleSubmit}>
                                            Submit
                                        </Button>
                                    </div>

                                </div>
                            </Box>
                        </div>


                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirFinancerCard;