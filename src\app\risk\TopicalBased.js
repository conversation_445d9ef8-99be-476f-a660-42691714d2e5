import React, { useRef, useState, useEffect } from 'react'
import TopicalCuration from '../curation/TopicalCuration'
import CurrentControl from './components/CurrentControl'
import TeamDeclaration from './components/TeamDeclaration'
import SignatureCanvas from "react-signature-canvas";
import CreatableSelect from 'react-select/creatable';
import API from '../services/API';
import { GET_USER_ROLE_BY_MODE, RISKASSESSMENT_LIST,RISK_UPDATE_WITH_ID_URL ,RISK_WITH_ID_URL} from '../constants'
import { useSelector } from 'react-redux';
import S3 from "react-aws-s3";
import Swal from "sweetalert2";
import $ from "jquery";
import cogoToast from "cogo-toast";
import { useHistory } from "react-router-dom";
Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const config = {
    bucketName: "sagt",
    region: "ap-southeast-1",
    accessKeyId: "********************",
    secretAccessKey: "iuo0OyvIb5Fvwlq3t0uStZP9oUvmrLfggSj5YsOs",
};
function TopicalBased({id,name}) {
    const history = useHistory()

    const user = useSelector((state) => state.login.user);
    console.log(user)

    const signRef = useRef(null)
    const title = useRef(null)
    const [control, setControl] = useState({
        type: "textbox1",
        variant: "current_control",
        option: [
            { type: "textarea", label: "Current Control", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} },

        ],
        button: true,
    })
    const [crew, setCrew] = useState([])
    const [selectedMemberValue, setSelectedMemberValue] = useState([]);
    const [selectedAssignValue, setSelectedAssignValue] = useState({});
    const [selectedRecommendation, setselectedRecommendation] = useState({});
    const [selectSignMember, setSignMemberList] = useState([])
    const [sendShow, setSendShow] = useState(true)
    const [task, setTask] = useState([])
    const customSwal2 = Swal.mixin({
        customClass: {
            confirmButton: "btn btn-primary",
        },
        buttonsStyling: false,
    });
    const getCrewList = async () => {

        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ra-member'
        });
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {
                let department = item.ghsOne ? item.ghsOne.name : ''
                if (item.id !== user.id) {
                    data.push({ label: item.firstName, value: item.id, department: department });
                }
            });

            setCrew(data);
        }
    };

    useEffect(() => {
        getCrewList()

    }, [])

    const createUserHandler = async () => {

    }
    const onSave = async (data) => {
        let field = true

       
        if (data.length === 0) {
            field = false
        }
        if (field) {

            const response = await fetch(RISK_WITH_ID_URL(id), {
                method: "PATCH",
                body: JSON.stringify({
                  
                    status: "1",
                    topical: JSON.stringify(data)
                }),
                headers: { "Content-type": "application/json; charset=UTF-8" },
            });

            if (response.ok) {
                customSwal2.fire("Topical Created!", "", "success");
                history.go(0)
            } else {
                //show error
                customSwal2.fire("Please Try Again!", "", "error");

            }
        } else {
            customSwal2.fire("Please fill all the required fields", "", "error");
        }
    }
    return (
        <div>
            <div className="row">
                <div className="col-12">
                    <div className="card">
                        {/* <div className="row">
                            <div className="col-12">
                                <div className="form-group required">
                                    <label htmlFor="user_name">Title</label>
                                    <textarea
                                        className="form-control"
                                        ref={title}
                                        id="user_name"
                                        placeholder={
                                            ""
                                        }
                                    ></textarea>

                                </div>
                            </div>



                        </div> */}

                        <TopicalCuration onSave={onSave} data={[]} />





                    </div>
                </div>
            </div>
        </div>
    )
}

export default TopicalBased