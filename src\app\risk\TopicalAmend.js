import React, { useRef, useState, useEffect } from 'react'
import TopicalCuration from '../curation/TopicalCuration'
import CurrentControl from './components/CurrentControl'
import TeamDeclaration from './components/TeamDeclaration'
import SignatureCanvas from "react-signature-canvas";
import CreatableSelect from 'react-select/creatable';
import API from '../services/API';
import { GET_USER_ROLE_BY_MODE, RISKASSESSMENT_LIST, RISK_WITH_ID_URL } from '../constants'
import { useSelector } from 'react-redux';
import S3 from "react-aws-s3";
import Swal from "sweetalert2";
import $ from "jquery";
import cogoToast from "cogo-toast";
import { useHistory } from "react-router-dom";
Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const config = {
    bucketName: "sagt",
    region: "ap-southeast-1",
    accessKeyId: "********************",
    secretAccessKey: "iuo0OyvIb5Fvwlq3t0uStZP9oUvmrLfggSj5YsOs",
};
function TopicalAmend({ id }) {
    const history = useHistory()

    const user = useSelector((state) => state.login.user);
    console.log(user)

    const signRef = useRef(null)
    const title = useRef(null)
    const [control, setControl] = useState({
        type: "textbox1",
        variant: "current_control",
        option: [
            { type: "textarea", label: "Current Control", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} },

        ],
        button: true,
    })
    const [crew, setCrew] = useState([])
    const [selectedMemberValue, setSelectedMemberValue] = useState([]);
    const [selectedAssignValue, setSelectedAssignValue] = useState({});
    const [selectedRecommendation, setselectedRecommendation] = useState({});
    const [selectSignMember, setSignMemberList] = useState([])
    const [sendShow, setSendShow] = useState(true)
    const [task, setTask] = useState([])
    const [meet, setMeet] = useState([])
    const customSwal2 = Swal.mixin({
        customClass: {
            confirmButton: "btn btn-primary",
        },
        buttonsStyling: false,
    });
    const getRisk = async () => {
        const response = await API.get(RISK_WITH_ID_URL(id));
        if (response.status === 200) {

            setMeet(response.data)


        }
    }

    useEffect(() => {
        getRisk()

    }, [id])

    // useEffect(() => {
    //     getRisk()

    // }, [meet])

   
    const onSave = async (data) => {

        const response = await fetch(RISK_WITH_ID_URL(id), {
            method: "PATCH",
            body: JSON.stringify({ 
                
                topical: JSON.stringify(data)
            }),
            headers: { "Content-type": "application/json; charset=UTF-8" },
        });

        if (response.ok) {
            customSwal2.fire("Topical Saved!", "", "success");
        
        } else {
            //show error
            customSwal2.fire("Please Try Again!", "", "error");

        }
    }
    if (meet.length === 0) {
        return (<>loading...</>)
    } else {
        return (
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                           
                       

                            <TopicalCuration onSave={onSave} data={JSON.parse(meet.topical)} />





                        </div>
                    </div>
                </div>
            </div>
        )
    }
}

export default TopicalAmend