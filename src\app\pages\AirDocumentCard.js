import React, { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import { STATIC_URL } from '../constants';

const AirDocumentCard = ({ type, data }) => {
    const [documents, setDocuments] = useState([]);

    useEffect(() => {
        switch (type) {
            case 'investigation': setDocuments(data.investigationDocuments); break;

            case 'receipts': setDocuments(data.receiptDocuments); break;
            case 'surveyor': setDocuments(data.surveyorDocuments); break;
        }
    }, [type, data])
    return (<>
        <Box>

            <div className="row mt-4">

                <p className="h5 mb-4">Documents:</p>
                <div className="form-group">
                    <label>{
                        (documents && documents.length > 0) ? documents.map(i => {
                            return (
                                <div>
                                    <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                </div>
                            )
                        }) : 'No Documents Uploaded'
                    }</label>
                </div>
            </div>
        </Box>
    </>)
}

export default AirDocumentCard;