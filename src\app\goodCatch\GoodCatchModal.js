import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { ACTION_OWNER_LIST, API_URL, GMS1_URL, OBS_ACTION_ARCHIVE, DYNAMIC_TITLES_URL, OBS_ACTION_ASSIGN, OBS_REVIEWER_ASSIGN, STATIC_URL, ACTION_REVIEWER_LIST, GET_USER_BY_ROLES_URL, GC_ACTION_ASSIGN, GC_ACTION_OWNER_SUMBIT } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import { RadioButton } from "primereact/radiobutton";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import { useSelector } from "react-redux";
import { useHistory } from 'react-router-dom';
import moment from "moment";
import Swal from "sweetalert2";
import Select from "react-select";
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});
const GoodCatchModal = ({ reportData1, showReportModal, setShowReportModal }) => {



    const [title, setTitle] = useState([])



    useEffect(() => {



        // getContractorActionOwners()


        getDynamicTitle()







    }, [])

    const getDynamicTitle = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);
        setTitle(response.data)

        // setUsers(response.data)

    }





    const displayTitle = (type) => {

        const locTitle = title.filter(item => item.title === type)

        return locTitle[0].altTitle

    }
    return (
        <>
            <Modal
                show={showReportModal}
                size="lg"
                onHide={() => setShowReportModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    {reportData1 && <>
                        <div className=" ">
                            <div className="d-flex justify-content-between">
                                <h2 className="mb-3 obs-head-title">Good Catch Report</h2>


                                {/* <Button type="button" label="Download Report" outlined icon="pi pi-download" onClick={generatePdf} /> */}

                            </div>

                            <div className="d-flex align-items-center mb-2">

                                <h3 className="mb-0 me-2 obs-id">{(reportData1 && reportData1.maskId) ? reportData1.maskId : ''}</h3> <span className={`badge badge-success`}>
                                    {reportData1.status}
                                </span>
                            </div>
                        </div>
                    </>}
                </Modal.Header>

                <Modal.Body>


                    {reportData1 && <div className="">
                        <div className="card">

                            <div className="card-body p-0">

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Reporter</p>
                                        <p className="obs-content">
                                            {reportData1?.reporter?.firstName || "N/A"}
                                        </p>
                                    </div>
                                    {reportData1?.actionOwner &&
                                        <div className="col-md-6">
                                            <p className="obs-title">Admin</p>
                                            <p className="obs-content">
                                                {reportData1?.actionOwner?.firstName || "N/A"}
                                            </p>
                                        </div>
                                    }
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">{displayTitle("LocationThree")}</p>
                                        <p className="obs-content">
                                            {reportData1.locationThree
                                                ? reportData1.locationThree.name
                                                : "Not Available"}
                                        </p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">{displayTitle("LocationFour")}</p>
                                        <p className="obs-content">
                                            {reportData1.locationFour
                                                ? reportData1.locationFour.name
                                                : "Not Available"}
                                        </p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Observation</p>
                                        <p className="obs-content">{reportData1.whatDidYouObserve || "N/A"}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Where Observed</p>
                                        <p className="obs-content">{reportData1.whereDidYouObserve || "N/A"}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">What Could Have Gone Wrong</p>
                                        <p className="obs-content">{reportData1.whatCouldHaveGoneWrong || "N/A"}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Preventive Action</p>
                                        <p className="obs-content">{reportData1.preventiveAction || "N/A"}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Work Activity</p>
                                        <p className="obs-content">{reportData1.workActivity || "N/A"}</p>
                                    </div>

                                    {/* <div className="col-md-6">
                                        <p className="obs-title">Status</p>
                                        <p className="obs-content">{reportData1.status || "N/A"}</p>
                                    </div> */}
                                </div>


                                {
                                    (reportData1.uploads && reportData1.uploads.length > 0) && <div className="mb-3">
                                        <label className="form-label">Uploads</label>
                                        <div className="border p-3 row">
                                            {
                                                (reportData1.uploads && reportData1.uploads.length > 0) && reportData1.uploads.map(i => (
                                                    <div className="col-md-3">
                                                        <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                    </div>

                                                ))
                                            }

                                        </div>
                                    </div>
                                }









                                <div className="col-md-12">
                                    <p className="obs-title">Immediate Action Taken</p>
                                    <p className="obs-content">{reportData1.immediateActionTaken || "N/A"}</p>
                                </div>
                                <div className="col-md-12">
                                    <p className="obs-title">Immediate Action Date</p>
                                    <p className="obs-content">{moment(reportData1.immediateActionDate).format('DD-MM-YYYY') || "N/A"}</p>
                                </div>

                                <div className="col-md-12">
                                    <p className="obs-title">Action Owner</p>
                                    <p className="obs-content">{reportData1?.actionOwner?.firstName || "N/A"}</p>
                                </div>


                                <div className="row mt-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Action to be taken</p>
                                        <p className="obs-content">{reportData1.actionToBeTaken}</p>

                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">dueDate</p>
                                        <p className="obs-content">{reportData1.dueDate}</p>

                                    </div>
                                </div>

                                <div className="col-md-12 mt-3">
                                    <p className="obs-title">Action taken</p>
                                    <p className="obs-content">{reportData1.actionTaken}</p>

                                </div>

                                {/* <div className="mb-3 mt-3">
                                    <label className="form-label required">Action taken</label>
                                    <textarea ref={actionTaken} className="form-control" rows="3"></textarea>
                                </div> */}

                                {
                                    (reportData1.actionOwnerUploads && reportData1.actionOwnerUploads.length > 0) && <div className="mb-3">
                                        <label className="form-label">Evidence</label>
                                        <div className="border p-3 row">
                                            {
                                                (reportData1.actionOwnerUploads && reportData1.actionOwnerUploads.length > 0) && reportData1.actionOwnerUploads.map(i => (
                                                    <div className="col-md-3">
                                                        <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                    </div>

                                                ))
                                            }

                                        </div>
                                    </div>
                                }
                                {/* <div className="mb-3">
                                    <label className="form-label required">Upload evidence</label>
                                    <DropzoneArea
                                        acceptedFiles={[

                                            'image/jpeg',
                                            'image/png'

                                        ]}
                                        dropzoneText={"Drag and Drop Evidence Images"}
                                        filesLimit={5}
                                        maxFileSize={104857600}

                                        onChange={handleFileChange}

                                    />
                                </div> */}
                                {/* <div className="mb-3">
                                            <label className="form-label required">Action Reviewed By</label>
                                            <select className="form-select" onChange={(e) => getReviewer(e.target)}>
                                                <option>Select</option>
                                              
                                                {
                                                    reviewer.map(u => (
                                                        <option data-id={'user'} key={u.id} value={u.id}>{u.firstName}</option>
                                                    ))
                                                }
                                            </select>
                                        </div> */}









                            </div>
                        </div>
                    </div>}



                </Modal.Body>

                <Modal.Footer className="flex-wrap">



                    <Button
                        variant="light"
                        onClick={() => setShowReportModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>
        </>
    )
}

export default GoodCatchModal