import React from 'react';
import cogoToast from "cogo-toast";

const OverdueTask = ({ tasks }) => {

    const handleEscalation = (taskId) => {
        // Implement your logic here for escalating the task
        cogoToast.error(`IR ID#${taskId} has been escalated.`)
        
    };
    return (
        <div className="container mt-5">
            {tasks.map(task => (
                <div key={task.id} className="card mb-4 shadow">
                    <div className="card-body">
                        <h5 className="card-title">
                            <i className="mdi mdi-alert-circle-outline me-2 text-danger"></i>
                            Overdue Task
                        </h5>
                        <p className="card-text">
                            IR ID# <strong>{task.id}</strong> created on <strong>{task.createdOn}</strong> was due for more than <strong>{task.overdueDays}</strong> days. Investigation was not initiated till date.
                        </p>
                        <button className="btn btn-danger" onClick={() => handleEscalation(task.id)}>Escalate to IR Group</button>
                    </div>
                </div>
            ))}
        </div>
    );
};

export default OverdueTask;
