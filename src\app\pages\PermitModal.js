import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import moment from "moment";
import GalleryPage from '../apps/Gallery';
import API from "../services/API";
import { USERS_URL, API_URL } from "../constants";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
pdfMake.vfs = pdfFonts.pdfMake.vfs;
const PermitModal = ({ reportData, showReportModal, setShowReportModal }) => {

    const [applicantSign, setApplicantSign] = useState('')
    const [assessorSign, setAssessorSign] = useState('')
    const [approverSigns, setApproverSigns] = useState({})

    const [fetchedImages, setFetchedImages] = useState([])

    useEffect(() => {
        getAllUsers();
    }, [])

    const [users, setUsers] = useState([])
    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }


    useEffect(() => {
        const loadSignatures = async () => {
            try {
                const fetchAndSetSignature = async (url, setState) => {
                    const response = await fetch(url);
                    if (!response.ok) throw new Error(`Failed to fetch ${url}`);
                    const blob = await response.blob();
                    const base64Data = await convertBlobToBase64(blob); // Convert blob to Base64
                    setState(base64Data);
                };

                const convertBlobToBase64 = (blob) => {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onloadend = () => resolve(reader.result);
                        reader.onerror = reject;
                        reader.readAsDataURL(blob);
                    });
                };

                if (reportData?.uploads) {
                    const imagePromises = reportData.uploads.map(async (upload) => {
                        if (isImage(upload.src)) {
                            const imageUrl = `${API_URL}/docs/${upload.src}`;
                            const response = await fetch(imageUrl);
                            if (!response.ok) throw new Error(`Failed to fetch ${imageUrl}`);
                            const blob = await response.blob();
                            return await convertBlobToBase64(blob); // Convert to Base64
                        }
                        return null;
                    });

                    const images = await Promise.all(imagePromises);
                    setFetchedImages(images.filter((img) => img !== null)); // Remove null values
                }

                // Fetch Applicant Signature
                if (reportData?.high_risk?.applicantSign) {
                    await fetchAndSetSignature(
                        `${API_URL}/docs/${reportData.high_risk?.applicantSign}`,
                        setApplicantSign
                    );
                }

                // Fetch Assessor Signature
                if (reportData?.high_risk?.assessorSign) {
                    await fetchAndSetSignature(
                        `${API_URL}/docs/${reportData.high_risk?.assessorSign}`,
                        setAssessorSign
                    );
                }

                // Fetch Approver Signatures
                if (reportData?.high_risk?.approvers) {
                    for (const [key, approver] of Object.entries(reportData.high_risk?.approvers)) {
                        if (approver.approverSign) {
                            const approverSignURL = `${API_URL}/docs/${approver.approverSign}`;
                            await fetchAndSetSignature(approverSignURL, (data) =>
                                setApproverSigns((prev) => ({
                                    ...prev,
                                    [key]: {
                                        ...approver,
                                        approverSignURL: data, // Set Base64 data URL
                                    },
                                }))
                            );
                        }
                    }
                }
            } catch (error) {
                console.error("Error loading signatures:", error);
            }
        };

        if (reportData) {
            loadSignatures();
        }
    }, [reportData]);



    // const generatePdf = () => {
    //     const input = document.getElementById('pdf-content');

    //     // Hide elements temporarily
    //     const downloadButton = document.querySelector('.btn-secondary'); // Adjust selector if needed
    //     const modalFooter = document.querySelector('.modal-footer');

    //     if (downloadButton) downloadButton.style.display = 'none';
    //     if (modalFooter) modalFooter.style.display = 'none';

    //     const pdf = new jsPDF('p', 'mm', 'a4');
    //     const pdfWidth = pdf.internal.pageSize.getWidth();
    //     const pdfHeight = pdf.internal.pageSize.getHeight();
    //     const marginBottom = 10; // margin at the bottom of each page

    //     html2canvas(input).then((canvas) => {
    //         const imgData = canvas.toDataURL('image/png');
    //         const imgProps = pdf.getImageProperties(imgData);
    //         const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;

    //         let heightLeft = imgHeight;
    //         let position = 0;

    //         pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
    //         heightLeft -= pdfHeight;

    //         while (heightLeft >= 0) {
    //             position = heightLeft - imgHeight + marginBottom;
    //             pdf.addPage();
    //             pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
    //             heightLeft -= pdfHeight;
    //         }

    //         // Restore elements' visibility
    //         if (downloadButton) downloadButton.style.display = '';
    //         if (modalFooter) modalFooter.style.display = '';

    //         pdf.save(`${reportData.maskId + '-' + new Date()}.pdf`);
    //     });
    // };

    const generatePdf = () => {
        if (!reportData) return;

        // Define content for the PDF
        const docDefinition = {
            content: [
                {

                    alignment: 'center', // Centers everything within the section
                    columns: [
                        {
                            image: require('../../assets/images/logo.png'),
                            width: 100,
                            margin: [0, 0, 10, 0],
                        },
                        [
                            { text: 'Permit to Work', style: 'header', alignment: 'center' },
                            { text: `#${reportData.maskId || ''}`, style: 'subHeader', alignment: 'center' },
                            { text: reportData.status, style: 'status', alignment: 'center' },
                        ],
                        // {
                        //     stack: [
                        //         { text: 'Risk Level', bold: true, fontSize: 12, margin: [0, 0, 0, 2], alignment: 'center' },
                        //         {
                        //             text: reportData.high_risk?.risk_level || 'N/A',
                        //             margin: [0, 5, 0, 5],
                        //             bold: true, // Makes text bold
                        //             color: 'red', // Changes text color to red
                        //             fontSize: 12, // Increases font size for emphasis
                        //             background: 'yellow', // Adds yellow highlight
                        //             alignment: 'center' // Centers the text
                        //         }
                        //     ],
                        // },
                    ],
                    margin: [0, 0, 0, 20],
                }
                ,
                {
                    table: {
                        widths: ['50%', '50%'],
                        body: [
                            [
                                { text: 'Application Organization', style: 'tableHeader' },
                                { text: 'Risk Level', style: 'tableHeader' },
                            ],
                            [
                                { text: reportData.applicantOrg.name, margin: [0, 5, 0, 5] },
                                {
                                    text:
                                      (reportData.high_risk?.risk_level || "N/A").charAt(0).toUpperCase() +
                                      (reportData.high_risk?.risk_level || "N/A").slice(1),
                                    margin: [0, 5, 0, 5],
                                    bold: true,
                                    background: "yellow",
                                  }
                                  

                            ],
                        ],
                    },
                    layout: 'noBorders',
                },

                {
                    text: 'Activities',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                reportData.activities?.length > 0
                    ? {
                        ul: reportData.activities.map((activity, i) => `${i + 1}. ${activity.name}`),
                    }
                    : { text: 'No Activities', italics: true },
                // Applied details
                {
                    text: 'Details',
                    style: 'sectionHeader',
                    margin: [0, 10, 0, 10],
                },
                {
                    table: {
                        widths: ['33%', '33%', '34%'],
                        body: [
                            [
                                { text: 'Applied On', style: 'tableHeader' },
                                { text: 'Work Start Date & Time', style: 'tableHeader' },
                                { text: 'Work End Date & Time', style: 'tableHeader' },
                            ],
                            [
                                { text: moment(reportData.created, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A'), margin: [0, 5, 0, 5] },
                                { text: moment(reportData.permitStartDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A'), margin: [0, 5, 0, 5] },
                                { text: moment(reportData.permitEndDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A'), margin: [0, 5, 0, 5] },
                            ],

                        ],
                    },
                    layout: 'noBorders',
                },

                // Location and business unit
                {
                    text: 'Location Details',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                {
                    table: {
                        widths: ['50%', '50%'],
                        body: [
                            [
                                // { text: 'Location', style: 'tableHeader' },
                                { text: 'Zone', style: 'tableHeader' },
                                { text: 'Area', style: 'tableHeader' },
                            ],
                            [
                                { text: reportData.locationFour?.name || '', margin: [0, 5, 0, 5] },
                                // { text: `${reportData.locationTwo?.name || ''}, ${reportData.locationOne?.name || ''}`, margin: [0, 5, 0, 5] },
                                { text: reportData.locationThree?.name || '', margin: [0, 5, 0, 5] },
                            ],
                        ],
                    },
                    layout: 'noBorders',
                },
                // {
                //     table: {
                //         widths: ['50%', '50%'],
                //         body: [
                //             [
                //                 { text: 'Zone', style: 'tableHeader' },
                //                 // { text: 'Type', style: 'tableHeader' },
                //             ],
                //             [
                //                 { text: reportData.locationFour?.name || '', margin: [0, 5, 0, 5] },
                //                 // { text: reportData.high_risk?.risk_level || '', margin: [0, 5, 0, 5] },
                //             ],
                //         ],
                //     },
                //     layout: 'noBorders',
                // },

                {
                    table: {
                        widths: ['50%', '50%'],
                        body: [
                            [
                                // { text: 'Application Organization', style: 'tableHeader' },
                                { text: 'Contact Number', style: 'tableHeader' },
                            ],
                            [
                                // { text: reportData.applicantOrg.name, margin: [0, 5, 0, 5] },
                                { text: reportData.contactNumber || '', margin: [0, 5, 0, 5] },
                            ],
                        ],
                    },
                    layout: 'noBorders',
                },

                // Description
                {
                    text: 'Description',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                {
                    text: reportData.description || 'N/A',
                    margin: [0, 0, 0, 10],
                },

                // Uploaded Documents
                {
                    text: 'Uploaded Documents',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                fetchedImages?.length > 0
                    ? {
                        stack: [
                            ...fetchedImages.map((src) => ({
                                image: src,
                                width: 150,
                                margin: [0, 0, 0, 10],
                            })),
                            ...reportData.uploads
                                .filter((upload) => !['png', 'jpg', 'jpeg', 'gif', 'bmp'].includes(
                                    upload.src.split('.').pop().toLowerCase()
                                ))
                                .map((upload) => ({
                                    text: upload.src.split('/').pop(),
                                    link: `${API_URL}/docs/${upload.src}`,
                                    style: 'link',
                                    margin: [0, 0, 0, 10],
                                })),
                        ],
                    }
                    : { text: 'No Documents Uploaded', italics: true },

                // Activities
                

                // High-Risk Permit
                {
                    text: 'Other Permits Required',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                reportData.additionalPermits?.length > 0
                    ? {
                        ul: reportData.additionalPermits.map((permit, i) => `${permit.name}`),
                    }
                    : { text: 'No High-Risk Permits', italics: true },

                // Workers
                {
                    text: 'Workers',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                reportData.workers?.length > 0
                    ? {
                        ul: reportData.workers.map((worker, i) => ` ${worker.id} - ${worker.name}`),
                    }
                    : { text: 'No Workers Assigned', italics: true },

                // Checklists
                {
                    text: 'Checklists',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                reportData.checklists?.length > 0
                    ? {
                        table: {
                            widths: ['40%', '30%', '30%'],
                            body: [
                                [
                                    { text: 'Checklist', style: 'tableHeader' },
                                    { text: 'Confirmation', style: 'tableHeader' },
                                    { text: 'Remarks', style: 'tableHeader' },

                                ],
                                ...reportData.checklists.map((item, i) => [
                                    { text: `${i + 1}. ${item.value}`, margin: [0, 5, 0, 5] },
                                    { text: getConfirmationLabel(item), margin: [0, 5, 0, 5] },
                                    { text: item.remarks || 'N/A', margin: [0, 5, 0, 5] },

                                ]),
                            ],
                        },
                        layout: {
                            fillColor: (rowIndex) => (rowIndex === 0 ? '#f2f2f2' : null),
                            hLineWidth: () => 0.5,
                            vLineWidth: () => 0.5,
                            hLineColor: () => '#d9d9d9',
                            vLineColor: () => '#d9d9d9',
                            paddingTop: () => 5,
                            paddingBottom: () => 5,
                        },
                    }
                    : { text: 'No Checklists', italics: true },

                // Signatures
                {
                    text: 'Signatures',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                {
                    table: {
                        widths: ['50%', '50%'],
                        body: [
                            [
                                { text: 'Applicant', style: 'tableHeader' },
                                { text: 'Assessor', style: 'tableHeader' },
                            ],
                            [
                                {
                                    stack: [
                                        { text: `Name: ${reportData.high_risk?.applicantName || 'N/A'}` },
                                        {
                                            text: `I hereby declare that the job in the specified area will be carried out in full compliance with the HSE rules and regulations of SAGT. All necessary controls, as stipulated in the permit and outlined in the Safety Checklist, will be implemented and continuously monitored to ensure compliance and a safe working environment. Upon completion, the work area will be handed over to SAGT in a safe and hazard-free condition.`,
                                            margin: [0, 5, 0, 0], // optional margin to add some spacing
                                        },
                                        reportData.high_risk?.applicantSign && {
                                            image: applicantSign,
                                            width: 100,
                                        },
                                        { text: `Date: ${reportData.high_risk?.applicantSignedDate || 'N/A'}` }
                                    ],
                                },
                                {
                                    stack: [
                                        { text: `Name: ${reportData.high_risk?.assessorName || 'N/A'}` },
                                        {
                                            text: `I hereby declare that I hold the necessary qualifications and competence to assess this permit. I have thoroughly reviewed all aspects of the planned task(s)/activity(ies) and have visually verified that all required control measures and Safety Checklist conditions, as stipulated by SAGT, are in place. To the best of my knowledge, the job is adequately planned in accordance with SAGT’s HSE requirements and is safe to proceed.`,
                                            margin: [0, 5, 0, 0], // optional margin to add some spacing
                                        },
                                        reportData.high_risk?.assessorSign && {
                                            image: assessorSign,
                                            width: 100,
                                        },
                                        { text: `Date: ${reportData.high_risk?.assessorSignedDate || 'N/A'}` },
                                        { text: `Comments : ${reportData.high_risk?.assessorComments || 'N/A'}` }
                                    ],
                                },
                            ],
                        ],
                    },
                    layout: 'noBorders',
                },

                // Approvers
                {
                    text: 'Approvers',
                    style: 'sectionHeader',
                    margin: [0, 20, 0, 10],
                },
                // {
                //     table: {
                //         widths: ['50%', '50%'], // Two equal-width columns
                //         body: Object.entries(reportData.high_risk?.approvers || {})
                //             .reduce((rows, [key, approver], index, array) => {
                //                 if (index % 2 === 0) {
                //                     // If it's an even index, create a new row with two columns
                //                     rows.push([
                //                         {
                //                             stack: [
                //                                 { text: `${approver.approverName || 'N/A'}`, margin: [0, 0, 0, 5] },
                //                                 approver.approverStage && {
                //                                     text: `Stage: ${approver.approverStage}`,
                //                                     margin: [0, 0, 0, 5],
                //                                 },
                //                                 {
                //                                     text: `I hereby declare that I have reviewed the permit and am satisfied that all necessary safety processes, control measures, and requirements, as stipulated by SAGT, are in place. Based on this assessment, I grant permission to the applicant to carry out the specified activity(ies) in the designated area(s). I acknowledge my responsibility in approving this permit, ensuring compliance with SAGT’s HSE standards.`,
                //                                     margin: [0, 5, 0, 0],
                //                                 },
                //                                 approver.approverSign && {
                //                                     image: approverSigns[key]?.approverSignURL,
                //                                     width: 100,
                //                                     margin: [0, 5, 0, 5],
                //                                 },
                //                                 approver.rejectedDate
                //                                     ? { text: `Rejected Date: ${approver.rejectedDate}`, margin: [0, 0, 0, 5] }
                //                                     : approver.approverSignedDate && { text: `Date: ${approver.approverSignedDate}`, margin: [0, 0, 0, 5] },
                //                                 approver.approverComments && { text: `Comments: ${approver.approverComments}`, margin: [0, 0, 0, 5] },
                //                             ].filter(Boolean),
                //                         },
                //                         array[index + 1] // Check if there's another item for the second column
                //                             ? {
                //                                 stack: [
                //                                     { text: `${array[index + 1][1].approverName || 'N/A'}`, margin: [0, 0, 0, 5] },
                //                                     array[index + 1][1].approverStage && {
                //                                         text: `Stage: ${array[index + 1][1].approverStage}`,
                //                                         margin: [0, 0, 0, 5],
                //                                     },
                //                                     {
                //                                         text: `I hereby declare that I have reviewed the permit and am satisfied that all necessary safety processes, control measures, and requirements, as stipulated by SAGT, are in place. Based on this assessment, I grant permission to the applicant to carry out the specified activity(ies) in the designated area(s). I acknowledge my responsibility in approving this permit, ensuring compliance with SAGT’s HSE standards.`,
                //                                         margin: [0, 5, 0, 0],
                //                                     },
                //                                     array[index + 1][1].approverSign && {
                //                                         image: approverSigns[array[index + 1][0]]?.approverSignURL,
                //                                         width: 100,
                //                                         margin: [0, 5, 0, 5],
                //                                     },
                //                                     array[index + 1][1].rejectedDate
                //                                         ? { text: `Rejected Date: ${array[index + 1][1].rejectedDate}`, margin: [0, 0, 0, 5] }
                //                                         : array[index + 1][1].approverSignedDate && { text: `Date: ${array[index + 1][1].approverSignedDate}`, margin: [0, 0, 0, 5] },
                //                                     array[index + 1][1].approverComments && { text: `Comments: ${array[index + 1][1].approverComments}`, margin: [0, 0, 0, 5] },
                //                                 ].filter(Boolean),
                //                             }
                //                             : {}, // If there's no second item, add an empty object
                //                     ]);
                //                 }
                //                 return rows;
                //             }, []),
                //     },
                //     // layout: 'noBorders', // No borders for a cleaner look
                // }

                {
                    table: {
                        widths: ['100%'], // Single column layout
                        body: Object.entries(reportData.high_risk?.approvers || {}).map(([key, approver]) => [
                            {
                                stack: [
                                    { text: `${approver.approverName || 'N/A'}`, margin: [0, 0, 0, 5] },
                                    approver.approverStage && {
                                        text: `Stage: ${approver.approverStage}`,
                                        margin: [0, 0, 0, 5],
                                    },
                                    {
                                        text: `I hereby declare that I have reviewed the permit and am satisfied that all necessary safety processes, control measures, and requirements, as stipulated by SAGT, are in place. Based on this assessment, I grant permission to the applicant to carry out the specified activity(ies) in the designated area(s). I acknowledge my responsibility in approving this permit, ensuring compliance with SAGT’s HSE standards.`,
                                        margin: [0, 5, 0, 0],
                                    },
                                    approver.approverSign && {
                                        image: approverSigns[key]?.approverSignURL,
                                        width: 100,
                                        margin: [0, 5, 0, 5],
                                    },
                                    approver.rejectedDate
                                        ? { text: `Rejected Date: ${approver.rejectedDate}`, margin: [0, 0, 0, 5] }
                                        : approver.approverSignedDate && { text: `Date: ${approver.approverSignedDate}`, margin: [0, 0, 0, 5] },
                                    approver.approverComments && { text: `Comments: ${approver.approverComments}`, margin: [0, 0, 0, 5] },
                
                                    // Horizontal Line at the End of Each Row
                                    {
                                        canvas: [
                                            { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1,lineColor: '#d9d9d9' }
                                        ],
                                        margin: [0, 10, 0, 10]
                                    }
                                ].filter(Boolean),
                            }
                        ]),
                    },
                    layout: 'noBorders',
                }
                



            ],
            styles: {
                header: { fontSize: 18, bold: true, alignment: 'center' },
                subHeader: { fontSize: 14, bold: true, alignment: 'center' },
                status: { fontSize: 12, color: 'gray', alignment: 'center' },
                sectionHeader: { fontSize: 16, bold: true, margin: [0, 10, 0, 5], color: '#007bff' },
                tableHeader: { bold: true, fillColor: '#f2f2f2' },
                link: { color: 'blue', decoration: 'underline' },
            },
            defaultStyle: {
                fontSize: 12,
            },
        };

        pdfMake.createPdf(docDefinition).download(`${reportData.maskId || 'Permit'}-${new Date().toISOString()}.pdf`);
    };


    function getName(id) {
        const user = users.find(user => user.id === id)
        return id ? user.firstName : ''
    }
    const isImage = (filename) => {
        const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp'];
        const extension = filename.split('.').pop().toLowerCase();
        return imageExtensions.includes(extension);
    };

    const getConfirmationLabel = (data) => {
        const tes = data.options.find(item => item.checked === 1)

        return tes.label
    }
    return (
        <>
            <Modal
                show={showReportModal}
                size={'lg'}
                onHide={() => setShowReportModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                id="pdf-content"
            >
                <Modal.Header>

                    {reportData && (
                        <div className="row" style={{ width: '100%' }}>
                            <div className="col-9">
                                <div className="row">
                                    <div className="col-3" style={{ borderRight: '1px solid #D1D5DB' }}>
                                        <img src={require("../../assets/images/logo.png")} className="me-3" alt="logo" style={{ maxWidth: '100%' }} />
                                    </div>
                                    <div className="col-9">
                                        <h4>Permit to Work</h4>
                                        <div className="d-flex align-items-center">
                                            <p className="me-2">#{reportData.maskId || ''} </p>
                                            <p className="card-eptw">{reportData.status} </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-3 d-flex justify-content-end align-items-center">
                                {(reportData.status === "Active") && (
                                    <Button type="button" className="btn btn-secondary"
                                        onClick={generatePdf}

                                    >Download Report</Button>
                                )}
                            </div>

                        </div>
                    )}


                </Modal.Header>
                <Modal.Body>
                    {
                        reportData && (<>



                            <div className="section p-1">
                                <div className="row mb-3">

                                    <div className="col-md-6">
                                        <p className="obs-title">Applied On</p>
                                        <p className="obs-content">{moment(reportData.created, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A') || ''}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Work Start Date & Time</p>
                                        <p className="obs-content">{moment(reportData.permitStartDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A') || ''}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Work End Date & Time</p>
                                        <p className="obs-content">{moment(reportData.permitEndDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A') || ''}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Applied By</p>
                                        <p className="obs-content">{reportData.high_risk?.applicantName || ''}</p>
                                    </div>
                                </div>

                            </div>

                            <div className="section p-1">
                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Location</p>
                                        <p className="obs-content">{(reportData.locationOne && reportData.locationTwo) && reportData.locationTwo.name + ', ' + reportData.locationOne.name}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Business Unit</p>
                                        <p className="obs-content">{reportData.locationThree && reportData.locationThree.name}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Project/DC name</p>
                                        <p className="obs-content">{reportData.locationFour && reportData.locationFour.name}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Type</p>
                                        <p className="obs-content">{reportData.high_risk && reportData.high_risk?.risk_level}</p>
                                    </div>
                                </div>


                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Permit Type</p>
                                        <p className="obs-content">{reportData.permitType && reportData.permitType}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Permit Status</p>
                                        <p className="obs-content">{reportData.status && reportData.status}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Contact Number</p>
                                        <p className="obs-content">{reportData.contactNumber && reportData.contactNumber
                                        }</p>
                                    </div>
                                </div>

                            </div>

                            <div className="section p-1">


                                <div className="row mb-3">
                                    <div className="col-md-8">
                                        <p className="obs-title">Description</p>
                                        <p className="obs-content">{reportData.description || ''}</p>
                                    </div>

                                </div>


                                {reportData.permitType !== 'CA' && <div className="row mb-3">
                                    <div className="col-md-12">
                                        <p className="obs-title">Uploaded Documents</p>

                                        {reportData.uploads && reportData.uploads.length > 0 ? (
                                            <div>

                                                {reportData.uploads.filter(item => isImage(item.src)).length > 0 && (
                                                    <GalleryPage photos={fetchedImages.map((src) => ({ src }))} />
                                                )}
                                                {reportData.uploads
                                                    .filter(item => !isImage(item.src))
                                                    .map((item, index) => (
                                                        <p key={index} className="obs-content">
                                                            <a href={item.src} target="_blank" rel="noopener noreferrer">
                                                                {item.src.split('/').pop()}
                                                            </a>
                                                        </p>
                                                    ))
                                                }
                                            </div>
                                        ) : (
                                            <p className="obs-content">No Documents Upload</p>
                                        )}
                                    </div>
                                </div>
                                }


                            </div>


                            <div className="section p-1">
                                <div className="row mb-3">
                                    <div className="col-md-8 mb-4">
                                        <p className="obs-title"> Activities</p>

                                        {reportData.activities.map((item, i) => (

                                            <p className="obs-content">{i + 1} . {item.name}</p>

                                        ))}

                                    </div>

                                    <div className="col-md-8 mb-4">
                                        <p className="obs-title"> HighRisk Permit</p>

                                        {reportData.additionalPermits.map((item, i) => (

                                            <p className="obs-content">{i + 1} . {item.name}</p>

                                        ))}

                                    </div>

                                    <div className="col-md-8">
                                        <p className="obs-title"> Workers </p>

                                        {reportData.workers.map((item, i) => (

                                            <p className="obs-content">{i + 1} . {item.name}</p>

                                        ))}

                                    </div>

                                </div>

                            </div>

                            <div className="section p-1 mb-3">

                                <p className="obs-title"> Checklists </p>


                                <div className="row d-flex">
                                    {reportData.checklists.map((item, index) => {

                                        return (
                                            <div className="col-4 ">
                                                <div key={index} className=" box">
                                                    <p className="checklist-item">{index + 1}. {item.value}</p>

                                                    <div className="activity-confirmation col-6">
                                                        <p className="activity-answer">{getConfirmationLabel(item)}</p>
                                                        <p className="activity-answer"><strong>Remarks: </strong>{item.remarks}</p>
                                                    </div>

                                                </div>
                                            </div>
                                        );

                                    })}
                                </div>
                            </div>

                            <div className="section p-1">
                                <div className="row mb-3">
                                    {/* Applicant Section */}
                                    {reportData.high_risk?.applicantName && (
                                        <div className="col-md-6">
                                            <p className="obs-title">Applicant - {reportData.high_risk.applicantName}</p>
                                            <p>I hereby declare that the job in the specified area will be carried out in full compliance with the HSE rules and regulations of SAGT. All necessary controls, as stipulated in the permit and outlined in the Safety Checklist, will be implemented and continuously monitored to ensure compliance and a safe working environment. Upon completion, the work area will be handed over to SAGT in a safe and hazard-free condition.
                                            </p>
                                            {reportData.high_risk.applicantSign && (
                                                <img
                                                    src={applicantSign}
                                                    alt="Applicant Signature"
                                                    style={{ width: '100px', height: '100px' }}
                                                />
                                            )}
                                            {reportData.high_risk.applicantSignedDate && (
                                                <p>
                                                    Date: <span>{reportData.high_risk.applicantSignedDate}</span>
                                                </p>
                                            )}
                                        </div>
                                    )}

                                    {/* Assessor Section */}
                                    {reportData.high_risk?.assessorName && (
                                        <div className="col-md-6">
                                            <p className="obs-title">Assessor - {reportData.high_risk.assessorName}</p>
                                            <p>I hereby declare that I hold the necessary qualifications and competence to assess this permit. I have thoroughly reviewed all aspects of the planned task(s)/activity(ies) and have visually verified that all required control measures and Safety Checklist conditions, as stipulated by SAGT, are in place. To the best of my knowledge, the job is adequately planned in accordance with SAGT’s HSE requirements and is safe to proceed.
                                            </p>
                                            {reportData.high_risk.assessorSign && (
                                                <img
                                                    src={assessorSign}
                                                    alt="Assessor Signature"
                                                    style={{ width: '100px', height: '100px' }}
                                                />
                                            )}
                                            {reportData.high_risk.assessorSignedDate && (
                                                <p>
                                                    Date: <span>{reportData.high_risk.assessorSignedDate}</span>
                                                </p>
                                            )}
                                            {reportData.high_risk.assessorComments && (
                                                <p>
                                                    Comments: <span>{reportData.high_risk.assessorComments}</span>
                                                </p>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Approvers Section */}
                            {reportData.high_risk?.approvers && (
                                <div className="section p-1">
                                    <div className="row mb-3">
                                        {Object.keys(reportData.high_risk.approvers).map((key, index) => {
                                            const approver = reportData.high_risk.approvers[key];
                                            const approverSignURL = approverSigns[key]?.approverSignURL; // Fetching from state
                                            return (
                                                <div className="col-md-6 mb-3" key={index}>
                                                    <p className="obs-title">Approver - {approver.approverName}</p>
                                                    <p>I hereby declare that I have reviewed the permit and am satisfied that all necessary safety processes, control measures, and requirements, as stipulated by SAGT, are in place. Based on this assessment, I grant permission to the applicant to carry out the specified activity(ies) in the designated area(s). I acknowledge my responsibility in approving this permit, ensuring compliance with SAGT’s HSE standards.</p>
                                                    {approver.approverStage && (
                                                        <p>
                                                            Stage: <span>{approver.approverStage}</span>
                                                        </p>
                                                    )}
                                                    {approverSignURL && ( // Use the dynamically loaded signature URL
                                                        <img
                                                            src={approverSignURL}
                                                            alt={`Signature of ${approver.approverName}`}
                                                            style={{ width: '100px', height: '100px' }}
                                                        />
                                                    )}
                                                    {approver.rejectedDate ? (
                                                        <p>
                                                            Rejected Date: <span>{approver.rejectedDate}</span>
                                                        </p>
                                                    ) : (
                                                        approver.approverSignedDate && (
                                                            <p>
                                                                Date: <span>{approver.approverSignedDate}</span>
                                                            </p>
                                                        )
                                                    )}
                                                    {approver.approverComments && (
                                                        <p>
                                                            Comments: <span>{approver.approverComments}</span>
                                                        </p>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}






                        </>)
                    }
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {

                        <>
                            <Button variant="light" onClick={() => setShowReportModal(false)}>Close</Button>

                        </>

                    }

                </Modal.Footer>
            </Modal>
        </>
    )
}

export default PermitModal;