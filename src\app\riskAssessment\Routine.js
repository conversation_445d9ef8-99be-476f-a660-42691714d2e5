import React, { useEffect, useState, useContext } from 'react'
import { Dropdown } from 'primereact/dropdown';
import { But<PERSON> } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputTextarea } from 'primereact/inputtextarea'
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
// import 'primeflex/primeflex.css'; 
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { GMS1_URL, GET_USER_ROLE_BY_MODE, HAZARDS_CATEGOTY, GET_ALL_USER } from '../constants';
import API from '../services/API';
import { useSelector } from 'react-redux';
import { InputSwitch } from 'primereact/inputswitch';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
function Routine() {

    const user = useSelector((state) => state.login.user);
    const [files, setFiles] = useState([])
    const [depart, setDepart] = useState([])
    const [activity, setActivity] = useState([])
    const [crew, setCrew] = useState([])
    const [selectedDepart, setSelectedDepart] = useState(null)
    const [selectedActivity, setSelectedActivity] = useState(null)
    const [selectedCrew, setSelectedCrew] = useState([])
    const [addSubActivity, setAddSubActivity] = useState(false)
    const [activityDesc, setActivityDesc] = useState(null)
    const [task, setTask] = useState([])
    const [subActivityName, setSubActivityName] = useState('')
    const [visible, setVisible] = useState(false)
    const [item, setItem] = useState('')
    const [index, setIndex] = useState('')
    const [activeIndex, setActiveIndex] = useState(0);
    const [hazards, setHazards] = useState([])
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [severityTable, setSeverityTable] = useState(false)
    const [likelyhoodTable, setLikelyhoodTable] = useState(false)
    const [riskTable, setRiskTable] = useState(false)
    const [responsibility, setResponsibility] = useState([])
    const [required, setRequired] = useState(true)

    const severity = [
        { "value": "1", "label": "Negligible (1)" },
        { "value": "2", "label": "Minor (2)" },
        { "value": "3", "label": "Moderate (3)" },
        { "value": "4", "label": "Major (4)" },
        { "value": "5", "label": "Catastrophic (5)" }
    ]
    const likelyhood = [
        { label: "Rare (1)", value: "1" },
        { label: "Unlikely (2)", value: "2" },
        { label: "Possible (3)", value: "3" },
        { label: "Likely (4)", value: "4" },
        { label: "Almost Certain (5)", value: "5" },
    ]
    const impactOn = [
        { 'label': 'Personnel', 'value': 'Personnel' },
        { 'label': 'Property', 'value': 'Property' },
        { 'label': 'Environment', 'value': 'Environment' },
        { 'label': 'Service Loss', 'value': 'Service Loss' },
    ]
    const control = [
        { 'label': 'No Control', 'value': 'No Control' },
        { 'label': 'Engineering', 'value': 'Engineering' },
        { 'label': 'Administrative', 'value': 'Administrative' },
        { 'label': 'PPE', 'value': 'PPE' }

    ]
    const severityData = [
        { id: '5', severity: '5 Catastrophic' },
        { id: '4', severity: '4 Major' },
        { id: '3', severity: '3 Moderate' },
        { id: '2', severity: '2 Minor' },
        { id: '1', severity: '1 Insignificant' },
    ];
    const levelData = [
        { level: '1 (A)', descriptor: 'Rare', detailedDescription: '' },
        { level: '2 (B)', descriptor: 'Unlikely', detailedDescription: '' },
        { level: '3 (C)', descriptor: 'Possible', detailedDescription: '' },
        { level: '4 (D)', descriptor: 'Likely', detailedDescription: '' },
        { level: '5 (E)', descriptor: 'Almost Certain', detailedDescription: '' },
    ];
    const tableData = [
        { id: '5', severity: 'Catastrophic', rare: 5, unlikely: 10, possible: 15, likely: 20, almostCertain: 25 },
        { id: '4', severity: 'Major', rare: 4, unlikely: 8, possible: 12, likely: 16, almostCertain: 20 },
        { id: '3', severity: 'Moderate', rare: 3, unlikely: 6, possible: 9, likely: 12, almostCertain: 15 },
        { id: '2', severity: 'Minor', rare: 2, unlikely: 4, possible: 6, likely: 8, almostCertain: 10 },
        { id: '1', severity: 'Insignificant', rare: 1, unlikely: 2, possible: 3, likely: 4, almostCertain: 5 },
    ];


    useEffect(() => {
        getWorkActivity()
        getCrewList()
        getHazardList()
        getAllResponsibility();
    }, [])
    const getAllResponsibility = async () => {
        const uriString = { include: [{ "relation": "workingGroup" }, { "relation": "designation" }, { "relation": "ghsOne" }] }
        const url = `${GET_ALL_USER}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const depart = response.data.map(item => {
                return { id: item.id, name: item.firstName, email: item.email }
            })
            setResponsibility(depart)
        }

    }
    const getHazardList = async () => {
        const uriString = { include: ["hazards"] };
        const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const data = response.data.filter((item) => item.name !== 'Hazard-Based')
            setHazards(data);
        }
    }
    const handleFileChange = (file) => {
        setFiles(file)

    }
    const getWorkActivity = async () => {
        const uriString = { include: ["ghsTwos"] };

        const url = `${GMS1_URL}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {


            setDepart(response.data)
        }
    };

    const getCrewList = async () => {

        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ra-member'
        });
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {
                let department = item.ghsOne ? item.ghsOne.name : ''
                if (item.id !== user.id) {
                    data.push({ name: item.firstName, id: item.id, department: department });
                }
            });

            setCrew(data);
        }
    };
    const selectedDepartmentTemplate = (option, props) => {

        if (option) {
            const active = depart.find(item => item.id === option.id);
            setActivity(active.ghsTwos)
            return (
                <div className="d-flex align-items-center">
                    <div>{option.name}</div>
                </div>
            );
        }

        return <span>{props.placeholder}</span>;
    };

    const departmentOptionTemplate = (option) => {
        return (
            <div className="d-flex align-items-center">
                <div>{option.name}</div>
            </div>
        );
    };

    const AddSubActivityTitle = () => {

        if (subActivityName !== '') {
            const t = [
                { type: 'activity', name: subActivityName, images: [] },
                { type: 'hazards', selected: [] },
                { type: 'consequence', option: [{ value: "", files: [], current_type: '', }] },
                { type: 'current_control', option: [{ value: "", files: [], current_type: '', }] },
                { type: 'assessment', severity: '', likelyhood: '', level: '' },
                { type: 'additional', accept: false },
                { type: 'responsibility', option: [{ current_type: '', person: '', date: null, value: '' }] },
                { type: 'reassessment', severity: '', likelyhood: '', level: '' },
                { type: 'activeStep', step: 0 },
                { type: 'stage', level: ['Hazards Identification', 'Consequences', 'Current Controls', 'Risk Assessment'] },
                { type: 'completed_stage', level: [] }
            ]
            setTask((prev) => [...prev, t]);
            setSubActivityName('');
            setFiles([])
            setAddSubActivity(false)
        } else {

        }

    }
    const deleteTask = (e, i) => {
        e.stopPropagation()
        const t = task;
        const newTasks = task.filter((_, idx) => idx !== i);
        setTask(newTasks);
    }
    const openDialog = (item, i) => {
        setItem('')
        setItem(item);
        setIndex(i)
        setVisible(true)

    }
    const editSubActivityTitle = () => {
        console.log(task[index][0])

    }
    const checkRequiredStepField = () => {

        let required = true;
        if (item[8].step === 0) {

            if (item[1].selected.length === 0) {
                required = false
                setRequired(false)
            }

        } else if (item[8].step === 1) {
            for (let option of item[2].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false
                    setRequired(false)
                }
            }

        } else if (item[8].step === 2) {
            for (let option of item[3].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false
                    setRequired(false)
                }
            }
        } else if (item[8].step === 3) {
            if (item[4].severity === '' && item[4].likelyhood === '') {
                required = false
                setRequired(false)
            }

        }
        else if (item[8].step === 4) {
            if (item[7].severity === '' && item[7].likelyhood === '') {
                required = false
                setRequired(false)
            }

        }


        return required

    }

    const headerTemplate = (
        <div className="d-flex flex-column">
            <div className='col-12 mb-3'>
                <p>Add Sub Activity for</p>
                <h6>{item !== '' && item[0] && item[0].name}</h6>
            </div>
            {item !== '' && item[8] && item[8].step !== undefined && (
                <Stepper activeStep={item[8].step} className='mb-4'>
                    {item[9].level.map((label, index) => (
                        <Step key={index}>
                            <StepLabel className='step-label d-flex flex-column'>{label}</StepLabel>
                        </Step>
                    ))}
                </Stepper>
            )}
        </div>
    );
    const handleNext = () => {
        console.log(checkRequiredStepField())
        if (checkRequiredStepField()) {
            item[10].level.push(item[8].step)
            item[10].level = [...new Set(item[10].level)]
            if (item[8].step === item[9].level.length - 1) {
                setVisible(false)

            } else {
                const t = task;
                const text = t.map((item, i) => {
                    if (i === index) {
                        item.map((ite) => {
                            if (ite.type === 'activeStep') {
                                ite.step = ite.step + 1

                            }

                        })
                    }
                    return item
                })
                setTask(text)
                setItem(text[index])
            }
        } else {
            alert('fill required field')
        }

    };
    const handleBack = () => {

        item[10].level = item[10].level.filter(item1 => item1 !== item[8].step);
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'activeStep') {
                        ite.step = ite.step - 1

                    }
                    if (ite.type === 'completed_stage') {
                        ite.level.pop(ite.step)
                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])


    };

    const footerTemplate = (
        <div className="d-flex justify-content-between">
            <div>
                <Button
                    className='me-2'
                    outlined
                    label="Back"
                    onClick={handleBack}
                    disabled={item[8] && item[8].step !== undefined && item[8].step === 0}
                />
            </div>
            <div>
                <Button className='me-2' outlined label="Cancel" />
                <Button className='me-2' outlined label="Save Progress" />
                <Button
                    label={item[8] && item[8].step !== undefined && item[8].step === item[9].level.length - 1 ? "Finish" : "Next"}
                    onClick={handleNext}
                />
            </div>
        </div>
    );
    const rowClassName = (data) => {
        switch (data.level[0]) {
            case '1':
                return 'level-1';
            case '2':
                return 'level-2';
            case '3':
                return 'level-3';
            case '4':
                return 'level-4';
            case '5':
                return 'level-5';
            default:
                return '';
        }
    };
    const cellClassName = (value) => {
        if (value === 1 || value === 2 || value === 3 || value === 4) return 'cell-green';
        if (value === 15 || value === 20 || value === 25 || value === 16) return 'cell-red';
        return 'cell-yellow';
    };
    const cellStyle = (data, field) => cellClassName(data[field]);

    const onClickHazards = (ha, j) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'hazards') {
                        if (ite.selected.some(hazards => hazards.id === ha.id)) {
                            const index = ite.selected.findIndex(hazard => hazard.id === ha.id);
                            if (index !== -1) {
                                const newHazards = [...ite.selected];
                                newHazards.splice(index, 1);
                                ite.selected = newHazards;
                            }
                        } else {
                            ite.selected.push(ha)
                        }
                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onChangeSeverity = (e, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.severity = e.value

                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onChangeLikelyhood = (e, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.likelyhood = e.value

                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onDeleteHaz = (item1) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'hazards') {
                        const index = ite.selected.findIndex(hazard => hazard.id === item1.id);
                        if (index !== -1) {
                            const newHazards = [...ite.selected];
                            newHazards.splice(index, 1);
                            ite.selected = newHazards;
                        }
                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onImapactOn = (value, j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.current_type = value
                            }


                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const onControlAddion = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.current_type = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onControlAddionText = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.value = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onResponsePerson = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.person = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onResponseDate = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.date = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onChangeReAss = (value) => {
        console.log(value)
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'additional') {
                        ite.accept = value
                    }
                    if (ite.type === 'stage') {
                        if (value === true) {
                            ite.level.push('Addiitonal Controls')
                        } else {
                            ite.level = ite.level.filter(item => item !== 'Addiitonal Controls');
                        }
                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onConseqText = (value, j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.value = value
                            }

                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const addConsequence = (type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.push({ value: "", files: [], current_type: '', })
                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const addAdditionalControl = () => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.push({ current_type: '', person: '', date: null, value: '' })
                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onDeleteConseq = (j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        const newHazards = [...ite.option];
                        newHazards.splice(j, 1);
                        ite.option = newHazards;

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const ContextAwareToggle = ({ children, eventKey, callback }) => {

        const { activeEventKey } = useContext(AccordionContext);

        const decoratedOnClick = useAccordionButton(
            eventKey,
            () => callback && callback(eventKey),
        );

        const isCurrentEventKey = activeEventKey === eventKey;
    
        return (

            <i className={`pi ${isCurrentEventKey ? 'pi-angle-up' : 'pi-angle-down'}`} onClick={decoratedOnClick}></i>

        )
    }

    return (
        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className='card-body p-0'>

                        <div className='borderSection p-4'>
                            <h4 className="risk-title m-0">Routine Work RiskAssessment</h4>

                            <div className='row mb-4'>
                                <div className='col-4'>
                                    <Dropdown options={depart} optionLabel="name" value={selectedDepart} onChange={(e) => setSelectedDepart(e.value)} placeholder="Department" className='d-flex'
                                        filter valueTemplate={selectedDepartmentTemplate} itemTemplate={departmentOptionTemplate} showClear />
                                </div>
                                <div className='col-8'>
                                    <Dropdown optionLabel="name" placeholder="WorkActivity" className='d-flex' options={activity} value={selectedActivity} onChange={(e) => setSelectedActivity(e.value)}
                                        filter itemTemplate={departmentOptionTemplate} showClear
                                    />
                                </div>
                            </div>
                            <div className='row mb-4'>
                                <div className='col-12'>
                                    <MultiSelect optionLabel="name" options={crew}
                                        filter itemTemplate={departmentOptionTemplate}
                                        value={selectedCrew} onChange={(e) => setSelectedCrew(e.value)} showClear
                                        placeholder="Select Members" maxSelectedLabels={7} className="d-flex" />
                                </div>
                            </div>
                            <div className='row '>
                                <div className='col-4'>
                                    <Button label="Send Notification" outlined className='d-flex' />
                                </div>
                            </div>

                        </div>

                        <div className='borderSection p-4'>
                            <h4 className="risk-title m-0">Activity</h4>
                            <div className='row'>
                                <div className='col-6'>
                                    <p>Activity Name</p>
                                    <h4 className="risk-title m-0">{selectedActivity ? selectedActivity.name : ''}</h4>
                                </div>
                            </div>
                            <div className='row mb-4'>
                                <div className="d-flex flex-column col-8">
                                    <label htmlFor="username" className='mb-2'>Description</label>

                                    <InputTextarea rows={3} cols={30} onChange={(e) => setActivityDesc(e.target.value)} />

                                </div>
                            </div>

                        </div>
                        <div className='borderSection p-4'>

                            <div className='row mb-4'>
                                <div className='col-12'>
                                    <h4 className="risk-title-sub m-0">Sub Activities</h4>
                                    <p>Add sub activities and information such as hazards and consequences for the activity</p>

                                </div>
                            </div>
                            <div className='col-12 mb-4'>
                                {task.length === 0 ?
                                    <p>No sub-activities added</p>
                                    :
                                    <Accordion>
                                        {task.map((item, i) => {
                                            return (<>
                                                <div className='d-flex align-items-center mb-3'>
                                                    <div className='col-11 subBox p-3 d-flex'>
                                                        <div className='d-flex align-items-center col-11'>
                                                            <div className='col-4 d-flex align-items-center'>
                                                                <i className='pi pi-arrows-v'></i>
                                                                <h6 className='m-0 ps-2 pointer' onClick={() => openDialog(item, i)}>{item[0].name}</h6>
                                                            </div>
                                                            <div className='col-8 d-flex align-items-center justify-content-end fsteps'>
                                                                {item[9].level.map((level, l) => {
                                                                    return (
                                                                        <div className="d-flex align-items-center">
                                                                            <RadioButton checked={item[10].level.includes(l)} />
                                                                            <span className="ms-2 me-2">{level}</span>
                                                                        </div>
                                                                        // <h6 className='d-flex align-items-center m-0'><i className='pi pi-circle'></i> <span>{level}</span> </h6>
                                                                    )
                                                                })}
                                                            </div>
                                                        </div>
                                                        <div className='col-1 text-center'>
                                                            <ContextAwareToggle eventKey={i} />
                                                        </div>


                                                    </div>
                                                    <div className='col-1 text-center'>
                                                        <i className="pi pi-trash" onClick={(e) => deleteTask(e, i)}></i>
                                                    </div>
                                                </div>
                                                <Accordion.Collapse eventKey={i}>
                                                    <div className='p-4 mb-4 col-11' style={{ border: '1px solid rgba(209, 213, 219, 1)' }}>
                                                        <div className='col-12'>
                                                            <div className="d-flex flex-column col-12">
                                                                <label htmlFor="username" className='mb-2'>Sub-Activity Name</label>
                                                                <InputText defaultValue={item[0].name} onChange={(e) => item[0].name = e.target.value} />
                                                            </div>
                                                        </div>
                                                        <div className='col-12 mt-3'>
                                                            <label htmlFor="username" className='mb-2'>Image Uploads</label>
                                                            <div className="mb-3">
                                                                <DropzoneArea
                                                                    acceptedFiles={[
                                                                        'image/jpeg',
                                                                        'image/png'
                                                                    ]}
                                                                    dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                                                    filesLimit={5}
                                                                    maxFileSize={104857600}
                                                                    onChange={handleFileChange}
                                                                    showPreviewsInDropzone={false}
                                                                    showPreviews={true}
                                                                    dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                                                />
                                                            </div>
                                                        </div>
                                                        <div className='col-12 mt-3'>
                                                            <label htmlFor="username" className='mb-2'>Uploaded</label>
                                                        </div>
                                                        <div className='col-12 mt-3'>
                                                            <Button label="Save" onClick={() => editSubActivityTitle()} />
                                                        </div>

                                                    </div>
                                                </Accordion.Collapse>
                                            </>)
                                        })}
                                    </Accordion>
                                }

                            </div>
                            <div className='row '>
                                <div className='col-4'>
                                    <Button label="Add Sub-Activity" outlined className='d-flex' onClick={() => setAddSubActivity(!addSubActivity)} />
                                </div>
                            </div>
                            {addSubActivity &&
                                <div className='p-4 mt-4' style={{ border: '1px solid rgba(209, 213, 219, 1)' }}>
                                    <div className='col-12'>
                                        <div className="d-flex flex-column col-12">
                                            <label htmlFor="username" className='mb-2'>Sub-Activity Name</label>

                                            <InputText onChange={(e) => setSubActivityName(e.target.value)} />

                                        </div>
                                    </div>
                                    <div className='col-12 mt-3'>
                                        <label htmlFor="username" className='mb-2'>Image Uploads</label>
                                        <div className="mb-3">
                                            <DropzoneArea
                                                acceptedFiles={[
                                                    'image/jpeg',
                                                    'image/png'
                                                ]}
                                                dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                onChange={handleFileChange}
                                                showPreviewsInDropzone={false}
                                                showPreviews={true}
                                                dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                            />
                                        </div>
                                    </div>

                                    <div className='col-12 mt-3'>
                                        <label htmlFor="username" className='mb-2'>Uploaded</label>

                                    </div>
                                    <div className='col-12 mt-3'>

                                        <Button label="Save" onClick={() => AddSubActivityTitle()} />

                                    </div>

                                </div>
                            }
                        </div>


                        <div className='borderSection p-4'>
                            <h5 className="mb-4 fw-bold ">Overall recommendations of the RA Team</h5>


                            <div className='row mb-4 '>

                                <div className='col-8 '>
                                    <div className='row'>
                                        <div className='col-12 mb-3'>
                                            <Dropdown optionLabel="name" placeholder="Type" className='d-flex'
                                                filter />
                                        </div>
                                        <div className='col-12'>
                                            <Dropdown optionLabel="name" placeholder="Department" className='d-flex'
                                                filter />
                                        </div>

                                    </div>

                                </div>

                            </div>
                            <div className='row '>
                                <p>Considering the hazards and risks associated with this work activity, the RA team requires the following hish-risk permits to be approved and active when applying for permit for this specific activity</p>

                            </div>
                            <div className='row mb-4'>
                                <div className="d-flex flex-column col-8">
                                    <label htmlFor="username" className='mb-2'>Additional Recommentation and Remarks</label>

                                    <InputTextarea rows={3} cols={30} />

                                </div>
                            </div>


                        </div>
                        <div className='borderSection p-4'>
                            <h5 className="mb-4 fw-bold ">Declaration</h5>


                            <div className='row mb-2'>
                                <p>I affirm my position as the Team Leader for this Risk Assessment. The eventual outcome signifies our collective professional judgement, reached through consensus and utilizing our team’s fullest capabilities.</p>

                            </div>
                            <div className='row mb-4'>
                                <div className="d-flex flex-column col-8">
                                    <InputTextarea rows={3} cols={30} />
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            {item !== '' && <>
                <Dialog visible={visible} header={headerTemplate} modal footer={footerTemplate} style={{ width: '70rem' }} onHide={() => setVisible(false)}>

                    {item[8].step === 0 && <>
                        <Accordion defaultActiveKey={'0'}>
                            <Accordion.Item eventKey="0">
                                <Accordion.Header style={(required === false && item[1].selected.length === 0) ? { border: '1px solid red' } : {}}>
                                    <h6>Hazards Identification</h6>
                                    <p>Identify potential hazards associated with sub-activity</p>
                                </Accordion.Header>
                                <Accordion.Body>
                                    <div class="d-flex" style={{ border: '1px solid #E5E7EB' }}>

                                        <TabView
                                            activeIndex={activeTabIndex}
                                            onTabChange={(e) => setActiveTabIndex(e.index)}
                                            orientation="left"
                                            className='d-flex hazTabs'
                                        >
                                            {hazards.map((haz, h) => {
                                                return (
                                                    <TabPanel header={haz.name} className='tabsHead'>
                                                        <div className='row'>
                                                            {haz.hazards.map((ha, j) => {
                                                                return (
                                                                    <div className='col-4 mb-3'>
                                                                        <div className={`d-flex align-items-center hazClick ${item[1].selected.some(hazard => hazard.id === ha.id) ? 'active' : ''}`} onClick={() => onClickHazards(ha, j)}>
                                                                            <div className='col-2'>
                                                                                {item[1].selected.some(hazard => hazard.id === ha.id) ?
                                                                                    <Checkbox name='haz' checked></Checkbox>
                                                                                    :
                                                                                    <Checkbox name='haz'></Checkbox>
                                                                                }
                                                                            </div>
                                                                            <div className='col-2'>
                                                                                <img
                                                                                    src={
                                                                                        "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                        ha.image
                                                                                    }
                                                                                    style={{
                                                                                        height: 40,
                                                                                    }}
                                                                                    alt="sample"
                                                                                />
                                                                            </div>
                                                                            <div className='col-8 ms-3'>
                                                                                <p className='m-0'>{ha.name}</p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                )
                                                            })}
                                                        </div>
                                                    </TabPanel>
                                                )
                                            })}
                                        </TabView>
                                    </div>
                                </Accordion.Body>
                            </Accordion.Item>
                        </Accordion>
                        <h6 className='mt-4 mb-3'>Hazards Identified</h6>
                        <div className='row'>
                            {item[1].selected.map((item) => {
                                return (
                                    <div className='col-3 mb-3'>
                                        <div className='d-flex justify-content-between align-items-center p-2' style={{ border: '1px solid #E5E7EB', borderRadius: 8 }}>
                                            <img
                                                src={
                                                    "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                    item.image
                                                }
                                                style={{
                                                    height: 40,
                                                }}
                                                alt="sample"
                                            />
                                            <p>{item.name}</p>
                                            <i className='pi pi-times' onClick={() => onDeleteHaz(item)}></i>
                                        </div>


                                    </div>
                                )

                            })}

                        </div>


                    </>}
                    {item[8].step === 1 && <>
                        <h6>Consequences</h6>
                        <p>Identify the potential consequence on Satefy, Environment, Financial , Security and Community/ Brand Exposure due to presence of hazards</p>

                        {item[2].option.map((con, i) => {
                            return (<>

                                <div class="row mt-4 align-items-end">


                                    <div className='col-3'>
                                        <p className=''>Impact on</p>
                                        <Dropdown options={impactOn} value={con.current_type} onChange={(e) => onImapactOn(e.value, i, 'consequence')} className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`} />
                                    </div>
                                    <div className='col-8'>
                                        <InputText style={{ width: '100%' }} value={con.value} onChange={(e) => onConseqText(e.target.value, i, 'consequence')} className={`${(required === false && con.value === '') ? 'borderRed' : ''}`} />
                                    </div>
                                    <div className='col-1 text-center'>
                                        <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'consequence')}></i>
                                    </div>
                                </div>

                                <div className='col-12 mt-3'>
                                    <label htmlFor="username" className='mb-2'>Image Uploads</label>
                                    <div className="mb-3">
                                        <DropzoneArea
                                            acceptedFiles={[
                                                'image/jpeg',
                                                'image/png'
                                            ]}
                                            dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            onChange={handleFileChange}
                                            showPreviewsInDropzone={false}
                                            showPreviews={true}
                                            dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                        />
                                    </div>
                                </div>
                            </>

                            )
                        })}


                        <Button outlined label="Add Consequence" onClick={() => addConsequence('consequence')} />



                    </>}
                    {item[8].step === 2 && <>
                        <h6>Current Controls</h6>
                        <p>Existing measures in place to mitigate or manage the above identified hazards and potential consequnces.</p>
                        {item[3].option.map((con, i) => {
                            return (
                                <>
                                    <div class="row mt-4 align-items-end">


                                        <div className='col-3'>
                                            <p className=''>Type</p>
                                            <Dropdown options={control} value={con.current_type} onChange={(e) => onImapactOn(e.value, i, 'current_control')} className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`} />
                                        </div>
                                        <div className='col-8'>
                                            <InputText style={{ width: '100%' }} value={con.value} onChange={(e) => onConseqText(e.target.value, i, 'current_control')} className={`${(required === false && con.value === '') ? 'borderRed' : ''}`} />
                                        </div>
                                        <div className='col-1 text-center'>
                                            <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'current_control')}></i>
                                        </div>
                                    </div>

                                    <div className='col-12 mt-3'>
                                        <label htmlFor="username" className='mb-2'>Image Uploads</label>
                                        <div className="mb-3">
                                            <DropzoneArea
                                                acceptedFiles={[
                                                    'image/jpeg',
                                                    'image/png'
                                                ]}
                                                dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                onChange={handleFileChange}
                                                showPreviewsInDropzone={false}
                                                showPreviews={true}
                                                dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                            />
                                        </div>
                                    </div>
                                </>
                            )

                        })}


                        <Button outlined label="Add Current Control" onClick={() => addConsequence('current_control')} />



                    </>}
                    {item[8].step === 3 && <>
                        <h6>Risk Assessment</h6>
                        <p>Risk level based on the presence of the identified controls</p>

                        <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                            <div className='col-8'>
                                <h6>Severity</h6>
                                <p>Degree of harm or impact that could result from a hazardous event or situation</p>

                            </div>
                            <div className='col-4'>
                                <Dropdown className={`d-flex ${(required === false && item[4].severity === '') ? 'borderRed' : ''}`} options={severity} value={item[4].severity} onChange={(e) => onChangeSeverity(e, 'assessment')} />
                            </div>
                            <h6 className='mt-3 pointer' onClick={() => setSeverityTable(!severityTable)}> Understand Severity Levels <i className={`pi ${severityTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                            {severityTable &&
                                <div className='col-12 mt-3'>
                                    <div className="card">
                                        <DataTable value={severityData} className="table-bordered">
                                            <Column field="id" header=""></Column>
                                            <Column field="severity" header="Severity"></Column>
                                            <Column header="Personnel"></Column>
                                            <Column header="Property"></Column>
                                            <Column header="Environment"></Column>
                                            <Column header="Service Loss"></Column>
                                        </DataTable>
                                    </div>
                                </div>}

                        </div>
                        <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                            <div className='col-8'>
                                <h6>Likelihood</h6>
                                <p>Frequency with which a hazardous event or situation could happen</p>
                            </div>
                            <div className='col-4'>
                                <Dropdown className={`d-flex ${(required === false && item[4].severity === '') ? 'borderRed' : ''}`} options={likelyhood} optionLabel="label" value={item[4].likelyhood} onChange={(e) => onChangeLikelyhood(e, 'assessment')} />
                            </div>

                            <h6 className='mt-3 pointer' onClick={() => setLikelyhoodTable(!likelyhoodTable)}> Understand Likelihood Levels <i className={`pi ${likelyhoodTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                            {likelyhoodTable &&
                                <div className='col-12 mt-3'>
                                    <div className="card">
                                        <DataTable value={levelData} className="table-bordered" rowClassName={rowClassName}>
                                            <Column field="level" header="Level"></Column>
                                            <Column field="descriptor" header="Descriptor"></Column>
                                            <Column field="detailedDescription" header="Detailed Description"></Column>
                                        </DataTable>
                                    </div>
                                </div>}

                        </div>
                        <div class="row mt-4 mb-3 pb-4" >


                            <div className='col-8'>
                                <h6>Risk Level</h6>
                                <p></p>
                            </div>
                            <div className='col-4'>
                                <div className={`boxShadow p-2 text-center fw-bold ${cellClassName(item[4].severity * item[4].likelyhood)}`}>
                                    {item[4].severity * item[4].likelyhood}
                                </div>
                            </div>
                            <h6 className='mt-3 pointer' onClick={() => setRiskTable(!riskTable)}> Understand Risk Levels <i className={`pi ${riskTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                            {riskTable &&
                                <div className='col-12 mt-3'>
                                    <div className="card">
                                        <DataTable value={tableData} className="table-bordered">
                                            <Column field="id" header=""></Column>
                                            <Column field="severity" header=""></Column>
                                            <Column field="rare" header="1 (A) Rare" bodyClassName={(data) => cellStyle(data, 'rare')}></Column>
                                            <Column field="unlikely" header="2 (B) Unlikely" bodyClassName={(data) => cellStyle(data, 'unlikely')}></Column>
                                            <Column field="possible" header="3 (C) Possible" bodyClassName={(data) => cellStyle(data, 'possible')}></Column>
                                            <Column field="likely" header="4 (D) Likely" bodyClassName={(data) => cellStyle(data, 'likely')}></Column>
                                            <Column field="almostCertain" header="5 (E) Almost Certain" bodyClassName={(data) => cellStyle(data, 'almostCertain')}></Column>
                                        </DataTable>
                                    </div>
                                </div>}
                        </div>

                        <h5 className="mb-4 fw-bold ">Risk Level</h5>
                        <div className='row mb-4 '>
                            <div className='col-4 d-flex align-items-center '>
                                <InputSwitch checked={item[5].accept} onChange={(e) => onChangeReAss(e.value)} /> <span className='bold ms-2'>This Risk Level is acceptable</span>
                            </div>

                        </div>






                    </>}
                    {item[8].step === 4 && <>

                        <div className=' '>

                            <h5 className="mb-4">Proposed Additional Controls</h5>
                            {item[6].option.map((item, i) => {
                                return (
                                    <div className='row pb-4 mt-3' >

                                        <div className='col-12 mb-4'>
                                            <div className='row'>
                                                <div className='col-4'>
                                                    <Dropdown className='d-flex'
                                                        options={control} value={item.current_type} onChange={(e) => onControlAddion(e.value, i)} />
                                                </div>
                                                <div className='col-7'>
                                                    <InputText style={{ width: '100%' }} value={item.value} onChange={(e) => onControlAddionText(e.target.value, i)} />
                                                </div>
                                                <div className='col-1 text-center'>
                                                    <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'responsibility')}></i>
                                                </div>
                                            </div>

                                        </div>
                                        <div className='col-8 '>
                                            <div className='row'>
                                                <div className='col-6'>
                                                    <Dropdown optionLabel="name" className='d-flex' options={responsibility} onChange={(e) => onResponsePerson(e.value, i)} value={item.person} filter />
                                                </div>
                                                <div className='col-6'>
                                                    <Calendar value={item.date} onChange={(e) => onResponseDate(e.value, i)} dateFormat="dd/mm/yy" placeholder='Date' className='d-flex' />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )
                            })}

                            <div className='row '>
                                <div className='col-4'>
                                    <Button label="Add Additional Control" outlined className='d-flex' onClick={() => addAdditionalControl()} />
                                </div>
                            </div>



                        </div>
                        <h5 className='mt-4'>Residual Risk Assessment</h5>
                        <p>Expected risk based on the implementation of the identified additional controls</p>

                        <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                            <div className='col-8'>
                                <h6>Severity</h6>
                                <p>Degree of harm or impact that could result from a hazardous event or situation</p>

                            </div>
                            <div className='col-4'>
                                <Dropdown options={severity} className='d-flex' value={item[7].severity} onChange={(e) => onChangeSeverity(e, 'reassessment')} />
                            </div>
                            <h6 className='mt-3 pointer' onClick={() => setSeverityTable(!severityTable)}> Understand Severity Levels <i className={`pi ${severityTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                            {severityTable &&
                                <div className='col-12 mt-3'>
                                    <div className="card">
                                        <DataTable value={severityData} className="table-bordered">
                                            <Column field="id" header=""></Column>
                                            <Column field="severity" header="Severity"></Column>
                                            <Column header="Personnel"></Column>
                                            <Column header="Property"></Column>
                                            <Column header="Environment"></Column>
                                            <Column header="Service Loss"></Column>
                                        </DataTable>
                                    </div>
                                </div>}

                        </div>
                        <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                            <div className='col-8'>
                                <h6>Likelihood</h6>
                                <p>Frequency with which a hazardous event or situation could happen</p>
                            </div>
                            <div className='col-4'>
                                <Dropdown options={likelyhood} className='d-flex' value={item[7].likelyhood} onChange={(e) => onChangeLikelyhood(e, 'reassessment')} />
                            </div>

                            <h6 className='mt-3 pointer' onClick={() => setLikelyhoodTable(!likelyhoodTable)}> Understand Likelihood Levels <i className={`pi ${likelyhoodTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                            {likelyhoodTable &&
                                <div className='col-12 mt-3'>
                                    <div className="card">
                                        <DataTable value={levelData} className="table-bordered" rowClassName={rowClassName}>
                                            <Column field="level" header="Level"></Column>
                                            <Column field="descriptor" header="Descriptor"></Column>
                                            <Column field="detailedDescription" header="Detailed Description"></Column>
                                        </DataTable>
                                    </div>
                                </div>}

                        </div>
                        <div class="row mt-4 mb-3 pb-4" >


                            <div className='col-8'>
                                <h6>Risk Level</h6>
                                <p></p>
                            </div>
                            <div className='col-4'>
                                <div className={`boxShadow p-2 text-center fw-bold ${cellClassName(item[7].severity * item[7].likelyhood)}`}>
                                    {item[7].severity * item[7].likelyhood}
                                </div>
                            </div>
                            <h6 className='mt-3 pointer' onClick={() => setRiskTable(!riskTable)}> Understand Risk Levels <i className={`pi ${riskTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                            {riskTable &&
                                <div className='col-12 mt-3'>
                                    <div className="card">
                                        <DataTable value={tableData} className="table-bordered">
                                            <Column field="id" header=""></Column>
                                            <Column field="severity" header=""></Column>
                                            <Column field="rare" header="1 (A) Rare" bodyClassName={(data) => cellStyle(data, 'rare')}></Column>
                                            <Column field="unlikely" header="2 (B) Unlikely" bodyClassName={(data) => cellStyle(data, 'unlikely')}></Column>
                                            <Column field="possible" header="3 (C) Possible" bodyClassName={(data) => cellStyle(data, 'possible')}></Column>
                                            <Column field="likely" header="4 (D) Likely" bodyClassName={(data) => cellStyle(data, 'likely')}></Column>
                                            <Column field="almostCertain" header="5 (E) Almost Certain" bodyClassName={(data) => cellStyle(data, 'almostCertain')}></Column>
                                        </DataTable>
                                    </div>
                                </div>}
                        </div>

                    </>}

                </Dialog>
            </>}
        </div>


    )
}

export default Routine