import React, { useState, useEffect } from "react";
import { Mo<PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import Select from "react-select";

import { AIR_COST_REVIEWER_URL, AIR_COST_REVIEWER_WITH_ID_URL, AIR_COST_REVIEWER_RETURN_WITH_ID_URL, AIR_DUTY_MANAGER_ESTIMATION_WITH_ID_URL, AIR_FINANCE_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';
import { DropzoneArea } from 'material-ui-dropzone';
import Grid from '@mui/material/Grid'; // Import Grid
import FormControl from '@mui/material/FormControl'; // Import FormControl
import FormLabel from '@mui/material/FormLabel'; // Import FormLabel
import RadioGroup from '@mui/material/RadioGroup'; // Import RadioGroup
import FormControlLabel from '@mui/material/FormControlLabel'; // Import FormControlLabel
import Radio from '@mui/material/Radio'; // Import Radio
import TextField from '@mui/material/TextField'; // Import TextField
import InvoiceComponent from "./InvoiceComponent";

const AirCostEstimatorViewCard = ({ showModal, setShowModal, data, setData }) => {

    const [showNext, setShowNext] = useState(false);
    const [showLeftPane, setShowLeftPane] = useState(true);

    const [combinedInvoiceItems, setCombinedInvoiceItems] = useState([]);
    const [damagedData, setDamagedData] = useState({})
    const handleUpdateApiData = (updatedApiData) => {
        setApiData(updatedApiData);
    };
    const handleAddCombinedInvoiceItem = (item, title) => {
        setCombinedInvoiceItems([...combinedInvoiceItems, { ...item, title }]);
    };

    const [apiData, setApiData] = useState(data.costEstimation);

    const [financer, setFinancer] = useState([])
    useEffect(() => {
        getFinancer()
        console.log(data)
    }, [])

    const getFinancer = async () => {
        const response = await API.post(AIR_FINANCE_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setFinancer(response.data)
        }
    }

    const [selectedFinancer, setSelectedFinancer] = useState('');
    const [costReviewerComments, setCostReviewerComments] = useState('');

    const handleSubmit = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_COST_REVIEWER_WITH_ID_URL(data.id, data.actionId), {

                financerId: selectedFinancer,
                costReviewerComments: costReviewerComments
            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };

    const handleReturn = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_COST_REVIEWER_RETURN_WITH_ID_URL(data.id, data.actionId), {


            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Returned`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };
    const [files, setFiles] = useState([]);
    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {
        if (data?.costEstimation) {


            setDamagedData(data.costEstimation)
            setCostReviewerComments(data.costReviewerComments)
        }
    }, [data]);

    return (
        <>
            {data &&
                <div className="row">



                    <div className={`col-md-12`}>
                        <Box>

                            <div className="row">
                                <div className="table-responsive">
                                    <label>Cost Estimation</label>

                                    <div className="col-md-12">
                                        <div className="row">
                                            {damagedData?.length > 0 && (
                                                damagedData.map((item, index) => (
                                                    <Accordion key={index} TransitionProps={{ unmountOnExit: true }} className="mb-3" >
                                                        <AccordionSummary
                                                            expandIcon={<ExpandMoreIcon />}
                                                            aria-controls={`panel${index}a-content`}
                                                            id={`panel${index}a-header`}

                                                        >
                                                            <Typography><strong>Equipment #{index + 1}:</strong> {item.category}</Typography>
                                                            <Typography style={{ marginLeft: 'auto', opacity: 0.7 }}>Click to expand</Typography>
                                                        </AccordionSummary>
                                                        <AccordionDetails>
                                                            <Box sx={{ width: '100%' }}>
                                                                <Typography variant="body2" paragraph>
                                                                    Detailed information for <strong>{item.category}</strong>:
                                                                </Typography>
                                                                <Grid container spacing={2}>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Number:</strong> {item.number}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Damage Type:</strong> {item.damageType}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6}>
                                                                        <Typography><strong>Cost Details:</strong> {item.costDetails}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Work Order Number:</strong> {item.workOrderNumber}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <Typography><strong>Operational:</strong> {item.operational ? "Yes" : "No"}</Typography>
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6}>

                                                                    </Grid>
                                                                    <Grid item xs={6} sm={3}>
                                                                        <TextField
                                                                            label="Inspection Date"
                                                                            type="date"
                                                                            value={damagedData[index].inspectionDate}
                                                                            style={{ color: 'black' }}
                                                                            disabled={true}
                                                                            InputLabelProps={{ shrink: true }}
                                                                            fullWidth
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={9}>
                                                                        <TextField
                                                                            label="Inspection Remarks"
                                                                            type="text"
                                                                            value={damagedData[index].inspectionRemarks}
                                                                            style={{ color: 'black' }}
                                                                            disabled={true}
                                                                            variant="outlined"
                                                                            fullWidth
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12}>
                                                                        <InvoiceComponent editable={false} costData={damagedData[index].costEstimation} />
                                                                    </Grid>
                                                                </Grid>
                                                            </Box>
                                                        </AccordionDetails>
                                                    </Accordion>
                                                ))
                                            )}
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label>Comments</label>
                                        <p>{costReviewerComments}</p>
                                    </div>
                                </div>
                            </div>

                            {/* <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <DropzoneArea
                                            acceptedFiles={[
                                                'application/pdf',
                                                'image/jpeg',
                                                'image/png'

                                            ]}
                                            dropzoneText={"Drag and drop files / documents / pictures"}
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            onChange={handleFileChange}
                                        />
                                    </div>
                                </div>
                            </div> */}


                        </Box>
                    </div>


                </div>



            }
        </>
    )
}

export default AirCostEstimatorViewCard;