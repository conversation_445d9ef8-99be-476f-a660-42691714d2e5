// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { EHS_ROLE_URL, EPTW_ROLE_URL, EXTERNAL_USERS_URL, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, INTERNAL_USERS_URL, LOCATION1_URL, PLANT_ROLE_URL, USERS_URL, USERS_URL_WITH_ID } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const AppUser = () => {

    const [mdShow, setMdShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [userShow, setUserShow] = useState(false);
    const history = useHistory();
    const uName = useRef();
    const uEmail = useRef();
    const uPassword = useRef();
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "" });
    useEffect(() => {
        getCountry();
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getPlantRole();

    }, [])


    const getCountry = async () => {
        const response = await API.get(LOCATION1_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, country: response.data } })
        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, inspection: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, plant: response.data } })
        }
    }
    const uRole = useRef();

    const thead = [
        'Name',
        'Email',
        'Type',
        'Actions',

    ];






    $(document).on('click', '.permission-assign-user', async function (e) {

        // setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
        const id = $(this).data('id');
        const email = $(this).data('email');

        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
            setSelectedUserId({ id: id, email: email })
            setMdShow(true)

        }






    })




    const options = {


        "ajax": {
            url: USERS_URL,
            type: "GET",
            "headers": {
                "Authorization": "Bearer " + localStorage.getItem("access_token"),

            },
            dataSrc: '',
            "error": function (xhr, error, thrown) {
                try {
                    console.log(xhr.responseText, 'here');

                    if (xhr.status === 401) {
                        history.push('/logout')
                    }

                    // alert("An error occurred while fetching data. Please try again later.");
                } catch (e) {
                    console.log(e);
                }
            }
        },

        "columns": [{
            "data": null,
            "render": function (data, type, full, meta) {
                return `<div data-column="username" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.firstName ? data.firstName : ''} </div>`;

            }
        },
        {
            "data": null,
            "render": function (data, type, full, meta) {
                return `<div data-column="email" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.email ? data.email : ''} </div>`;

            }
        },
        {
            "data": null,
            "render": function (data, type, full, meta) {
                return `<div data-column="type" data-id="${data.id}" class="edit-inline text-wrap width-200" contenteditable="true"> ${data.type ? data.type : ''} </div>`;

            }
        },


        {
            "data": null,
            "render": function (data, type, full, meta) {

                return `<div style="font-size: 22px;">
                                    <i class="mdi mdi-text-box-check-outline text-danger permission-assign-user cursor-pointer" data-id="${data.id}" data-email="${data.email}" data-name="${data.firstName}" ></i></div>`;
            }


        },
        ]
    }

    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)

        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category];
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        const response = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response.status === 204) {
            setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
            setSelectedUserId({ id: "", email: "" })
            setMdShow(false)
            cogoToast.info('Assigned', { position: 'top-right' })

        }
    }
    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)

        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: uName.current.value,
            email: uEmail.current.value,
            password: uPassword.current.value,


        })
        if (response.status === 200) {

            cogoToast.info('Created!', { position: 'top-right' })
            $('#dataTable').DataTable().ajax.reload();
            customSwal2.fire(
                'User Created!',
                '',
                'success'
            )
        } else {
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }



        uName.current.value = '';
        uEmail.current.value = '';
        uPassword.current.value = '';
        setUserShow(false)
        setIsLoading(false)
    }
    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">

                                <h4 className="card-title">Application User Management</h4>
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            {/* <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button> */}
                                            <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setUserShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button>
                                           
                                            <DataTables thead={thead} options={options} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Modal
                show={mdShow}
                size="md"
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Assign Permissions
                </Modal.Header>

                <Modal.Body>
                    <form className="forms">

                        <h4>Country Admin</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.country.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.country.includes(i.id)} onChange={(e) => handleRoleChange(e, 'country')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>EHS Observation</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.ehs.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.ehs.includes(i.id)} onChange={(e) => handleRoleChange(e, 'ehs')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>ePermit to Work</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.eptw.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.eptw.includes(i.id)} onChange={(e) => handleRoleChange(e, 'eptw')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>Incident Reporting</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.incident.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.incident.includes(i.id)} onChange={(e) => handleRoleChange(e, 'incident')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>Inspection and Audit</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.inspection.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.inspection.includes(i.id)} onChange={(e) => handleRoleChange(e, 'inspection')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>Plant and Equipment</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.plant.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.plant.includes(i.id)} onChange={(e) => handleRoleChange(e, 'plant')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>

                        {selectedUserId.id && <Button variant="primary" onClick={handleAssignSubmit}>Assign</Button>}

                    </>


                </Modal.Footer>
            </Modal>
            <Modal
                show={userShow}
                onHide={() => setUserShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="user_name" >Name</label>
                            <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_category" >Email</label>
                            <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_description" >Temporary Password</label>
                            <Form.Control type="password" ref={uPassword} id="user_description" placeholder="Enter Password" />
                        </div>



                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setUserShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={createUserHandler}>Create</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>
        </>
    )
}


export default AppUser;
