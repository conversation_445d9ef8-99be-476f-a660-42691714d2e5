import React, { useState } from "react";
import ToolBox from "./components/ToolBox";
import FormContainer from "./components/FormContainer";
import {
  STEP_BY_ID,
  CHECKLIST_WITH_ID_URL
} from "../constants";
import API from "../services/API";
import { singlePopup } from "./../notifications/Swal";
import { useLocation ,useHistory} from "react-router-dom/cjs/react-router-dom.min";
const CHCuration = (props) => {
  console.log(props);
  const location =useLocation();
  const history =useHistory()
  const [isLoading, setIsLoading] = useState(true);
  const [checklistData, setChecklistData] = useState({ name: "", value: {} });

  const myForm = async (form,type) => {
    console.log(form);
    let url = "";
    if (location.state.type === "checklist") {
      url = CHECKLIST_WITH_ID_URL(location.state.id);
    } 
    const data = JSON.stringify(form);

    const response = await fetch(url, {
      method: "PATCH",
      body: JSON.stringify({
        value: data,
      }),
      headers: {
        "Content-type": "application/json; charset=UTF-8",
      },
    });
    if (response.ok) {
      singlePopup.fire("Saved!", "", "success");
      if(type ==='exit'){
        history.goBack()
     }
    } else {
      //show error
      singlePopup.fire("Please Try Again", "", "error");
    }
  };
  const updateForm = async (callback) => {
    let url = "";
    if (location.state.type === "checklist") {
      url = CHECKLIST_WITH_ID_URL(location.state.id);
    } 
    const response = await API.get(
     url
    );
    if (response.status === 200) {
      let form = JSON.parse(response.data.value);
      console.log(form);
      callback(form);
      setChecklistData(response.data);
      setIsLoading(false);
    }
  };
  const goBack =()=>{
    history.goBack();
 }
  return (
    <div className="row">
      <div className="col-12">
        <div className="card">
          <div className="card-body p-0">
            <div className="row">
              <div className="col-md-9 pr-0">
                <FormContainer
                  loader={false}
                  debug={false}
                  updateOnMount={true}
                  updateForm={updateForm}
                  onSave={myForm}
                  goBack ={goBack}
                />
              </div>
              <div
                className="col-md-3 pl-0">
                <ToolBox />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CHCuration;
