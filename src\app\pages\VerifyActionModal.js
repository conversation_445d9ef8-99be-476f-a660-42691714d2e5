import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { API_URL, GET_USER_ROLE_BY_MODE, REPORT_INCIDENT_ACTIONS_WITH_ID, STATIC_URL } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import MedicalHRCard from "./MedicalHRCard";

const VerifyActionModal = ({ data, applicationType, showModal, setShowModal }) => {

    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])

    const comments = useRef();

    const actionTaken = useRef();

    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {
        if (data.applicationDetails.locationOneId && data.applicationDetails.locationTwoId && data.applicationDetails.locationThreeId && data.applicationDetails.locationFourId) {

            getObsUsers();


        }

    }, [])

    const getObsUsers = async () => {
        const response = await API.post(GET_USER_ROLE_BY_MODE, { locationOneId: data.applicationDetails.locationOneId, locationTwoId: data.applicationDetails.locationTwoId, locationThreeId: data.applicationDetails.locationThreeId, locationFourId: data.applicationDetails.locationFourId, mode: 'ir-action-verifier' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }
    const handleSubmit = async (type) => {
        if (!comments.current.value) {
            cogoToast.error('Please enter your comments!')
            return;
        }

        let url = REPORT_INCIDENT_ACTIONS_WITH_ID(data.id);
        let actionType = type === 'approve' ? 'approve' : 'reject';



        const response = await API.patch(url, {
            actionType: actionType,
            comments: comments.current.value,
            actionTaken: '',
            assignedToId: data.assignedToId,
            actionToBeTaken: data.actionToBeTaken,
            objectId: data.objectId,
            description: data.description,
            dueDate: data.dueDate,
            uploads: data.uploads,
            createdDate: data.createdDate

        })

        if (response.status === 204) {
            cogoToast.success('Submitted!')
            setShowModal(false)
        }
    }


    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>

                        Verify Implementation -
                        {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>}
                        {applicationType === 'AIR' && `${data.applicationDetails.maskId}`}

                    </div>

                </Modal.Header>

                <Modal.Body>

                    <Box>
                        {data.applicationDetails && <div className="container">
                            <div className="card">

                                <div className="card-body">
                                    {
                                        data.description === 'HR' && <MedicalHRCard title="Person Involved" data={data.applicationDetails.personInvolved} />
                                    }

                                    {
                                        data.description === 'HR' && <MedicalHRCard title="Personnel Impacted" data={data.applicationDetails.personnelImpacted} />
                                    }
                                    {/* {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>} */}

                                    <div className="mb-3">
                                        <label className="form-label">Location</label>
                                        <input type="text" className="form-control" value={`${data.applicationDetails.locationThree && data.applicationDetails.locationThree.name} > ${data.applicationDetails.locationFour && data.applicationDetails.locationFour.name}`} readOnly />
                                    </div>



                                    {
                                        (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && <div className="mb-3">
                                            <label className="form-label">Uploads</label>
                                            <div className="border p-3 row">
                                                {
                                                    (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && data.applicationDetails.uploads.map(i => (
                                                        <div className="col-md-3">
                                                            <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                        </div>

                                                    ))
                                                }

                                            </div>
                                        </div>
                                    }

                                    {(applicationType === 'AIR' && data.applicationDetails.description) && <div className="mb-3">
                                        <label className="form-label">Description of Incident</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.applicationDetails.description}</textarea>
                                    </div>}

                                    {
                                        (!applicationType === 'AIR' && data.description) && <div className="mb-3">
                                            <label className="form-label">Description</label>
                                            <textarea className="form-control" rows="3" readOnly>{data.description}</textarea>
                                        </div>
                                    }
                                    {(applicationType === 'AIR' && data.additionalComments) && <div className="mb-3">
                                        <label className="form-label">Additional Information</label>
                                        <textarea disabled className="form-control" rows="3" readOnly>{data.additionalComments}</textarea>
                                    </div>}
                                    {(data.uploads && data.uploads.length > 0) && <div className="mb-3">
                                        <label className="form-label">Evidence</label>
                                        <div className="border p-3 row">
                                            {
                                                (data.uploads && data.uploads.length > 0) && data.uploads.map(i => (
                                                    <div className="col-md-3">
                                                        <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                    </div>

                                                ))
                                            }

                                        </div>
                                    </div>
                                    }
                                    {data.actionToBeTaken && <div className="mb-3">
                                        <label className="form-label">Actions to be taken</label>
                                        <textarea className="form-control" rows="3" value={data.actionToBeTaken} readOnly></textarea>
                                    </div>}

                                    <div className="mb-3">
                                        <label className="form-label">Action taken</label>
                                        <textarea value={data.actionTaken} className="form-control" rows="3" readOnly></textarea>
                                    </div>


                                    <div className="mb-3">
                                        <label className="form-label">Comments</label>
                                        <textarea ref={comments} className="form-control" rows="3" required></textarea>
                                    </div>


                                </div>
                            </div>
                        </div>}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">


                    <Button
                        variant="success"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit('approve')}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Approve
                    </Button>
                    <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit('reject')}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Return
                    </Button>

                    <Button
                        variant="light"
                        onClick={() => setShowModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>
        </>
    )
}

export default VerifyActionModal;