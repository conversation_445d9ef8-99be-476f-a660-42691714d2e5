import moment from 'moment';

/**
 * Chart color palette for consistent styling
 */
export const CHART_COLORS = {
  primary: '#007bff',
  success: '#28a745',
  danger: '#dc3545',
  warning: '#ffc107',
  info: '#17a2b8',
  secondary: '#6c757d',
  light: '#f8f9fa',
  dark: '#343a40',
  purple: '#6f42c1',
  pink: '#e83e8c',
  orange: '#fd7e14',
  teal: '#20c997',
  cyan: '#17a2b8',
  indigo: '#6610f2'
};

/**
 * Status color mapping for observations
 */
export const STATUS_COLORS = {
  'Actions Assigned': CHART_COLORS.warning,
  'Actions Taken - Pending Verification': CHART_COLORS.info,
  'Action Verified - Closed': CHART_COLORS.success,
  'Reported & Closed': CHART_COLORS.success,
  'Reported & Rectified on Spot': CHART_COLORS.success,
  'Action Reassigned': CHART_COLORS.orange,
  'Archived without actions': CHART_COLORS.secondary,
  'Under Review': CHART_COLORS.primary
};

/**
 * Severity color mapping
 */
export const SEVERITY_COLORS = {
  'High': CHART_COLORS.danger,
  'Medium': CHART_COLORS.warning,
  'Low': CHART_COLORS.success
};

/**
 * Category color mapping
 */
export const CATEGORY_COLORS = {
  'Safety': CHART_COLORS.danger,
  'Health': CHART_COLORS.success,
  'Environment': CHART_COLORS.info
};

/**
 * Type color mapping
 */
export const TYPE_COLORS = {
  'Unsafe Act': CHART_COLORS.danger,
  'Unsafe Condition': CHART_COLORS.warning,
  'Positive': CHART_COLORS.success
};

/**
 * Process observation data for status distribution chart
 */
export const processStatusData = (data) => {
  const statusCounts = {};
  
  data.forEach(item => {
    const status = item.status || 'Unknown';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.entries(statusCounts).map(([name, y]) => ({
    name,
    y,
    color: STATUS_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for category distribution chart
 */
export const processCategoryData = (data) => {
  const categoryCounts = {};
  
  data.forEach(item => {
    const category = item.category || 'Unknown';
    categoryCounts[category] = (categoryCounts[category] || 0) + 1;
  });

  return Object.entries(categoryCounts).map(([name, y]) => ({
    name,
    y,
    color: CATEGORY_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for type distribution chart
 */
export const processTypeData = (data) => {
  const typeCounts = {};
  
  data.forEach(item => {
    const type = item.type || 'Unknown';
    typeCounts[type] = (typeCounts[type] || 0) + 1;
  });

  return Object.entries(typeCounts).map(([name, y]) => ({
    name,
    y,
    color: TYPE_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for severity distribution chart
 */
export const processSeverityData = (data) => {
  const severityCounts = {};
  
  data.forEach(item => {
    const severity = item.severity || 'Unknown';
    severityCounts[severity] = (severityCounts[severity] || 0) + 1;
  });

  return Object.entries(severityCounts).map(([name, y]) => ({
    name,
    y,
    color: SEVERITY_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for department-wise analysis
 */
export const processDepartmentData = (data) => {
  const departmentCounts = {};
  
  data.forEach(item => {
    const department = item.workActivityDepartment?.name || 'Unknown';
    departmentCounts[department] = (departmentCounts[department] || 0) + 1;
  });

  return Object.entries(departmentCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 departments
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process observation data for monthly trends
 */
export const processMonthlyTrends = (data) => {
  const monthlyData = {};
  
  data.forEach(item => {
    const date = moment(item.created, ['Do MMM YYYY', 'DD-MM-YYYY', moment.ISO_8601]);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    }
  });

  // Get last 12 months
  const months = [];
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  return {
    categories: months,
    data: months.map(month => monthlyData[month] || 0)
  };
};

/**
 * Process observation data for action completion rates
 */
export const processActionCompletionData = (data) => {
  const completedStatuses = [
    'Action Verified - Closed',
    'Reported & Closed',
    'Reported & Rectified on Spot',
    'Archived without actions'
  ];

  const total = data.length;
  const completed = data.filter(item => completedStatuses.includes(item.status)).length;
  const pending = total - completed;

  return [
    { name: 'Completed', y: completed, color: CHART_COLORS.success },
    { name: 'Pending', y: pending, color: CHART_COLORS.warning }
  ];
};

/**
 * Process observation data for overdue analysis
 */
export const processOverdueData = (data) => {
  const currentDate = moment();
  let overdue = 0;
  let dueSoon = 0; // Due within 7 days
  let upcoming = 0;
  let noDueDate = 0;

  data.forEach(item => {
    if (!item.dueDate || item.dueDate === '') {
      noDueDate++;
    } else {
      const dueDate = moment(item.dueDate, 'DD-MM-YYYY');
      if (dueDate.isBefore(currentDate)) {
        overdue++;
      } else if (dueDate.diff(currentDate, 'days') <= 7) {
        dueSoon++;
      } else {
        upcoming++;
      }
    }
  });

  return [
    { name: 'Overdue', y: overdue, color: CHART_COLORS.danger },
    { name: 'Due Soon (≤7 days)', y: dueSoon, color: CHART_COLORS.warning },
    { name: 'Upcoming', y: upcoming, color: CHART_COLORS.info },
    { name: 'No Due Date', y: noDueDate, color: CHART_COLORS.secondary }
  ];
};

/**
 * Default chart options for consistent styling
 */
export const getDefaultChartOptions = (title, subtitle = '') => ({
  chart: {
    backgroundColor: 'transparent',
    style: {
      fontFamily: 'Lato, sans-serif'
    }
  },
  title: {
    text: title,
    style: {
      fontSize: '18px',
      fontWeight: 'bold',
      color: '#333'
    }
  },
  subtitle: {
    text: subtitle,
    style: {
      fontSize: '14px',
      color: '#666'
    }
  },
  credits: {
    enabled: false
  },
  exporting: {
    enabled: true,
    buttons: {
      contextButton: {
        menuItems: ['downloadPNG', 'downloadJPEG', 'downloadPDF', 'downloadSVG']
      }
    }
  },
  responsive: {
    rules: [{
      condition: {
        maxWidth: 500
      },
      chartOptions: {
        legend: {
          layout: 'horizontal',
          align: 'center',
          verticalAlign: 'bottom'
        }
      }
    }]
  }
});

/**
 * Pie chart specific options
 */
export const getPieChartOptions = (title, subtitle = '') => ({
  ...getDefaultChartOptions(title, subtitle),
  chart: {
    ...getDefaultChartOptions().chart,
    type: 'pie'
  },
  tooltip: {
    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b> ({point.y})'
  },
  accessibility: {
    point: {
      valueSuffix: '%'
    }
  },
  plotOptions: {
    pie: {
      allowPointSelect: true,
      cursor: 'pointer',
      dataLabels: {
        enabled: true,
        format: '<b>{point.name}</b>: {point.percentage:.1f} %'
      },
      showInLegend: true
    }
  }
});

/**
 * Column chart specific options
 */
export const getColumnChartOptions = (title, subtitle = '', yAxisTitle = 'Count') => ({
  ...getDefaultChartOptions(title, subtitle),
  chart: {
    ...getDefaultChartOptions().chart,
    type: 'column'
  },
  xAxis: {
    type: 'category',
    labels: {
      rotation: -45,
      style: {
        fontSize: '13px',
        fontFamily: 'Lato, sans-serif'
      }
    }
  },
  yAxis: {
    min: 0,
    title: {
      text: yAxisTitle
    }
  },
  tooltip: {
    headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
      '<td style="padding:0"><b>{point.y}</b></td></tr>',
    footerFormat: '</table>',
    shared: true,
    useHTML: true
  },
  plotOptions: {
    column: {
      pointPadding: 0.2,
      borderWidth: 0,
      dataLabels: {
        enabled: true
      }
    }
  }
});

/**
 * Line chart specific options
 */
export const getLineChartOptions = (title, subtitle = '', yAxisTitle = 'Count') => ({
  ...getDefaultChartOptions(title, subtitle),
  chart: {
    ...getDefaultChartOptions().chart,
    type: 'line'
  },
  xAxis: {
    categories: []
  },
  yAxis: {
    title: {
      text: yAxisTitle
    }
  },
  tooltip: {
    valueSuffix: ' observations'
  },
  plotOptions: {
    line: {
      dataLabels: {
        enabled: true
      },
      enableMouseTracking: true
    }
  }
});

// ============ INCIDENT ANALYTICS UTILITIES ============

/**
 * Incident severity color mapping
 */
export const INCIDENT_SEVERITY_COLORS = {
  'H1C1': CHART_COLORS.danger,
  'H1C2': CHART_COLORS.danger,
  'H1C3': CHART_COLORS.danger,
  'H2C1': CHART_COLORS.warning,
  'H2C2': CHART_COLORS.warning,
  'H2C3': CHART_COLORS.warning,
  'H3C1': CHART_COLORS.info,
  'H3C2': CHART_COLORS.info,
  'H3C3': CHART_COLORS.info,
  'H4C1': CHART_COLORS.success,
  'H4C2': CHART_COLORS.success,
  'H4C3': CHART_COLORS.success,
  'H4C4': CHART_COLORS.success
};

/**
 * Incident status color mapping
 */
export const INCIDENT_STATUS_COLORS = {
  'Open': CHART_COLORS.warning,
  'Under Investigation': CHART_COLORS.info,
  'Closed': CHART_COLORS.success,
  'Pending': CHART_COLORS.orange,
  'Draft': CHART_COLORS.secondary
};

/**
 * Process incident data for severity distribution chart
 */
export const processIncidentSeverityData = (data) => {
  const severityCounts = {};

  data.forEach(item => {
    const severity = item.incidentRating?.item || 'Unknown';
    // Convert format from C1H1 to H1C1 if needed
    const formattedSeverity = severity.replace(/C(\d+)H(\d+)/, "H$2C$1");
    severityCounts[formattedSeverity] = (severityCounts[formattedSeverity] || 0) + 1;
  });

  return Object.entries(severityCounts).map(([name, y]) => ({
    name,
    y,
    color: INCIDENT_SEVERITY_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process incident data for status distribution chart
 */
export const processIncidentStatusData = (data) => {
  const statusCounts = {};

  data.forEach(item => {
    const status = item.status || 'Unknown';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.entries(statusCounts).map(([name, y]) => ({
    name,
    y,
    color: INCIDENT_STATUS_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process incident data for reported status distribution
 */
export const processIncidentReportedStatusData = (data) => {
  const reportedCounts = {};

  data.forEach(item => {
    const status = item.isReported === 'Yes' ? 'Reported' :
                   item.isReported === 'No' ? 'Unreported' :
                   item.isReported || 'Unknown';
    reportedCounts[status] = (reportedCounts[status] || 0) + 1;
  });

  return Object.entries(reportedCounts).map(([name, y]) => ({
    name,
    y,
    color: name === 'Reported' ? CHART_COLORS.success :
           name === 'Unreported' ? CHART_COLORS.danger :
           CHART_COLORS.secondary
  }));
};

/**
 * Process incident data for day/night distribution
 */
export const processIncidentDayNightData = (data) => {
  const dayNightCounts = {};

  data.forEach(item => {
    const date = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
    if (date.isValid()) {
      const hour = date.hour();
      const dayNight = hour >= 6 && hour < 18 ? 'Day' : 'Night';
      dayNightCounts[dayNight] = (dayNightCounts[dayNight] || 0) + 1;
    }
  });

  return Object.entries(dayNightCounts).map(([name, y]) => ({
    name,
    y,
    color: name === 'Day' ? CHART_COLORS.warning : CHART_COLORS.dark
  }));
};

/**
 * Process incident data for group-wise analysis
 */
export const processIncidentGroupData = (data) => {
  const groupCounts = {};

  data.forEach(item => {
    const group = item.workingGroup?.name || 'Unknown';
    groupCounts[group] = (groupCounts[group] || 0) + 1;
  });

  return Object.entries(groupCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 groups
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process incident data for area-wise analysis
 */
export const processIncidentAreaData = (data) => {
  const areaCounts = {};

  data.forEach(item => {
    const area = item.locationThree?.name || 'Unknown';
    areaCounts[area] = (areaCounts[area] || 0) + 1;
  });

  return Object.entries(areaCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 areas
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process incident data for monthly trends
 */
export const processIncidentMonthlyTrends = (data) => {
  const monthlyData = {};

  data.forEach(item => {
    const date = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    }
  });

  // Get last 12 months
  const months = [];
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  return {
    categories: months,
    data: months.map(month => monthlyData[month] || 0)
  };
};

/**
 * Process incident data for severity trends over time
 */
export const processIncidentSeverityTrends = (data) => {
  const severityTrends = {};
  const months = [];

  // Get last 12 months
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  // Initialize severity categories
  const severities = ['H1C1', 'H1C2', 'H1C3', 'H2C1', 'H2C2', 'H2C3', 'H3C1', 'H3C2', 'H3C3', 'H4C1', 'H4C2', 'H4C3', 'H4C4'];
  severities.forEach(severity => {
    severityTrends[severity] = months.map(() => 0);
  });

  data.forEach(item => {
    const date = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      const monthIndex = months.indexOf(monthKey);
      if (monthIndex !== -1) {
        const severity = item.incidentRating?.item || 'Unknown';
        const formattedSeverity = severity.replace(/C(\d+)H(\d+)/, "H$2C$1");
        if (severityTrends[formattedSeverity]) {
          severityTrends[formattedSeverity][monthIndex]++;
        }
      }
    }
  });

  return {
    categories: months,
    series: Object.entries(severityTrends)
      .filter(([, data]) => data.some(val => val > 0)) // Only include severities with data
      .map(([severity, data]) => ({
        name: severity,
        data: data,
        color: INCIDENT_SEVERITY_COLORS[severity] || CHART_COLORS.secondary
      }))
  };
};
