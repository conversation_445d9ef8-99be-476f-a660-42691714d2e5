import moment from 'moment';

/**
 * Chart color palette for consistent styling
 */
export const CHART_COLORS = {
  primary: '#007bff',
  success: '#28a745',
  danger: '#dc3545',
  warning: '#ffc107',
  info: '#17a2b8',
  secondary: '#6c757d',
  light: '#f8f9fa',
  dark: '#343a40',
  purple: '#6f42c1',
  pink: '#e83e8c',
  orange: '#fd7e14',
  teal: '#20c997',
  cyan: '#17a2b8',
  indigo: '#6610f2'
};

/**
 * Status color mapping for observations
 */
export const STATUS_COLORS = {
  'Actions Assigned': CHART_COLORS.warning,
  'Actions Taken - Pending Verification': CHART_COLORS.info,
  'Action Verified - Closed': CHART_COLORS.success,
  'Reported & Closed': CHART_COLORS.success,
  'Reported & Rectified on Spot': CHART_COLORS.success,
  'Action Reassigned': CHART_COLORS.orange,
  'Archived without actions': CHART_COLORS.secondary,
  'Under Review': CHART_COLORS.primary
};

/**
 * Severity color mapping
 */
export const SEVERITY_COLORS = {
  'High': CHART_COLORS.danger,
  'Medium': CHART_COLORS.warning,
  'Low': CHART_COLORS.success
};

/**
 * Category color mapping
 */
export const CATEGORY_COLORS = {
  'Safety': CHART_COLORS.danger,
  'Health': CHART_COLORS.success,
  'Environment': CHART_COLORS.info
};

/**
 * Type color mapping
 */
export const TYPE_COLORS = {
  'Unsafe Act': CHART_COLORS.danger,
  'Unsafe Condition': CHART_COLORS.warning,
  'Positive': CHART_COLORS.success
};

/**
 * Process observation data for status distribution chart
 */
export const processStatusData = (data) => {
  const statusCounts = {};
  
  data.forEach(item => {
    const status = item.status || 'Unknown';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.entries(statusCounts).map(([name, y]) => ({
    name,
    y,
    color: STATUS_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for category distribution chart
 */
export const processCategoryData = (data) => {
  const categoryCounts = {};
  
  data.forEach(item => {
    const category = item.category || 'Unknown';
    categoryCounts[category] = (categoryCounts[category] || 0) + 1;
  });

  return Object.entries(categoryCounts).map(([name, y]) => ({
    name,
    y,
    color: CATEGORY_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for type distribution chart
 */
export const processTypeData = (data) => {
  const typeCounts = {};
  
  data.forEach(item => {
    const type = item.type || 'Unknown';
    typeCounts[type] = (typeCounts[type] || 0) + 1;
  });

  return Object.entries(typeCounts).map(([name, y]) => ({
    name,
    y,
    color: TYPE_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for severity distribution chart
 */
export const processSeverityData = (data) => {
  const severityCounts = {};
  
  data.forEach(item => {
    const severity = item.severity || 'Unknown';
    severityCounts[severity] = (severityCounts[severity] || 0) + 1;
  });

  return Object.entries(severityCounts).map(([name, y]) => ({
    name,
    y,
    color: SEVERITY_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process observation data for department-wise analysis
 */
export const processDepartmentData = (data) => {
  const departmentCounts = {};
  
  data.forEach(item => {
    const department = item.workActivityDepartment?.name || 'Unknown';
    departmentCounts[department] = (departmentCounts[department] || 0) + 1;
  });

  return Object.entries(departmentCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 departments
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process observation data for monthly trends
 */
export const processMonthlyTrends = (data) => {
  const monthlyData = {};
  
  data.forEach(item => {
    const date = moment(item.created, ['Do MMM YYYY', 'DD-MM-YYYY', moment.ISO_8601]);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    }
  });

  // Get last 12 months
  const months = [];
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  return {
    categories: months,
    data: months.map(month => monthlyData[month] || 0)
  };
};

/**
 * Process observation data for action completion rates
 */
export const processActionCompletionData = (data) => {
  const completedStatuses = [
    'Action Verified - Closed',
    'Reported & Closed',
    'Reported & Rectified on Spot',
    'Archived without actions'
  ];

  const total = data.length;
  const completed = data.filter(item => completedStatuses.includes(item.status)).length;
  const pending = total - completed;

  return [
    { name: 'Completed', y: completed, color: CHART_COLORS.success },
    { name: 'Pending', y: pending, color: CHART_COLORS.warning }
  ];
};

/**
 * Process observation data for overdue analysis
 */
export const processOverdueData = (data) => {
  const currentDate = moment();
  let overdue = 0;
  let dueSoon = 0; // Due within 7 days
  let upcoming = 0;
  let noDueDate = 0;

  data.forEach(item => {
    if (!item.dueDate || item.dueDate === '') {
      noDueDate++;
    } else {
      const dueDate = moment(item.dueDate, 'DD-MM-YYYY');
      if (dueDate.isBefore(currentDate)) {
        overdue++;
      } else if (dueDate.diff(currentDate, 'days') <= 7) {
        dueSoon++;
      } else {
        upcoming++;
      }
    }
  });

  return [
    { name: 'Overdue', y: overdue, color: CHART_COLORS.danger },
    { name: 'Due Soon (≤7 days)', y: dueSoon, color: CHART_COLORS.warning },
    { name: 'Upcoming', y: upcoming, color: CHART_COLORS.info },
    { name: 'No Due Date', y: noDueDate, color: CHART_COLORS.secondary }
  ];
};

/**
 * Default chart options for consistent styling
 */
export const getDefaultChartOptions = (title, subtitle = '') => ({
  chart: {
    backgroundColor: 'transparent',
    style: {
      fontFamily: 'Lato, sans-serif'
    }
  },
  title: {
    text: title,
    style: {
      fontSize: '18px',
      fontWeight: 'bold',
      color: '#333'
    }
  },
  subtitle: {
    text: subtitle,
    style: {
      fontSize: '14px',
      color: '#666'
    }
  },
  credits: {
    enabled: false
  },
  exporting: {
    enabled: true,
    buttons: {
      contextButton: {
        menuItems: ['downloadPNG', 'downloadJPEG', 'downloadPDF', 'downloadSVG']
      }
    }
  },
  responsive: {
    rules: [{
      condition: {
        maxWidth: 500
      },
      chartOptions: {
        legend: {
          layout: 'horizontal',
          align: 'center',
          verticalAlign: 'bottom'
        }
      }
    }]
  }
});

/**
 * Pie chart specific options
 */
export const getPieChartOptions = (title, subtitle = '') => ({
  ...getDefaultChartOptions(title, subtitle),
  chart: {
    ...getDefaultChartOptions().chart,
    type: 'pie'
  },
  tooltip: {
    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b> ({point.y})'
  },
  accessibility: {
    point: {
      valueSuffix: '%'
    }
  },
  plotOptions: {
    pie: {
      allowPointSelect: true,
      cursor: 'pointer',
      dataLabels: {
        enabled: true,
        format: '<b>{point.name}</b>: {point.percentage:.1f} %'
      },
      showInLegend: true
    }
  }
});

/**
 * Column chart specific options
 */
export const getColumnChartOptions = (title, subtitle = '', yAxisTitle = 'Count') => ({
  ...getDefaultChartOptions(title, subtitle),
  chart: {
    ...getDefaultChartOptions().chart,
    type: 'column'
  },
  xAxis: {
    type: 'category',
    labels: {
      rotation: -45,
      style: {
        fontSize: '13px',
        fontFamily: 'Lato, sans-serif'
      }
    }
  },
  yAxis: {
    min: 0,
    title: {
      text: yAxisTitle
    }
  },
  tooltip: {
    headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
      '<td style="padding:0"><b>{point.y}</b></td></tr>',
    footerFormat: '</table>',
    shared: true,
    useHTML: true
  },
  plotOptions: {
    column: {
      pointPadding: 0.2,
      borderWidth: 0,
      dataLabels: {
        enabled: true
      }
    }
  }
});

/**
 * Line chart specific options
 */
export const getLineChartOptions = (title, subtitle = '', yAxisTitle = 'Count') => ({
  ...getDefaultChartOptions(title, subtitle),
  chart: {
    ...getDefaultChartOptions().chart,
    type: 'line'
  },
  xAxis: {
    categories: []
  },
  yAxis: {
    title: {
      text: yAxisTitle
    }
  },
  tooltip: {
    valueSuffix: ' observations'
  },
  plotOptions: {
    line: {
      dataLabels: {
        enabled: true
      },
      enableMouseTracking: true
    }
  }
});

// ============ INCIDENT ANALYTICS UTILITIES ============

/**
 * Incident severity color mapping
 */
export const INCIDENT_SEVERITY_COLORS = {
  'H1C1': CHART_COLORS.danger,
  'H1C2': CHART_COLORS.danger,
  'H1C3': CHART_COLORS.danger,
  'H2C1': CHART_COLORS.warning,
  'H2C2': CHART_COLORS.warning,
  'H2C3': CHART_COLORS.warning,
  'H3C1': CHART_COLORS.info,
  'H3C2': CHART_COLORS.info,
  'H3C3': CHART_COLORS.info,
  'H4C1': CHART_COLORS.success,
  'H4C2': CHART_COLORS.success,
  'H4C3': CHART_COLORS.success,
  'H4C4': CHART_COLORS.success
};

/**
 * Incident status color mapping
 */
export const INCIDENT_STATUS_COLORS = {
  'Open': CHART_COLORS.warning,
  'Under Investigation': CHART_COLORS.info,
  'Closed': CHART_COLORS.success,
  'Pending': CHART_COLORS.orange,
  'Draft': CHART_COLORS.secondary
};

/**
 * Process incident data for severity distribution chart
 */
export const processIncidentSeverityData = (data) => {
  const severityCounts = {};

  data.forEach(item => {
    const severity = item.incidentRating?.item || 'Unknown';
    // Convert format from C1H1 to H1C1 if needed
    const formattedSeverity = severity.replace(/C(\d+)H(\d+)/, "H$2C$1");
    severityCounts[formattedSeverity] = (severityCounts[formattedSeverity] || 0) + 1;
  });

  return Object.entries(severityCounts).map(([name, y]) => ({
    name,
    y,
    color: INCIDENT_SEVERITY_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process incident data for status distribution chart
 */
export const processIncidentStatusData = (data) => {
  const statusCounts = {};

  data.forEach(item => {
    const status = item.status || 'Unknown';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.entries(statusCounts).map(([name, y]) => ({
    name,
    y,
    color: INCIDENT_STATUS_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process incident data for reported status distribution
 */
export const processIncidentReportedStatusData = (data) => {
  const reportedCounts = {};

  data.forEach(item => {
    const status = item.isReported === 'Yes' ? 'Reported' :
                   item.isReported === 'No' ? 'Unreported' :
                   item.isReported || 'Unknown';
    reportedCounts[status] = (reportedCounts[status] || 0) + 1;
  });

  return Object.entries(reportedCounts).map(([name, y]) => ({
    name,
    y,
    color: name === 'Reported' ? CHART_COLORS.success :
           name === 'Unreported' ? CHART_COLORS.danger :
           CHART_COLORS.secondary
  }));
};

/**
 * Process incident data for day/night distribution
 */
export const processIncidentDayNightData = (data) => {
  const dayNightCounts = {};

  data.forEach(item => {
    const date = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
    if (date.isValid()) {
      const hour = date.hour();
      const dayNight = hour >= 6 && hour < 18 ? 'Day' : 'Night';
      dayNightCounts[dayNight] = (dayNightCounts[dayNight] || 0) + 1;
    }
  });

  return Object.entries(dayNightCounts).map(([name, y]) => ({
    name,
    y,
    color: name === 'Day' ? CHART_COLORS.warning : CHART_COLORS.dark
  }));
};

/**
 * Process incident data for group-wise analysis
 */
export const processIncidentGroupData = (data) => {
  const groupCounts = {};

  data.forEach(item => {
    const group = item.workingGroup?.name || 'Unknown';
    groupCounts[group] = (groupCounts[group] || 0) + 1;
  });

  return Object.entries(groupCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 groups
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process incident data for area-wise analysis
 */
export const processIncidentAreaData = (data) => {
  const areaCounts = {};

  data.forEach(item => {
    const area = item.locationThree?.name || 'Unknown';
    areaCounts[area] = (areaCounts[area] || 0) + 1;
  });

  return Object.entries(areaCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 areas
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process incident data for monthly trends
 */
export const processIncidentMonthlyTrends = (data) => {
  const monthlyData = {};

  data.forEach(item => {
    const date = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    }
  });

  // Get last 12 months
  const months = [];
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  return {
    categories: months,
    data: months.map(month => monthlyData[month] || 0)
  };
};

/**
 * Process incident data for severity trends over time
 */
export const processIncidentSeverityTrends = (data) => {
  const severityTrends = {};
  const months = [];

  // Get last 12 months
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  // Initialize severity categories
  const severities = ['H1C1', 'H1C2', 'H1C3', 'H2C1', 'H2C2', 'H2C3', 'H3C1', 'H3C2', 'H3C3', 'H4C1', 'H4C2', 'H4C3', 'H4C4'];
  severities.forEach(severity => {
    severityTrends[severity] = months.map(() => 0);
  });

  data.forEach(item => {
    const date = moment(item.incidentDate, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A']);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      const monthIndex = months.indexOf(monthKey);
      if (monthIndex !== -1) {
        const severity = item.incidentRating?.item || 'Unknown';
        const formattedSeverity = severity.replace(/C(\d+)H(\d+)/, "H$2C$1");
        if (severityTrends[formattedSeverity]) {
          severityTrends[formattedSeverity][monthIndex]++;
        }
      }
    }
  });

  return {
    categories: months,
    series: Object.entries(severityTrends)
      .filter(([, data]) => data.some(val => val > 0)) // Only include severities with data
      .map(([severity, data]) => ({
        name: severity,
        data: data,
        color: INCIDENT_SEVERITY_COLORS[severity] || CHART_COLORS.secondary
      }))
  };
};

// ============ EPERMIT ANALYTICS UTILITIES ============

/**
 * ePermit status color mapping
 */
export const EPERMIT_STATUS_COLORS = {
  'Approved': CHART_COLORS.success,
  'Pending': CHART_COLORS.warning,
  'Rejected': CHART_COLORS.danger,
  'Draft': CHART_COLORS.secondary,
  'Under Review': CHART_COLORS.info,
  'Expired': CHART_COLORS.dark,
  'Active': CHART_COLORS.primary,
  'Closed': CHART_COLORS.success
};

/**
 * ePermit type color mapping
 */
export const EPERMIT_TYPE_COLORS = {
  'Hot Work': CHART_COLORS.danger,
  'Cold Work': CHART_COLORS.info,
  'Confined Space': CHART_COLORS.warning,
  'Working at Height': CHART_COLORS.orange,
  'Electrical Work': CHART_COLORS.purple,
  'Excavation': CHART_COLORS.dark,
  'General': CHART_COLORS.secondary
};

/**
 * ePermit level color mapping
 */
export const EPERMIT_LEVEL_COLORS = {
  'High Risk': CHART_COLORS.danger,
  'Medium Risk': CHART_COLORS.warning,
  'Low Risk': CHART_COLORS.success,
  'Level 1': CHART_COLORS.danger,
  'Level 2': CHART_COLORS.warning,
  'Level 3': CHART_COLORS.success
};

/**
 * Process ePermit data for status distribution chart
 */
export const processEPermitStatusData = (data) => {
  const statusCounts = {};

  data.forEach(item => {
    const status = item.status || 'Unknown';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.entries(statusCounts).map(([name, y]) => ({
    name,
    y,
    color: EPERMIT_STATUS_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process ePermit data for closure status distribution
 */
export const processEPermitClosureStatusData = (data) => {
  const closureStatusCounts = {};

  data.forEach(item => {
    const closureStatus = item.closure?.status || 'No Closure Data';
    closureStatusCounts[closureStatus] = (closureStatusCounts[closureStatus] || 0) + 1;
  });

  return Object.entries(closureStatusCounts).map(([name, y]) => ({
    name,
    y,
    color: name === 'Closed' ? CHART_COLORS.success :
           name === 'Open' ? CHART_COLORS.warning :
           name === 'Pending' ? CHART_COLORS.info :
           CHART_COLORS.secondary
  }));
};

/**
 * Process ePermit data for permit type distribution
 */
export const processEPermitTypeData = (data) => {
  const typeCounts = {};

  data.forEach(item => {
    const type = item.permitType || 'Unknown';
    typeCounts[type] = (typeCounts[type] || 0) + 1;
  });

  return Object.entries(typeCounts).map(([name, y]) => ({
    name,
    y,
    color: EPERMIT_TYPE_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process ePermit data for level distribution
 */
export const processEPermitLevelData = (data) => {
  const levelCounts = {};

  data.forEach(item => {
    const level = item.level || item.high_risk?.risk_level || 'Unknown';
    levelCounts[level] = (levelCounts[level] || 0) + 1;
  });

  return Object.entries(levelCounts).map(([name, y]) => ({
    name,
    y,
    color: EPERMIT_LEVEL_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process ePermit data for location-wise analysis
 */
export const processEPermitLocationData = (data) => {
  const locationCounts = {};

  data.forEach(item => {
    const location = item.locationThree?.name || 'Unknown';
    locationCounts[location] = (locationCounts[location] || 0) + 1;
  });

  return Object.entries(locationCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 locations
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process ePermit data for project-wise analysis
 */
export const processEPermitProjectData = (data) => {
  const projectCounts = {};

  data.forEach(item => {
    const project = item.locationFour?.name || 'Unknown';
    projectCounts[project] = (projectCounts[project] || 0) + 1;
  });

  return Object.entries(projectCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 projects
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process ePermit data for monthly trends
 */
export const processEPermitMonthlyTrends = (data) => {
  const monthlyData = {};

  data.forEach(item => {
    const date = moment(item.created, ['DD-MM-YYYY hh:mm A', 'DD-MM-YYYY HH:mm', moment.ISO_8601]);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    }
  });

  // Get last 12 months
  const months = [];
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  return {
    categories: months,
    data: months.map(month => monthlyData[month] || 0)
  };
};

/**
 * Process ePermit data for duration analysis
 */
export const processEPermitDurationData = (data) => {
  const durationRanges = {
    '< 1 Day': 0,
    '1-3 Days': 0,
    '4-7 Days': 0,
    '1-2 Weeks': 0,
    '2-4 Weeks': 0,
    '> 1 Month': 0
  };

  data.forEach(item => {
    if (item.permitStartDate && item.permitEndDate) {
      const startDate = moment(item.permitStartDate, ['DD-MM-YYYY hh:mm A', 'DD-MM-YYYY HH:mm']);
      const endDate = moment(item.permitEndDate, ['DD-MM-YYYY hh:mm A', 'DD-MM-YYYY HH:mm']);

      if (startDate.isValid() && endDate.isValid()) {
        const duration = endDate.diff(startDate, 'days');

        if (duration < 1) {
          durationRanges['< 1 Day']++;
        } else if (duration <= 3) {
          durationRanges['1-3 Days']++;
        } else if (duration <= 7) {
          durationRanges['4-7 Days']++;
        } else if (duration <= 14) {
          durationRanges['1-2 Weeks']++;
        } else if (duration <= 28) {
          durationRanges['2-4 Weeks']++;
        } else {
          durationRanges['> 1 Month']++;
        }
      }
    }
  });

  return Object.entries(durationRanges).map(([name, y]) => ({
    name,
    y,
    color: name === '< 1 Day' ? CHART_COLORS.success :
           name === '1-3 Days' ? CHART_COLORS.info :
           name === '4-7 Days' ? CHART_COLORS.primary :
           name === '1-2 Weeks' ? CHART_COLORS.warning :
           name === '2-4 Weeks' ? CHART_COLORS.orange :
           CHART_COLORS.danger
  }));
};

/**
 * Process ePermit data for approval time analysis
 */
export const processEPermitApprovalTimeData = (data) => {
  const approvalTimes = [];

  data.forEach(item => {
    if (item.created && item.approvers && item.approvers.length > 0) {
      const createdDate = moment(item.created, ['DD-MM-YYYY hh:mm A', 'DD-MM-YYYY HH:mm']);

      // Find the last approval date
      let lastApprovalDate = null;
      item.approvers.forEach(approver => {
        if (approver.approverSignedDate) {
          const approvalDate = moment(approver.approverSignedDate, ['DD-MM-YYYY hh:mm A', 'DD-MM-YYYY HH:mm']);
          if (approvalDate.isValid() && (!lastApprovalDate || approvalDate.isAfter(lastApprovalDate))) {
            lastApprovalDate = approvalDate;
          }
        }
      });

      if (createdDate.isValid() && lastApprovalDate) {
        const approvalTime = lastApprovalDate.diff(createdDate, 'hours');
        approvalTimes.push(approvalTime);
      }
    }
  });

  // Calculate average approval time
  const avgApprovalTime = approvalTimes.length > 0 ?
    approvalTimes.reduce((sum, time) => sum + time, 0) / approvalTimes.length : 0;

  // Categorize approval times
  const timeRanges = {
    '< 4 Hours': 0,
    '4-24 Hours': 0,
    '1-3 Days': 0,
    '3-7 Days': 0,
    '> 1 Week': 0
  };

  approvalTimes.forEach(time => {
    if (time < 4) {
      timeRanges['< 4 Hours']++;
    } else if (time <= 24) {
      timeRanges['4-24 Hours']++;
    } else if (time <= 72) {
      timeRanges['1-3 Days']++;
    } else if (time <= 168) {
      timeRanges['3-7 Days']++;
    } else {
      timeRanges['> 1 Week']++;
    }
  });

  return {
    data: Object.entries(timeRanges).map(([name, y]) => ({
      name,
      y,
      color: name === '< 4 Hours' ? CHART_COLORS.success :
             name === '4-24 Hours' ? CHART_COLORS.info :
             name === '1-3 Days' ? CHART_COLORS.warning :
             name === '3-7 Days' ? CHART_COLORS.orange :
             CHART_COLORS.danger
    })),
    avgApprovalTime: Math.round(avgApprovalTime * 10) / 10
  };
};

// ============ RISK ASSESSMENT ANALYTICS UTILITIES ============

/**
 * Risk Assessment status color mapping
 */
export const RISK_STATUS_COLORS = {
  'Published': CHART_COLORS.success,
  'Pending': CHART_COLORS.warning,
  'Draft': CHART_COLORS.secondary,
  'Under Review': CHART_COLORS.info,
  'Approved': CHART_COLORS.success,
  'Rejected': CHART_COLORS.danger
};

/**
 * Risk Assessment type color mapping
 */
export const RISK_TYPE_COLORS = {
  'Routine Work': CHART_COLORS.primary,
  'Non-Routine Work': CHART_COLORS.warning,
  'Hazard-Based': CHART_COLORS.danger,
  'Topical': CHART_COLORS.info
};

/**
 * Risk level color mapping based on severity x likelihood
 */
export const RISK_LEVEL_COLORS = {
  'Low': CHART_COLORS.success,
  'Medium': CHART_COLORS.warning,
  'High': CHART_COLORS.orange,
  'Very High': CHART_COLORS.danger,
  'Extreme': CHART_COLORS.dark
};

/**
 * Process Risk Assessment data for status distribution chart
 */
export const processRiskStatusData = (data) => {
  const statusCounts = {};

  data.forEach(item => {
    const status = item.status || 'Unknown';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.entries(statusCounts).map(([name, y]) => ({
    name,
    y,
    color: RISK_STATUS_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process Risk Assessment data for type distribution chart
 */
export const processRiskTypeData = (data) => {
  const typeCounts = {};

  data.forEach(item => {
    const type = item.type?.label || 'Unknown';
    typeCounts[type] = (typeCounts[type] || 0) + 1;
  });

  return Object.entries(typeCounts).map(([name, y]) => ({
    name,
    y,
    color: RISK_TYPE_COLORS[name] || CHART_COLORS.secondary
  }));
};

/**
 * Process Risk Assessment data for department distribution
 */
export const processRiskDepartmentData = (data) => {
  const departmentCounts = {};

  data.forEach(item => {
    const department = item.department || 'Unknown';
    departmentCounts[department] = (departmentCounts[department] || 0) + 1;
  });

  return Object.entries(departmentCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 departments
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process Risk Assessment data for RA Leader analysis
 */
export const processRiskLeaderData = (data) => {
  const leaderCounts = {};

  data.forEach(item => {
    const leader = item.captain || item.user?.firstName || 'Unknown';
    leaderCounts[leader] = (leaderCounts[leader] || 0) + 1;
  });

  return Object.entries(leaderCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 leaders
    .map(([name, y]) => ({ name, y }));
};

/**
 * Process Risk Assessment data for monthly trends
 */
export const processRiskMonthlyTrends = (data) => {
  const monthlyData = {};

  data.forEach(item => {
    const date = moment(item.date, ['DD-MM-YYYY', 'DD-MM-YYYY HH:mm', moment.ISO_8601]);
    if (date.isValid()) {
      const monthKey = date.format('MMM YYYY');
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    }
  });

  // Get last 12 months
  const months = [];
  for (let i = 11; i >= 0; i--) {
    const month = moment().subtract(i, 'months').format('MMM YYYY');
    months.push(month);
  }

  return {
    categories: months,
    data: months.map(month => monthlyData[month] || 0)
  };
};

/**
 * Process Risk Assessment data for overdue analysis
 */
export const processRiskOverdueData = (data) => {
  const currentDate = moment();
  let overdue = 0;
  let dueSoon = 0; // Due within 30 days
  let upcoming = 0;
  let noReviewDate = 0;

  data.forEach(item => {
    if (!item.nextdate || item.nextdate === '') {
      noReviewDate++;
    } else {
      const reviewDate = moment(item.nextdate, 'DD-MM-YYYY');
      if (reviewDate.isValid()) {
        if (reviewDate.isBefore(currentDate)) {
          overdue++;
        } else if (reviewDate.diff(currentDate, 'days') <= 30) {
          dueSoon++;
        } else {
          upcoming++;
        }
      } else {
        noReviewDate++;
      }
    }
  });

  return [
    { name: 'Overdue for Review', y: overdue, color: CHART_COLORS.danger },
    { name: 'Due Soon (≤30 days)', y: dueSoon, color: CHART_COLORS.warning },
    { name: 'Upcoming Review', y: upcoming, color: CHART_COLORS.info },
    { name: 'No Review Date', y: noReviewDate, color: CHART_COLORS.secondary }
  ];
};

/**
 * Process Risk Assessment data for additional controls analysis
 */
export const processRiskAdditionalControlsData = (data) => {
  let requiresAdditional = 0;
  let noAdditionalRequired = 0;
  let overdueAdditional = 0;

  const currentDate = moment();

  data.forEach(item => {
    if (item.additional && item.additional.includes('No')) {
      requiresAdditional++;

      // Check if additional controls are overdue
      if (item.additionalDates && Array.isArray(item.additionalDates)) {
        let hasOverdueControl = false;

        item.additionalDates.forEach(dateGroup => {
          if (Array.isArray(dateGroup)) {
            dateGroup.forEach(dateItem => {
              if (dateItem.date && dateItem.date !== '') {
                const controlDate = moment(dateItem.date);
                if (controlDate.isValid() && controlDate.isBefore(currentDate)) {
                  hasOverdueControl = true;
                }
              }
            });
          }
        });

        if (hasOverdueControl) {
          overdueAdditional++;
        }
      }
    } else {
      noAdditionalRequired++;
    }
  });

  return [
    { name: 'Requires Additional Controls', y: requiresAdditional, color: CHART_COLORS.warning },
    { name: 'No Additional Controls Required', y: noAdditionalRequired, color: CHART_COLORS.success },
    { name: 'Overdue Additional Controls', y: overdueAdditional, color: CHART_COLORS.danger }
  ];
};

/**
 * Process Risk Assessment data for activity analysis
 */
export const processRiskActivityData = (data) => {
  const activityCounts = {};

  data.forEach(item => {
    let activity = 'Unknown';

    if (item.type?.label === 'Hazard-Based') {
      activity = 'Hazard-Based Assessment';
    } else if (item.activity) {
      if (item.activity === 'Others' && item.otherActivity) {
        activity = `${item.activity} - ${item.otherActivity}`;
      } else {
        activity = item.activity;
      }
    }

    activityCounts[activity] = (activityCounts[activity] || 0) + 1;
  });

  return Object.entries(activityCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10) // Top 10 activities
    .map(([name, y]) => ({ name, y }));
};

/**
 * Calculate risk assessment summary statistics
 */
export const calculateRiskSummaryStats = (data, overdue, additional, aOverdue, archived) => {
  const total = data.length;
  const published = data.filter(item => item.status === 'Published').length;
  const pending = data.filter(item => item.status === 'Pending').length;
  const draft = data.filter(item => item.status === 'Draft').length;

  return {
    total,
    published,
    pending,
    draft,
    overdue: overdue.length,
    requiresAdditional: additional.length,
    overdueAdditional: aOverdue.length,
    archived: archived.length
  };
};
