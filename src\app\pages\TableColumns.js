import moment from "moment";
export const tableOptions = (title = '') => {
    return {
      exportButton: true,
      exportAllData: true,
      exportCsv: (columns, data) => {
        console.log(data)
        const customTitle = title || ''; // Custom title for cell A1
        const filteredData = data.map(row => ({
          firstName: row.firstName,
          email: row.email,
          type: row.type === 'Internal' ? 'SAGT' : row.company ? row.company : 'External',
        }));
        // Convert the data to CSV format
        const csvContent = `${customTitle}\n${columns.map(column => column.title).join(',')}\n${filteredData.map(row => Object.values(row).join(',')).join('\n')}`;
  
        // Create a Blob and initiate the download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'exported_data.csv';
        link.click();
      },
      actionsColumnIndex: -1,
      actionsCellStyle: {
        padding: '1.125rem 1.375rem',
      },
      pageSize: 20, 
      headerStyle: {
  
        padding: '1.125rem 1.375rem',
        fontSize: '0.812rem'
      },
      rowStyle: {
        // padding: '1.125rem 1.375rem',
        fontSize: '0.812rem'
      }
    }
  }
  export const observationColumns = [
    {
      title: "ID",
      field: "maskId",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
    {
      title: "Type of Observation",
      field: "type",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
    {
      title: "Category",
      field: "category",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
    {
      title: "Description",
      field: "description",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
    {
      title: "Rectified on the Spot",
      field: "rectifiedStatus",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
  
    {
      title: "Status",
      field: "status",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
    {
      title: "Actions to be Taken",
      field: "actionTaken",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
  ];
  
  export const userColumns = [
  
    {
      title: "Name",
      field: "firstName",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },
    {
      title: "Email",
      field: "email",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },
    {
      title: "Organization",
      field: "company",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      },
      render: rowData => rowData.type === 'Internal' ? 'SAGT' : (rowData.company ? rowData.company : 'External')
    }
  ];

  export const eptwColumns = [
    {
      title: "ID",
      field: "maskId",
      defaultSort: 'desc',
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
    {
      title: "Level",
      field: "level",
     
      cellStyle: {
        padding: '1.125rem 1.375rem',
      }
    },
    // {
    //   title: "Type of Permit",
    //   field: "permitType",
    //   cellStyle: {
    //     padding: '1.125rem 1.375rem',
    //   }
    // },
  
    {
      title: "Location",
      field: "location",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      },
      render: rowData => `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''}`
    },
  
    {
      title: "Closure Status",
      field: "closure",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      },
      render: rowData => rowData.closure ? (rowData.closure.status ? rowData.closure.status : 'N/A') : 'N/A'
    },
  
    {
      title: "Closeout Date",
      field: "closure",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      },
      render: rowData => rowData.closure && rowData.closure.closeoutDate
        ? moment(rowData.closure.closeoutDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A')
        : 'N/A'
    },
  
    // {
    //   title: "Submitted By",
    //   field: "applicant.firstName",
    //   cellStyle: {
    //     padding: '1.125rem 1.375rem',
    //   },
    //   render: rowData => rowData.applicant ? rowData.applicant.firstName : ''
    // },
  
    {
      title: "Status",
      field: "status",
      cellStyle: {
        padding: '1.125rem 1.375rem',
      },
      render: rowData => rowData.status ? rowData.status : 'N/A'
    },
  
  ];