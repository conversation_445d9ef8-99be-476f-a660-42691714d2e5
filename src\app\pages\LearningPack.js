import React, { useEffect, useRef, useState } from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Button } from 'react-bootstrap';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
pdfMake.vfs = pdfFonts.pdfMake.vfs;
const LearningPack = ({ data }) => {
    const [data1, setData1] = useState([])

    useEffect(() => {

        const fetchAndConvertImages = async () => {
            const newData = await convertImagesToDataUrls(data);
            setData1(newData);
        };
        if (data !== null) {

            fetchAndConvertImages();

        }

    }, [data])

    const convertImagesToDataUrls = async (data) => {
        const convertArrayToDataUrls = async (array) => {
            const imagePromises = array.map(async (item) => {
                console.log(item)
                const imageUrl = `${item.src}`;
                const response = await fetch(imageUrl);
                const blob = await response.blob();
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            });

            try {
                return await Promise.all(imagePromises);
            } catch (error) {
                console.error('Error fetching images:', error);
                return array;
            }
        };


        const evidenceDataUrls = data.evidence && await convertArrayToDataUrls(data.evidence);
        console.log(evidenceDataUrls)


        return {
            ...data,
            evidence: evidenceDataUrls,

        };
    };
    const contentRef1 = useRef();
    const contentRef2 = useRef();

    const generatePDF = async () => {
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'px',
            format: 'a4'
        });
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        const addPageToPDF = async (contentRef) => {
            const canvas = await html2canvas(contentRef.current, { scale: 2 });
            const imgData = canvas.toDataURL('image/png');

            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;

            const canvasAspectRatio = canvasWidth / canvasHeight;
            const pdfAspectRatio = pdfWidth / pdfHeight;

            let imgWidth, imgHeight;
            if (canvasAspectRatio > pdfAspectRatio) {
                imgWidth = pdfWidth;
                imgHeight = pdfWidth / canvasAspectRatio;
            } else {
                imgHeight = pdfHeight;
                imgWidth = pdfHeight * canvasAspectRatio;
            }

            const x = (pdfWidth - imgWidth) / 2;
            const y = (pdfHeight - imgHeight) / 2;

            pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);
        };

        await addPageToPDF(contentRef1);
        pdf.addPage();
        await addPageToPDF(contentRef2);

        pdf.save('learning_pack.pdf');
    };
    const convertToDataUrl = async (url) => {
        return fetch(url)
            .then(response => response.blob())
            .then(blob => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => {
                        resolve(reader.result);
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            });
    };
    const generatePDFMake = async () => {
        const documentDefinition = {
            pageOrientation: 'landscape',
            footer: {
                columns: [
                    {
                        image: await convertToDataUrl(require("../../assets/images/logo.png")).then((result) => {
                            return result
                        }),
                        fit: [40, 40], // Adjust the size as needed
                        alignment: 'right', // Center the logo
                    }
                ],
                margin: [10, 10, 10, 15], // Adjust the margin as needed
            },
            content: [
                {
                    columns: [
                        data1.evidence && {
                            columns: data1.evidence.map((item) => ({
                                image: item,
                                width: 80,
                                height: 100,
                                style: 'image'
                            })),
                        },
                        {
                            width: '50%',
                            stack: [
                                {
                                    text: `Learning Pack – HSI – ${data1.shortDescription}`,
                                    style: 'header',
                                },
                                {
                                    text: `Incident type: ${data1.incidentTypeName?.name || ''}`,
                                    style: 'subheader1',
                                },
                                {
                                    text: `IR number: ${data1.maskId}`,
                                    style: 'subheader1',
                                },
                                {
                                    text: `Location: ${data1.locationThree?.name || ''} > ${data1.locationFour?.name || ''}`,
                                    style: 'subheader1',
                                },
                                {
                                    text: `People: '-'`,
                                    style: 'subheader1',
                                },
                                {
                                    text: `Asset: '-'`,
                                    style: 'subheader1',
                                },
                            ],
                        },




                    ],
                },


                {
                    canvas: [
                        { type: 'line', x1: 0, y1: 0, x2: 750, y2: 0, lineWidth: 2, lineColor: '#007BFF' } // Set x2 to 822 for full width
                    ],
                    margin: [0, 10, 0, 10]
                },
                {
                    columns: [
                        {
                            width: '33%',
                            stack: [
                                { text: 'Description (what happened):', style: 'sectionHeader' },
                                { text: data1.description || '', margin: [5, 5, 5, 5] },
                            ],
                        },
                        {
                            width: '33%',
                            stack: [
                                { text: 'Contributing factors:', style: 'sectionHeader' },
                                { text: data1.moreDetails || '', margin: [5, 5, 5, 5]  },
                            ],
                        },
                        {
                            width: '33%',
                            stack: [
                                { text: 'Preventive measures and improved procedures:', style: 'sectionHeader' },
                                { text: data1.investigationStep?.e2 || '' , margin: [5, 5, 5, 5] },
                            ],
                        },
                    ],
                },
                {
                    columns: [
                        {
                            width: '50%',
                            stack: [
                                { text: 'Severity Classification (Risk Matrix)', style: 'sectionHeader' },
                                {
                                    text: data1.incidentRating?.item || '',
                                    color: data1.incidentRating?.color || 'black',
                                    fontSize: 24,
                                },
                            ],
                        },
                        {
                            width: '50%',
                            stack: [
                                { text: 'Corrective actions:', style: 'sectionHeader' },
                                { text: data1.investigationStep?.conclusionRemarks || '' },
                            ],
                        },
                    ],
                },
              
                {
                    text: 'Immediate causes:',
                    style: 'sectionHeader',
                    pageBreak: 'before'
                },
                {
                    text: data1.investigationStep?.f1 || '',
                },
                {
                    canvas: [
                        { type: 'line', x1: 0, y1: 0, x2: 750, y2: 0, lineWidth: 2, lineColor: '#007BFF' } // Set x2 to 822 for full width
                    ],
                    margin: [0, 10, 0, 10]
                },
                {
                    text: 'Underlying causes (General conditions):',
                    style: 'sectionHeader',
                },
                {
                    text: data1.investigationStep?.f2 || '',
                },
                {
                    canvas: [
                        { type: 'line', x1: 0, y1: 0, x2: 750, y2: 0, lineWidth: 2, lineColor: '#007BFF' } // Set x2 to 822 for full width
                    ],
                    margin: [0, 10, 0, 10]
                },
                {
                    text: 'Root causes:',
                    style: 'sectionHeader',
                },
                {
                    text: data1.investigationStep?.f3 || '',
                },
                
            ],
            styles: {
                header: {
                    fontSize: 16,
                    bold: true,
                    margin: [0, 0, 0, 10],
                    color: '#007BFF'
                },
                subheader: {
                    fontSize: 12,
                    margin: [0, 10, 0, 5],
                    color: '#007BFF'
                },
                subheader1: {
                    fontSize: 12,
                    margin: [0, 2, 0, 2],

                },
                sectionHeader: {
                    fontSize: 12,
                    bold: true,
                    margin: [0, 10, 0, 5],
                    color: '#007BFF'
                },
                image: {
                    margin: [0, 10, 0, 10],
                }
            },
        };

        pdfMake.createPdf(documentDefinition).download(`${data1.maskId}.pdf`);
    }
    return (

        <div>
            {console.log(data1)}
            <div ref={contentRef1} className="pack-container">
                <div className="pack-header">



                    <div className="pack-header-text">
                        <h1>Learning Pack – HSI – {data1.shortDescription}</h1>
                        <p>Incident type: {data1.incidentTypeName?.name || ''}<br />
                            IR number: {data1.maskId}<br />
                            Location: {data1.locationThree?.name || ''} > {data1.locationFour?.name || ''}<br />
                            People: '-'<br />
                            Asset: '-'
                        </p>
                    </div>
                    <div className='d-flex align-items-center justify-content-between'>
                        {data1.evidence &&

                            data1.evidence.map((item) => {
                                return (
                                    <img src={item} alt="Incident Image" className="pack-header-image" />
                                )
                            })
                        }
                    </div>
                </div>
                <div className="pack-content">
                    <div className='row'>
                        <div className="pack-section pack-description col-4">
                            <h2>Description (what happened):</h2>
                            <p>{data1.description}</p>
                        </div>
                        <div className="pack-section pack-contributing-factors col-4">
                            <h2>Contributing factors:</h2>
                            {data1?.moreDetails || ''}
                        </div>
                        <div className="pack-section pack-preventive-measures col-4">
                            <h2>Preventive measures and improved procedures:</h2>
                            {data1.investigationStep?.e2 || ''}
                        </div>
                    </div>
                    <div className='row'>
                        <div className="pack-section pack-risk-matrix col-6">
                            <h2>Severity Classification (Risk Matrix)</h2>
                            <p style={{ fontSize: '24px', color: data1.incidentRating?.color || 'black' }}>
                                {data1.incidentRating?.item || ''}
                            </p>
                        </div>

                        <div className="pack-section pack-corrective-actions col-6">
                            <h2>Corrective actions:</h2>
                            {data1?.investigationStep?.conclusionRemarks || ''}
                        </div>


                    </div>
                </div>
            </div>

            <div ref={contentRef2} className="pack-container">

                <div className="pack-content">


                    <div className="pack-section pack-immediate-causes">
                        <h2>Immediate causes:</h2>
                        {data1.investigationStep?.f1 || ''}
                    </div>
                    <div className="pack-section pack-underlying-causes">
                        <h2>Underlying causes (General conditions):</h2>
                        {data1.investigationStep?.f2 || ''}
                    </div>
                    <div className="pack-section pack-root-cause">
                        <h2>Root causes:</h2>
                        {data1.investigationStep?.f3 || ''}
                    </div>
                </div>
            </div>

            <Button onClick={generatePDFMake}>Export as PDF</Button>
        </div >
    );
}

export default LearningPack;
