import React, { Component } from "react";
import * as _ from "lodash";

class AudioInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      
      toolType: "AUDIO_INPUT",
      title: "",
      allowgallery:false,
      validation: {
        isRequired: false,
      },
      isEmpty:false
    
    };
    this.removeOption = this.removeOption.bind(this);
  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue(stateFor, value) {
    switch (stateFor) {
      case "TITLE":
        this.setState({ title: value });
        break;
        case "ALLOW_GALLERY":
            this.setState({ allowgallery:value });
    break;
      case "IS_REQUIRED":
        this.setState({
          validation: { ...this.state.validation, isRequired: value },
        });
        break;

   
      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  removeOption(index) {
    let radios = this.state.radios;
    radios.splice(index, 1);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  render() {
    return (
      <div className="card mb-3" style={this.state.title ==='' && this.props.field.isEmpty ?{boxShadow: '0px 0px 12px 3px #dfdfdf',border:'1px solid red'}:{boxShadow: '0px 0px 12px 3px #dfdfdf'}}>
        <div className="card-header d-flex justify-content-between">
          <div>
          <i className="fa fa-microphone mr-1"></i> Audio Input
          </div>
          <div className="">
          {this.props.index !== 0 ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveUp(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-up"></i>
                  </span>
                  : ""}
                {this.props.index !== this.props.length ?
                  <span
                    className="" style={{ paddingRight: 5 }}
                    onClick={() => this.props.moveDown(this.props.index)}
                  >
                    <i className="mdi mdi-arrow-down"></i>
                  </span>
                  : ''}
                <span
                  className=""
                  onClick={() => this.props.removeField(this.props.index)}
                >
                  <i className="mdi mdi-close"></i>
                </span>
              </div>
        </div>
        <div className="card-body">
       
         
            <div className="card-body">
              <div className="row">
                <div className="col-12">
                  <div className="form-group">
                    <label htmlFor="title">Text Title</label>
                    <textarea
                      value={this.state.title}
                      onChange={(e) =>
                        this.changeValue("TITLE", e.target.value)
                      }
                      className="form-control"
                    ></textarea>
                  </div>
                </div>
              </div>

             
              <div className="form-check">
                <input
                  defaultChecked={this.state.allowgallery}
                  onChange={(e) =>
                    this.changeValue("ALLOW_GALLERY", e.target.checked)
                  }
                  className="form-check-input"
                  type="checkbox"
                  id="isallow"
                />
                <label className="form-check-label" htmlFor="isallow">
                  Allow Gallery
                </label>
              </div>
           
              <div className="form-check">
                <input
                  defaultChecked={this.state.validation.isRequired}
                  onChange={(e) =>
                    this.changeValue("IS_REQUIRED", e.target.checked)
                  }
                  className="form-check-input"
                  type="checkbox"
                  id="isRequired"
                />
                <label className="form-check-label" htmlFor="isRequired">
                  Required
                </label>
              </div>

            
            </div>
          

          
        </div>
        
      </div>
    );
  }

 

 
}

export default AudioInput;
