import React, { useState, useEffect, useCallback } from "react";
import { Modal, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';

import API from "../services/API";
import { GENERAL_USER_URL, GET_USER_BY_ROLES_URL, AIR_HOD_REVIEW_WITH_ID_URL } from "../constants";

import cogoToast from "cogo-toast";

import moment from 'moment';
import ThirdPartyForm from "./ThirdPartyForm";
import AirInvestigationViewCard from "./AirInvestigationViewCard";
import "react-datepicker/dist/react-datepicker.css";


// Import required date-fns functions if needed for formatting
import { format } from 'date-fns';

const AirHodCard = ({ showModal, setShowModal, data }) => {


    function initializeDateWithOffset(date) {
        return moment(date).utcOffset('+0530');
    }
    const [supervisor, setSupervisor] = useState([])
    const [skillTrainer, setSkillTrainer] = useState([])
    const [hod, setHod] = useState([])
    const [generalUsers, setGeneralUsers] = useState([]);
    const [thirdParty, setThirdParty] = useState({});
    const [viewReport, setViewReport] = useState(false)
    const updateForms = (forms) => {
        setThirdParty(forms)
    }
    const [isRecovery, setIsRecovery] = useState(false)

    useEffect(() => {
        if (data.thirdPartyForm !== undefined) {
            setThirdParty(data.thirdPartyForm);

        }
        if (data.isRecoveryFromOtherParty !== undefined) {
            setIsRecovery(data.isRecoveryFromOtherParty);
        }

    }, [data])


    useEffect(() => {
        const fetchUsers = async () => {
            try {

                const supervisors = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'ir-supervisor' });
                setSupervisor(supervisors.data);

                const skillTrainers = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'ir-skill-trainer' });
                setSkillTrainer(skillTrainers.data);

                const hods = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'ir-hr' });
                setHod(hods.data);
            } catch (error) {
                console.error('Error fetching data', error);
            }
        };

        fetchUsers();
        // setSelectedReviewer(incidentData.incidentOwnerId)
        getGeneralUsers()



    }, [data])


    const getGeneralUsers = useCallback(async () => {
        const response = await API.get(GENERAL_USER_URL);
        if (response.status === 200) {
            setGeneralUsers(response.data);
        }
    }, []);





    const [witnessInvolved, setWitnessInvolved] = useState({
        witnessInvolved: [
            {
                "internal": true,
                "selectedEmp": {

                },
                "name": "",
                "empId": "",
                "designation": "",
                "comments": ""
            }
        ],
        personInvolved: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
        ],
        personnelImpacted: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
        ]

    })


    useEffect(() => {
        if (data) {

            const updatedPersonInvolved = data.personInvolved.map(person => ({
                ...person,
                supervisorAction: '',
                supervisorDueDate: '',
                supervisorResponsible: '',
                trainingNeed: '',
                trainingDueDate: '',
                trainingResponsible: '',
                hrAction: '',
                hrDueDate: '',
                hrResponsible: '' // Ensure this field is added
            }));

            // Similarly for personnelImpacted
            const updatedPersonnelImpacted = data.personnelImpacted.filter(i => i).map(person => ({
                ...person,
                supervisorAction: '',
                supervisorDueDate: '',
                supervisorResponsible: '',
                trainingNeed: '',
                trainingDueDate: '',
                trainingResponsible: '',
                hrAction: '',
                hrDueDate: '',
                hrResponsible: '',
            }));

            setWitnessInvolved({ witnessInvolved: data.witnessInvolved, personnelImpacted: updatedPersonnelImpacted, personInvolved: updatedPersonInvolved })

        }
    }, [data]);



    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(true);

        try {
            setIsClicked(false);
            // Patch Request to AIR_WITH_ID_URL

            const response = await API.patch(AIR_HOD_REVIEW_WITH_ID_URL(data.id, data.actionId), {
                hodComments: witnessInvolved,

            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                setIsClicked(false);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setIsClicked(false);
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            setIsClicked(false);
            console.error('An error occurred:', error);

        }
        setIsClicked(false);
    };

    const displayText = (label, text) => (
        <div className="mb-3">
            <strong>{label}: </strong> {text || "N/A"}
        </div>
    );

    const handleMedicalOfficerSurveillanceChange = (index, value, type) => {
        setWitnessInvolved(prevState => {
            const updatedList = prevState[type].map((item, i) => {
                if (i === index) {
                    return { ...item, medicalOfficerSurveillance: value };
                }
                return item;
            });

            return { ...prevState, [type]: updatedList };
        });
    };

    const handleInputChange = (type, index, fieldName, value) => {
        setWitnessInvolved(prevState => {
            // Clone the array to avoid direct mutation
            const updatedArray = [...prevState[type]];

            // Clone the specific object to be updated
            const updatedObject = { ...updatedArray[index] };

            // Update the field in the object
            updatedObject[fieldName] = value;

            // Update the object in the array
            updatedArray[index] = updatedObject;

            // Return the updated state
            return { ...prevState, [type]: updatedArray };
        });
    };

    const renderPersonFields = (action, index, type) => {
        const isInternal = action.internal;
        const isPersonInjured = action.injured;

        const commonFields = (
            <>

                <Box>

                    <div className="row mt-4">
                        <div className="col-4">
                            <div className="form-group">
                                <label>If any actions from the immediate supervisor?</label>
                                <input className="form-control" type="text" name="supervisorAction"

                                    value={action.supervisorAction}
                                    onChange={e => handleInputChange(type, index, 'supervisorAction', e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Due Date</label>
                                <input className="form-control" type="date" name="supervisorDueDate"

                                    value={action.supervisorDueDate}
                                    onChange={e => handleInputChange(type, index, 'supervisorDueDate', e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Person Responsible</label>
                                <select className="form-select" value={action.supervisorResponsible}

                                    onChange={e => handleInputChange(type, index, 'supervisorResponsible', e.target.value)}>
                                    <option value={''}>Choose</option>
                                    {
                                        supervisor.map(i => <option key={i.id} value={i.id}>{i.firstName}</option>)
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="row mt-4">
                        <div className="col-4">
                            <div className="form-group">
                                <label>Any training required?</label>
                                <input className="form-control" type="text" name="trainingNeed"
                                    value={action.trainingNeed}

                                    onChange={e => handleInputChange(type, index, 'trainingNeed', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Due Date</label>
                                <input className="form-control" type="date" name="trainingDueDate"

                                    value={action.trainingDueDate}
                                    onChange={e => handleInputChange(type, index, 'trainingDueDate', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Person Responsible</label>
                                <select className="form-select" value={action.trainingResponsible}
                                    onChange={e => handleInputChange(type, index, 'trainingResponsible', e.target.value)}>
                                    <option value={''}>Choose</option>
                                    {
                                        skillTrainer.map(i => <option key={i.id} value={i.id}>{i.firstName}</option>)
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="row mt-4">
                        <div className="col-4">
                            <div className="form-group">
                                <label>Any HR actions to be Taken?</label>
                                <input className="form-control" type="text" name="hrAction"
                                    value={action.hrAction}

                                    onChange={e => handleInputChange(type, index, 'hrAction', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">

                                <label>Due Date</label>
                                <input className="form-control" type="date" name="hrDueDate"
                                    value={action.hrDueDate}

                                    onChange={e => handleInputChange(type, index, 'hrDueDate', e.target.value)} />
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="form-group">
                                <label>Person Responsible</label>
                                <select className="form-select" value={action.hrResponsible}

                                    onChange={e => handleInputChange(type, index, 'hrResponsible', e.target.value)}>
                                    <option value={''}>Choose</option>
                                    {
                                        hod.map(i => <option key={i.id} value={i.id}>{i.firstName}</option>)
                                    }
                                </select>
                            </div>
                        </div>
                    </div>


                </Box>

            </>
        );

        if (isInternal) {

            // const selectedUser = generalUsers.length > 0 ? generalUsers.find(user => user.id === action?.selectedEmp?.id) : {};
            return (
                <div className="mb-4">
                    {displayText("Person Involved", action?.selectedEmp?.name ?? '')}
                    {commonFields}
                </div>
            );
        } else {
            return (
                <div className="mb-4">
                    {displayText("NIC", action.empId)}
                    {displayText("Name", action.name)}
                    {displayText("Remarks / Comments", action.comments)}
                    {commonFields}
                </div>
            );
        }
    };




    return (
        <>

            <>
                {data && <Modal
                    show={viewReport}
                    size="lg"
                    onHide={() => setViewReport(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                    backdrop="static"
                >
                    

                    <Modal.Body>
                       <AirInvestigationViewCard incidentData={data} />

                    </Modal.Body>

                    <Modal.Footer className="flex-wrap">

                        <Button variant="light" onClick={() => { setViewReport(false); }}>Close</Button>

                    </Modal.Footer>
                </Modal>}
            </>

            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className="w-100 d-flex align-items-center justify-content-between">
                        <h4 >
                            IR Information - {data.maskId}
                        </h4 >
                        <h4 >
                            Incident Date & Time: {data.incidentDate}
                        </h4 >
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        {/* <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div> */}
                        <div className="col-md-3">
                            <Button onClick={() => { setViewReport(true) }}> <i className='mdi mdi-eye'></i> View Investigation Report </Button>
                        </div>

                        <Form>


                            <div className="row">
                                <div>
                                    <Form.Label> Person Involved </Form.Label>
                                    {witnessInvolved.personInvolved.map((action, index) =>
                                        <div className="form-group" key={index}>
                                            {renderPersonFields(action, index, "personInvolved")}
                                            <br />
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <Form.Label> Personnel Injured </Form.Label>
                                    {witnessInvolved.personnelImpacted.filter(i => i).map((action, index) =>
                                        <div className="form-group" key={index}>
                                            {renderPersonFields(action, index, "personnelImpacted")}
                                            <br />
                                        </div>
                                    )}
                                </div>
                            </div>


                            <Button variant="primary" onClick={handleSubmit} disabled={isClicked}>Submit</Button>

                        </Form>
                    </div>
                    {
                        isRecovery && <div className='row'>
                            <label htmlFor="" className='m-0 me-3'>Recovery Parties</label>

                            <ThirdPartyForm readOnly={true} updateForms={updateForms} values={thirdParty} />


                        </div>
                    }


                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirHodCard;