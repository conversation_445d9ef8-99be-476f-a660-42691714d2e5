import React, { useState } from 'react';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { Checkbox } from 'primereact/checkbox';
import { But<PERSON> } from 'primereact/button';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';

function Investication() {
    const jobFactors = [
        { label: 'People', value: 'People' },
        { label: 'Environment', value: 'Environment' }
    ];

    const fallibilities = [
        { label: 'Select', value: '' },
        { label: 'High', value: 'High' },
        { label: 'Medium', value: 'Medium' },
        { label: 'Low', value: 'Low' }
    ];

    const extents = [
        { label: 'Minor', value: 'Minor' },
        { label: 'Major', value: 'Major' }
    ];

    return (
        <div className="row">
            <Accordion>
                <AccordionTab header="PC1 Identified Preventive Control">
                    <p>Nam quo sed id quis. Odo est perferendis sit necessitatibus accusamus sint in.</p>
                    <div className="form-group">
                        <label htmlFor="immediateCause">Immediate Cause</label>
                        <InputText id="immediateCause" placeholder="Describe immediate cause" className="form-control" />
                    </div>
                    <div className="form-group">
                        <label>Job factors that led to the immediate cause</label>
                    </div>
                    <div className="form-group">
                        <label>Job Factor #1</label>
                        <div className="row align-items-center">
                            <div className="col-md-4">
                                <label>Factor</label>
                                <Dropdown options={jobFactors} placeholder="Select Factor" className="d-flex" />
                            </div>
                            <div className="col-md-4">
                                <label>Fallibility</label>
                                <Dropdown options={fallibilities} placeholder="Select" className="d-flex" />
                            </div>
                            <div className="col-md-4">
                                <Checkbox inputId="routineIssue" />
                                <label htmlFor="routineIssue" className="p-checkbox-label">Routine / Repetitive issue</label>
                            </div>
                        </div>
                        <div className='row'>
                            <div className="col-md-4">
                                <label>Extent of Contribution</label>
                                <Dropdown options={extents} placeholder="Minor" className="d-flex" />
                            </div>
                            <div className="col-md-4">
                                <label>Description of Job Factors</label>
                                <InputText placeholder="Describe the factor" className="form-control" />
                            </div>

                        </div>
                    </div>
                    <div className="form-group">
                        <label>Related Organizational Factors</label>
                        <div className="row">
                            <div className="col-md-6">
                                <label>Factor</label>
                                <Dropdown options={jobFactors} placeholder="Factor2" className="d-flex" />
                            </div>
                            <div className="col-md-6">
                                <label>Description</label>
                                <InputText placeholder="Describe the factor" className="form-control" />
                            </div>
                        </div>
                        <Button label="Add Related Organizational Factor" className="btn btn-secondary mt-2" />
                    </div>
                    {/* <div className="form-group">
                        <label>Job Factor #2</label>
                        <div className="row">
                            <div className="col-md-4">
                                <label>Factor</label>
                                <Dropdown options={jobFactors} placeholder="Select Factor" className="d-flex" />
                            </div>
                            <div className="col-md-4">
                                <label>Extent of Contribution</label>
                                <Dropdown options={extents} placeholder="Major" className="form-control" />
                            </div>
                            <div className="col-md-12">
                                <label>Description of Job Factors</label>
                                <InputText placeholder="Describe the factor" className="form-control" />
                            </div>
                        </div>
                        <div className="form-group">
                            <label>Related Organizational Factors</label>
                            <div className="row">
                                <div className="col-md-6">
                                    <label>Factor</label>
                                    <Dropdown options={jobFactors} placeholder="Factor2" className="form-control" />
                                </div>
                                <div className="col-md-6">
                                    <label>Description</label>
                                    <InputText placeholder="Describe the factor" className="form-control" />
                                </div>
                            </div>
                            <Button label="Add Related Organizational Factor" className="btn btn-secondary mt-2" />
                        </div>
                    </div> */}
                    <Button label="Add More Job Factors" className="btn btn-secondary mt-2" />
                </AccordionTab>
                <AccordionTab header="UP1 Unidentified Preventive Control">
                    <p>Nam quo sed id quis. Odo est perferendis sit necessitatibus accusamus sint in.</p>
                    <InputText placeholder="Describe" className="form-control" />
                </AccordionTab>
            </Accordion>
        </div>
    );
};

export default Investication