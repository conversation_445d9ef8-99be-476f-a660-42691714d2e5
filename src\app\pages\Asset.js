import React, { useState, useMemo, useEffect } from 'react'

import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';

import { useSelector } from "react-redux";

import CardOverlay from './CardOverlay';
import AssetCard from './AssetCard';
import Checklist from '../checklist/Checklist';
import Documents from '../document/Document';
import Actions from '../risk/Actions';
import AppSwitch from './AppSwitch';
import Assignment from '../checklist/Assignment';
import Reports from '../checklist/Reports';

function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && (
                <Box >
                    {children}
                </Box>
            )}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `asset-tab-${index}`,
        'aria-controls': `asset-tabpanel-${index}`,
    };
}


const Asset = () => {

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

    };



    const me = useSelector((state) => state.login.user)




    const [value, setValue] = useState(0);


    const handleChange = (event, newValue) => {
        setValue(newValue);
    };




    const TABS = {
        ACTION: "ACTION",
        ASSET: "ASSET",
        CHECKLIST: "CHECKLIST",
        DOCUMENT: "DOCUMENT",
        SCHEDULING: "SCHEDULING",
        Assign: "Assign",
        Reports: "Reports",

    };



    useEffect(() => {
        setValue(TABS.CHECKLIST)

    }, [])

    return (
        <>


            <AppSwitch value={{ label: 'Asset', value: 'ra' }} />







            <Tabs value={value} onChange={handleChange} aria-label="asset management table">
                {/* <Tab label="My Action" value={TABS.ACTION} /> */}
                {/* <Tab label="Assets" value={TABS.ASSET} /> */}

                <Tab label="Checklists" value={TABS.CHECKLIST} />
                <Tab label="Assignment" value={TABS.Assign} />
                <Tab label="Reports" value={TABS.Reports} />

                {/* <Tab label="Documents" value={TABS.DOCUMENT} /> */}
                {/* <Tab label="Calendar View" value={TABS.SCHEDULING} /> */}

            </Tabs>


            <CustomTabPanel value={value} tabValue={TABS.ACTION}>
                <Actions />
            </CustomTabPanel>
            <CustomTabPanel value={value} tabValue={TABS.ASSET}>
                <AssetCard />
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.CHECKLIST}>
                <Checklist />
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.DOCUMENT}>
                <Documents />
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.Assign}>
                <Assignment/>

            </CustomTabPanel>
            <CustomTabPanel value={value} tabValue={TABS.Reports}>
                <Reports/>

            </CustomTabPanel>




        </>
    )

}

export default Asset;