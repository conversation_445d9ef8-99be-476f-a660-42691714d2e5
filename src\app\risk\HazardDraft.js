// @ts-nocheck
import React, { useState, useRef } from 'react';

import $ from "jquery";
import { Modal, Button, Form, Accordion, OverlayTrigger, Tooltip } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
import Swal from 'sweetalert2';
import { Typeahead } from 'react-bootstrap-typeahead';
import { GMS1_URL, GET_TEAM_MEMBERS_RISK,GET_USER_ROLE_BY_MODE, RISK_WITH_ID_URL,DRAFT_RA_WITH_ID, SENT_NOTIFICATION_MAIL, GET_ALL_USER, HAZARDS_CATEGOTY, RISK_UPDATES, RISK_UPDATE_WITH_ID_URL } from "../constants";
import { useEffect } from 'react';
import Select from 'react-select'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector, useDispatch } from 'react-redux'

import API from '../services/API';
import Switch from "react-switch";
import { useHistory, useParams, useLocation } from 'react-router-dom';
import SignatureCanvas from 'react-signature-canvas'
import DatePicker from "react-datepicker";
import moment from 'moment';


import "react-datepicker/dist/react-datepicker.css";
// @ts-ignore
import S3 from "react-aws-s3";
import { Buffer } from "buffer";
import TeamDeclaration from './components/TeamDeclaration';
import HZ from './components/HZ';

Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
window.jQuery = $;
// @ts-ignore
window.$ = $;
const config = {
    bucketName: "sagt",
    region: "ap-southeast-1",
    accessKeyId: "********************",
    secretAccessKey: "iuo0OyvIb5Fvwlq3t0uStZP9oUvmrLfggSj5YsOs",
};
const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})


const addTask1 = [
    { type: "textbox", label: "Sub-Activity Description", value: "" },
    { type: "hazards", option: [{ allhazards: [] }, { selected: [] }] },
    {
        type: "textbox1",
        variant: "consequence",
        option: [{ type: "textarea", label: "", value: "" }],
        button: true,
    },

    {
        type: "textbox1",
        variant: "current_control",
        option: [
            { type: "textarea", label: "Current Control", value: "", option: { type: "file", files: [] } },

        ],
        button: true,
    },
    {
        type: "initial",
        severity: [
            {
                type: "select1",
                label: "People",
                value: [
                    { "value": "1", "label": "Negligible (1)" },
                    { "value": "2", "label": "Minor (2)" },
                    { "value": "3", "label": "Moderate (3)" },
                    { "value": "4", "label": "Major (4)" },
                    { "value": "5", "label": "Catastrophic (5)" }
                ],
                selected: {},
                val: 0,
            },
        ],
        likelyhood: [
            {
                type: "select1",
                label: "People",
                value: [
                    { label: "Rare (1)", value: "1" },
                    { label: "Unlikely (2)", value: "2" },
                    { label: "Possible (3)", value: "3" },
                    { label: "Likely (4)", value: "4" },
                    { label: "Almost Certain (5)", value: "5" },
                ],
                selected: {},
                val: 0,
            },
        ],
        risk: [{ type: "People", label: "", color: "" }],
    },
    { type: "text", label: "is this Risk Level Acceptable ?" },

    {
        type: "checkbox1",
        variant: "additional",
        values: [
            { label: "Yes ", selected: true },
            { label: "No", selected: false },
        ],
        option: [
            {
                type: "textbox1",
                variant: "additional_control",
                option: [
                    {
                        label: "Addition Control Proposed",
                        type: "textbox1",
                        value: "",
                        option: [
                            { label: "Responsibility", value: "", type: "text" },
                            { label: "Date", type: "date", value: "" },
                        ],
                    },
                ],
            },
            { label: "Add Control", type: "button", value: "control" },

            {
                type: "rpn",
                severity: [
                    {
                        type: "select1",
                        label: "People",
                        value: [
                            { "value": "1", "label": "Negligible (1)" },
                            { "value": "2", "label": "Minor (2)" },
                            { "value": "3", "label": "Moderate (3)" },
                            { "value": "4", "label": "Major (4)" },
                            { "value": "5", "label": "Catastrophic (5)" }
                        ],
                        selected: {},
                        val: 0,
                    },
                ],
                likelyhood: [
                    {
                        type: "select1",
                        label: "People",
                        value: [
                            { label: "Rare (1)", value: "1" },
                            { label: "Unlikely (2)", value: "2" },
                            { label: "Possible (3)", value: "3" },
                            { label: "Likely (4)", value: "4" },
                            { label: "Almost Certain (5)", value: "5" },
                        ],
                        selected: {},
                        val: 0,
                    },
                ],
                risk: [{ type: "People", label: "", color: "" }],
            },
        ],
    },
]


const HazardDraft = (props) => {
    const history = useHistory()
    const description = useRef();
    const department = useRef();
    const location = useLocation()
    const title = useRef();
    const sign = useRef();
    const sign1 = useRef();
    const params = useParams()
    const review = useRef();
    const change = useRef();
    const reasonchange = useRef();
    const reference = useRef();
    const initial = useRef();
    const approve = useRef();
    const user = useSelector((state) => state.login.user)
    const defaultMaterialTheme = createTheme();
    const [departActivity, setDepartActivity] = useState([])
    const [selectedTypeDepart, setSelectedDepart] = useState([]);
    const [selectedTypeActivity, setSelectedActivity] = useState([]);
    const [depart, setDepart] = useState([])
    const [refreshToggle, setRefreshToggle] = useState(false);
    const [activity, setActivity] = useState([])
    const dispatch = useDispatch()
    const [amend, setAmend] = useState([])
    const [RPN, setRPN] = useState(0)
    const [color, setColor] = useState('')
    const [mdShow, setMdShow] = useState(false);
    const [attachmentShow, setAttachmentShow] = useState(false);
    const [viewAttachment, setViewAttachment] = useState([])
    const [isLoading, setIsLoading] = useState(false);
    const [crew, setCrew] = useState([]);
    const [meet, setMeet] = useState([])
    const [loader, setLoader] = useState(true)
    const [update, setUpdate] = useState([])
    const [selectedAttenteesValue, setSelectedAttenteesValue] = useState([]);
    const [selectedTypeValue, setSelectedTypeValue] = useState([]);
    const [selectedLeaderValue, setSelectedLeaderValue] = useState([]);
    const [selectedMemberValue, setSelectedMemberValue] = useState([]);
    const [selectedAssignValue, setSelectedAssignValue] = useState({});
    const [responsibility, setResponsibility] = useState([])
    const [sendShow, setSendShow] = useState(false)
    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };
    const matrix = {
        1: { 1: 1, 2: 2, 3: 3, 4: 4, 5: 5 },
        2: { 1: 2, 2: 4, 3: 6, 4: 8, 5: 10 },
        3: { 1: 3, 2: 6, 3: 9, 4: 12, 5: 15 },
        4: { 1: 4, 2: 8, 3: 12, 4: 16, 5: 20 },
        5: { 1: 5, 2: 10, 3: 15, 4: 20, 5: 25 },
    };
    const matrixColor = {
        1: { 1: "#8cc14b", 2: "#8cc14b", 3: "#8cc14b", 4: "#8cc14b", 5: "#ffef00" },
        2: { 1: "#8cc14b", 2: "#8cc14b", 3: "#ffef00", 4: "#ffef00", 5: "#ffef00" },
        3: { 1: "#8cc14b", 2: "#ffef00", 3: "#ffef00", 4: "#ffef00", 5: "#ff1900" },
        4: { 1: "#8cc14b", 2: "#ffef00", 3: "#ffef00", 4: "#ff1900", 5: "#ff1900" },
        5: { 1: "#ffef00", 2: "#ffef00", 3: "#ff1900", 4: "#ff1900", 5: "#ff1900" },
    };
    const columns = [

        {
            field: 'date',
            title: 'Date',


        },
        {
            field: 'reasonreview',
            title: 'Reason for Review',


        },
        {
            field: 'changes',
            title: 'Changes',

        },
        {
            field: 'reasonchange',
            title: 'Reason for Changes',


        },


        {
            field: 'initiatedBy',
            title: 'Initiated By',


        },
        {
            field: 'approvedBy',
            title: 'Approved By',


        },

        {
            field: 'reference',
            title: 'Reference',


        },


    ];
    const [task, setTask] = useState([
        [
            { type: "textbox", label: "Sub-Activity Description", value: "" },
            { type: "hazards", option: [{ allhazards: [] }, { selected: [] }] },

            {
                type: "textbox1",
                variant: "consequence",
                option: [{ type: "textarea", label: "", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} }],
                button: true,
            },

            {
                type: "textbox1",
                variant: "current_control",
                option: [
                    { type: "textarea", label: "Current Control", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} },

                ],
                button: true,
            },
            {
                type: "initial",
                severity: [
                    {
                        type: "select1",
                        label: "People",
                        value: [
                            { "value": "1", "label": "Negligible (1)" },
                            { "value": "2", "label": "Minor (2)" },
                            { "value": "3", "label": "Moderate (3)" },
                            { "value": "4", "label": "Major (4)" },
                            { "value": "5", "label": "Catastrophic (5)" }
                          ],
                        selected: {},
                        val: 0,
                    },
                ],
                likelyhood: [
                    {
                        type: "select1",
                        label: "People",
                        value: [
                            { label: "Rare (1)", value: "1" },
                            { label: "Unlikely (2)", value: "2" },
                            { label: "Possible (3)", value: "3" },
                            { label: "Likely (4)", value: "4" },
                            { label: "Almost Certain (5)", value: "5" },
                          ],
                        selected: {},
                        val: 0,
                    },
                ],
                risk: [{ type: "People", label: "", color: "" }],
            },
            { type: "text", label: "is this Risk Level Acceptable ?" },

            {
                type: "checkbox1",
                variant: "additional",
                values: [
                    { label: "Yes ", selected: true },
                    { label: "No", selected: false },
                ],
                option: [
                    {
                        type: "textbox1",
                        variant: "additional_control",
                        option: [
                            {
                                label: "Addition Control Proposed",
                                type: "textbox1",
                                value: "",
                                option: [
                                    { label: "Responsibility", value: "", type: "text" },
                                    { label: "Date", type: "date", value: "" },
                                ],
                                current_type: '',
                                selected: {}
                            },
                        ],
                    },
                    { label: "Add Control", type: "button", value: "control" },

                    {
                        type: "rpn",
                        severity: [
                            {
                                type: "select1",
                                label: "People",
                                value: [
                                    { "value": "1", "label": "Negligible (1)" },
                                    { "value": "2", "label": "Minor (2)" },
                                    { "value": "3", "label": "Moderate (3)" },
                                    { "value": "4", "label": "Major (4)" },
                                    { "value": "5", "label": "Catastrophic (5)" }
                                  ],
                                selected: {},
                                val: 0,
                            },
                        ],
                        likelyhood: [
                            {
                                type: "select1",
                                label: "People",
                                value: [
                                    { label: "Rare (1)", value: "1" },
                                    { label: "Unlikely (2)", value: "2" },
                                    { label: "Possible (3)", value: "3" },
                                    { label: "Likely (4)", value: "4" },
                                    { label: "Almost Certain (5)", value: "5" },
                                  ],
                                selected: {},
                                val: 0,
                            },
                        ],
                        risk: [{ type: "People", label: "", color: "" }],
                    },
                ],
            },
        ],
    ]);

    const [additon, setAddition] = useState([])

    useEffect(() => {
        getCrewList();
        getRisk();
        getUpdates();
        getWorkActivity();
        getAllResponsibility();
    }, [])
    const getAllResponsibility = async () => {
        const uriString = { include: [{ "relation": "workingGroup" }, { "relation": "designation" }, { "relation": "ghsOne" }] }
        const url = `${GET_ALL_USER}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {


            const depart = response.data.map(item => {
                return { value: item.id, label: item.firstName, email: item.email }
            })
            setResponsibility(depart)
        }

    }
    const getWorkActivity = async () => {
        const uriString = { include: ["ghsTwos"] };

        const url = `${GMS1_URL}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            console.log(response.data);
            setDepartActivity(response.data)
            const depart = response.data.map(item => {
                return { value: item.id, label: item.name }
            })
            setDepart(depart)
        }
    };
    const getUpdates = async () => {
        const response = await API.get(RISK_UPDATE_WITH_ID_URL(location.state.id));
        if (response.status === 200) {

            setUpdate(response.data)


        }
    }
    const handleRecommendationChange = (e) => {
        const t = meet;
        t.recommendation = e

        setMeet(t)
    }
    const getCrewList = async () => {
        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ra-member'
          });
        if (response.status === 200) {
            let data = [];

            response.data.map((item) => {
                let department = item.department ? item.department.name : '';
                if (item.id !== user.id) {
                    data.push({ label: item.firstName + ' - ' + department, value: item.id, department: department });
                }
            });
            setCrew(data);
        }
    }

    const getRisk = async () => {
        const response = await API.get(RISK_WITH_ID_URL(location.state.id));
        if (response.status === 200) {

            setMeet(response.data)

            setLoader(false)
        }
    }
    const onDeleteFileUpload = (j, i, k) => {
        const t = task;
        const text = t.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        if (item1.option.length !== 1) {
                            item1.option.splice(k, 1);
                        } else {
                            cogoToast.info("You Can't Delete Current Control  !", {
                                position: "top-right",
                            });
                        }
                    }
                });
            }

            return item;
        });
        setTask(text);
    };

    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };

    const amendUploads = async (e) => {
        console.log(e.target.files[0])
        const ReactS3Client = new S3(config);
        const filename = new Date().getTime() + e.target.files[0].name;

        await ReactS3Client.uploadFile(e.target.files[0],
            "uploads/risk_amends/" + filename
        )
            .then((data) => console.log(data))
            .catch((err) => console.error(err));

        const file = amend;
        file.push({ 'name': filename, 'type': e.target.files[0].type })
        console.log(file)
        setAmend(file)
        setRefreshToggle(!refreshToggle);
    }
    const updateHandler = async () => {
        const ReactS3Client = new S3(config);
        const filename = new Date().getTime() + 'edit_sign.png';

        await ReactS3Client.uploadFile(
            dataURItoFile(sign1.current.getTrimmedCanvas().toDataURL("image/png"), filename),
            "uploads/risk_sign/" + filename
        )
            .then((data) => console.log(data))
            .catch((err) => console.error(err));

        const response = await fetch(
            RISK_UPDATE_WITH_ID_URL(location.state.id),
            {
                method: 'POST',
                body: JSON.stringify({

                    reasonreview: review.current.value,
                    changes: change.current.value,
                    reasonchange: reasonchange.current.value,
                    initiatedBy: initial.current.value,
                    approvedBy: approve.current.value,
                    reference: reference.current.value,
                    attachment: amend,
                    sign: filename,



                }),
                headers: { "Content-type": "application/json; charset=UTF-8" }
            })
        if (response.ok) {



            createUserHandler();
        } else {
            //show error
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }

    }
    const handleAdditionalRecommendation = (e) => {
        const t = meet;
        t.additionRecommendation = e
        if (e.value !== 0) {
            t.eptw = true
        } else {
            t.eptw = false
        }

        setMeet(t)

    }
    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)
        let control = [];

        meet.task.map((item, i) => {

            let tempArray = [];

            item.map(item1 => {
                if (item1.variant === "current_control") {
                    item1.option.map(item2 => {
                        tempArray.push({ 'value': item2.value, 'files': item2.option.files });
                        return null;
                    });
                }
                return null;
            });

            control[i] = tempArray;
            return null;
        });

        const addition = []
        meet.task.map((item, i) => {
            if (item[5].values[0].selected) {
                addition.push('Yes')
            } else {
                addition.push('No')
            }



        })
        let additionControls = [];

        meet.task.map((item, i) => {

            let tempArray = [];

            item.map(item1 => {
                if (item1.variant === "additional") {
                    item1.option[0].option.map(ite => {
                        tempArray.push({ 'date': ite.option[1].value, 'name': ite.option[0].value, 'type': ite.current_type, 'value': ite.value });
                    })
                }
                return null;
            });

            additionControls[i] = tempArray;
            return null;
        });

        const response = await fetch(
            DRAFT_RA_WITH_ID (location.state.id),
            {
                method: 'PATCH',
                body: JSON.stringify({



                    member: meet.member,
                    department: meet.department,
                    task: meet.task,
                    teamMemberInvolved: meet.teamMemberInvolved,
                    recommendation: meet.recommendation,
                    additionRecommendation: meet.additionRecommendation,
                    eptw: meet.eptw,
                    currentcontrol: control,
                    additional: addition,
                    additionalDates: additionControls,
                    status:'1'


                }),
                headers: { "Content-type": "application/json; charset=UTF-8" }
            })

        if (response.ok) {



            customSwal2.fire(
                'Risk Assessment Updated!',
                '',
                'success'
            )
            setIsLoading(false)
            localStorage.setItem('ra_url','DASHBOARD')
            history.go(0);
           

        } else {
            //show error
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }

        setMdShow(false)
        getUpdates();

    }




    const handleTypeChange = (selectedOptions) => {

        const m = meet
        m.type = selectedOptions
        setMeet(m)
        setSelectedTypeValue(selectedOptions)
    }
    const handleLeaderChange = (selectedOptions) => {
        const m = meet
        m.leader = selectedOptions
        setMeet(m)
        setSelectedLeaderValue(selectedOptions)
    }
    const handleMemberChange = (selectedOptions) => {
        const m = meet
        m.member = selectedOptions;

        const sign1 = selectedOptions.map((item) => {
            return { label: item.label, sign: 'No', id: item.value, date: '', department: item.department }
        })
        const differences = [];
        sign1.forEach(item1 => {
            if (!m.teamMemberInvolved.some(item2 => item2.id === item1.id)) {
                differences.push(item1);
            }
            return differences;
        });
        const remove = [];
        m.teamMemberInvolved.forEach(item1 => {
            if (!sign1.some(item2 => item2.id === item1.id)) {
                remove.push(item1);
            }
            return remove;
        });
        if (differences.length !== 0) {
            differences.map(item => {
                m.teamMemberInvolved.push(item)
            })

        }
        if (remove.length !== 0) {
            remove.map(item1 => {
                m.teamMemberInvolved = m.teamMemberInvolved.filter(item => item.id !== item1.id);
            })

        }


        setMeet(m)
        setSelectedMemberValue(selectedOptions)
    }
    const handleChange = (selectedOptions) => {
        setSelectedAttenteesValue(selectedOptions)
    }
    const handleSelectChange = (e, j, i, k, name, variant) => {


        //  if (name === 'Personnel') {

        if (variant === "severity") {
            var likelyhood = 0;
            const t = meet;
            const text = t.task.map((item, ii) => {
                if (i === ii) {
                    item.map((item1, jj) => {
                        if (jj === j) {
                            likelyhood = item1.likelyhood[k].val;
                            item1.severity[k].selected = e;
                            item1.severity[k].val = e.value;
                            item1.risk[k].label = getMatrixValue(e.value, likelyhood)
                            item1.risk[k].color = getCalculatedColor(e.value, likelyhood);
                        }
                    });
                }

                return item;
            });
            setTask(text);
        } else {
            var likelyhood = 0;
            const t = meet;
            const text = t.task.map((item, ii) => {
                if (i === ii) {
                    item.map((item1, jj) => {
                        if (jj === j) {
                            likelyhood = item1.severity[k].val;
                            item1.likelyhood[k].selected = e;
                            item1.likelyhood[k].val = e.value;
                            item1.risk[k].label = getMatrixValue(e.value, likelyhood)
                            item1.risk[k].color = getCalculatedColor(e.value, likelyhood);
                        }
                    });
                }

                return item;
            });
            setTask(text);
        }


    };

    const onDeleteFiles = async (j, i, k, f) => {
        const t = meet;
        let text = []

        text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map(async (item2, kk) => {
                            if (kk === k) {
                                item2.option.files.splice(f, 1);

                            }
                        });
                    }
                });
            }

            return item;
        });



        console.log(text)


        setTask(text);
    }
    const getMatrixValue = (row, col) => {
        return matrix[row][col];
    };
    const getCalculatedColor = (row, col) => {
        return matrixColor[row][col];
    };
    const handleSelectRPNChange = (e, j, i, k, l, name, variant) => {
        if (variant === "severity") {
            var likelyhood = 0;
            const t = meet;
            const text = t.task.map((item, ii) => {
                if (i === ii) {
                    item.map((item1, jj) => {
                        if (jj === j) {
                            item1.option.map((item2, kk) => {
                                if (kk === k) {
                                    item2.severity.map((item3, ll) => {
                                        if (ll === l) {

                                            likelyhood = item2.likelyhood[l].val;
                                            item3.selected = e;
                                            item3.val = e.value;
                                            item2.risk[l].label = getMatrixValue(e.value, likelyhood);
                                            item2.risk[l].color = getCalculatedColor(
                                                e.value,
                                                likelyhood
                                            );
                                        }
                                    });
                                }


                            });
                        }
                    });
                }

                return item;
            });

            setTask(text);
        } else {
            var likelyhood = 0;
            const t = meet;
            const text = t.task.map((item, ii) => {
                if (i === ii) {
                    item.map((item1, jj) => {
                        if (jj === j) {
                            item1.option.map((item2, kk) => {
                                if (kk === k) {
                                    item2.likelyhood.map((item3, ll) => {
                                        if (ll === l) {
                                            likelyhood = item2.severity[l].val;
                                            item3.selected = e;
                                            item3.val = e.value;
                                            item2.risk[l].label = getMatrixValue(e.value, likelyhood);
                                            item2.risk[l].color = getCalculatedColor(
                                                e.value,
                                                likelyhood
                                            );
                                        }
                                    });
                                }


                            });
                        }
                    });
                }

                return item;
            });
            setTask(text);
        }
    };
    const addTask = async () => {
        let addT = [
            { type: "textbox", label: "Sub-Activity Description", value: "" },
            { type: "hazards", option: [{ allhazards: [] }, { selected: [] }] },

            {
                type: "textbox1",
                variant: "consequence",
                option: [{ type: "textarea", label: "", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} }],
                button: true,
            },

            {
                type: "textbox1",
                variant: "current_control",
                option: [
                    { type: "textarea", label: "Current Control", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} },

                ],
                button: true,
            },
            {
                type: "initial",
                severity: [
                    {
                        type: "select1",
                        label: "People",
                        value: [
                            { "value": "1", "label": "Negligible (1)" },
                            { "value": "2", "label": "Minor (2)" },
                            { "value": "3", "label": "Moderate (3)" },
                            { "value": "4", "label": "Major (4)" },
                            { "value": "5", "label": "Catastrophic (5)" }
                          ],
                        selected: {},
                        val: 0,
                    },
                ],
                likelyhood: [
                    {
                        type: "select1",
                        label: "People",
                        value: [
                            { label: "Rare (1)", value: "1" },
                            { label: "Unlikely (2)", value: "2" },
                            { label: "Possible (3)", value: "3" },
                            { label: "Likely (4)", value: "4" },
                            { label: "Almost Certain (5)", value: "5" },
                          ],
                        selected: {},
                        val: 0,
                    },
                ],
                risk: [{ type: "People", label: "", color: "" }],
            },
            { type: "text", label: "is this Risk Level Acceptable ?" },

            {
                type: "checkbox1",
                variant: "additional",
                values: [
                    { label: "Yes ", selected: true },
                    { label: "No", selected: false },
                ],
                option: [
                    {
                        type: "textbox1",
                        variant: "additional_control",
                        option: [
                            {
                                label: "Addition Control Proposed",
                                type: "textbox1",
                                value: "",
                                option: [
                                    { label: "Responsibility", value: "", type: "text" },
                                    { label: "Date", type: "date", value: "" },
                                ],
                                current_type: '',
                                selected: {}
                            },
                        ],
                    },
                    { label: "Add Control", type: "button", value: "control" },

                    {
                        type: "rpn",
                        severity: [
                            {
                                type: "select1",
                                label: "People",
                                value: [
                                    { "value": "1", "label": "Negligible (1)" },
                                    { "value": "2", "label": "Minor (2)" },
                                    { "value": "3", "label": "Moderate (3)" },
                                    { "value": "4", "label": "Major (4)" },
                                    { "value": "5", "label": "Catastrophic (5)" }
                                  ],
                                selected: {},
                                val: 0,
                            },
                        ],
                        likelyhood: [
                            {
                                type: "select1",
                                label: "People",
                                value: [
                                    { label: "Rare (1)", value: "1" },
                                    { label: "Unlikely (2)", value: "2" },
                                    { label: "Possible (3)", value: "3" },
                                    { label: "Likely (4)", value: "4" },
                                    { label: "Almost Certain (5)", value: "5" },
                                  ],
                                selected: {},
                                val: 0,
                            },
                        ],
                        risk: [{ type: "People", label: "", color: "" }],
                    },
                ],
            },
        ]
        const uriString = { include: ['hazards'] }

        const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const response = await API.get(url);
        if (response.status === 200) {

            response.data.sort((a, b) => (a.order > b.order) ? 1 : -1)

            const newT = addT.map((item1, j) => {

                if (item1.type === 'hazards') {

                    item1.option[0].allhazards = response.data.filter(item => item.name === 'Hazard-Based')
                }
                return item1
            })
            const t = meet;
            t.additional.push('Yes')
            t.additionalDates.push([{ 'control': '', 'name': '', 'date': '' }])
            const text = t.task.push(newT)


            setTask(text)

        }



        // setTask((prev) => ([...prev, addTask1]))

    }
    const handleSelectInChange = (e, k, j, i) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.value = e
                            }

                        })
                    }

                })
            }

            return item
        })
        setTask(text)
    }
    const onchangeSelectOpt = (e, k, j, i) => {
        const t = task;
        const text = t.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.selected = e
                            }

                        })
                    }

                })
            }

            return item
        })
        setTask(text)
    }
    const onchangeSelect = (e, j, i) => {
        console.log(e)
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.selected = e
                    }

                })
            }

            return item
        })
        setTask(text)

    }
    const onClickButton1 = (e, j, i, k) => {
        console.log(e)
        e.preventDefault();
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {

                        item1.option[k].option.push({ 'label': 'Addition Control Proposed', 'type': 'textbox1', 'value': '', 'option': [{ 'label': 'Responsibility', 'value': '', 'type': 'text' }, { 'label': 'Date', 'type': 'date', 'value': '' }] })


                        // item[j - 1].option.push({ 'type': 'textbox1', 'option': [{ 'label': 'Addition Control Proposed', 'type': 'textbox1', 'value': '', 'option': [{ 'label': 'Responsibility', 'value': '', 'type': 'text' }, { 'label': 'Date', 'type': 'date', 'value': '' }] }] })

                    }

                })
            }

            return item
        })
        t.additionalDates[i].push({ 'control': '', 'name': '', 'date': '' })
        setTask(text)

    }

    const onchangeBox1Text = (e, j, i, k, l) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.option.map((item3, ll) => {
                                    if (ll === l) {
                                        item3.value = e.target.value
                                    }
                                })
                            }
                        })

                    }
                })
            }

            return item
        })
        t.additionalDates[i][l].control = e.target.value
        setTask(text)
    }
    const onDeleteTextBox = (j, i, k) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {

                        if (item1.option.length !== 1) {
                            item1.option.splice(k, 1);
                        } else {
                            cogoToast.info("You Can't Delete Consequence  !", {
                                position: "top-right",
                            });
                        }

                    }
                });
            }

            return item
        })
        setTask(text)
    }
    const onDeleteTextBox1 = (j, i, k, l) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        // delete item1.option[k]

                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                if (item2.option.length !== 1) {
                                    item2.option.splice(l, 1);
                                } else {
                                    cogoToast.info("You Can't Delete Additional Current Control  !", {
                                        position: "top-right",
                                    });
                                }
                            }
                        });
                    }

                })
            }

            return item
        })
        t.additionalDates[i].splice(l, 1)
        setTask(text)
    }
    const arraysAreEqual = (arr1, arr2) => {
        if (arr1.length !== arr2.length) {
            return false;
        }

        for (let i = 0; i < arr1.length; i++) {
            if (arr1[i] !== arr2[i]) {
                return false;
            }
        }

        return true;
    }
    const onDeleteTask = (e, i) => {
        e.stopPropagation();
        const t = meet
        if (t.task.length !== 1) {
            const indexToRemove = t.task.findIndex(arr => arraysAreEqual(arr, t.task[i]));
            if (indexToRemove !== -1) {
                t.task.splice(indexToRemove, 1);
            }
            setMeet(t);
            setRefreshToggle(!refreshToggle);
        } else {
            cogoToast.info("You Can't Delete Sub-Activity !", {
                position: "top-right",
            });
        }
    }
    const onchangeBox2Text = (e, j, i, k, l, m) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.option.map((item3, ll) => {
                                    if (ll === l) {
                                        item3.option.map((item4, mm) => {
                                            if (mm === m) {
                                                item4.value = e
                                            }
                                        })
                                    }
                                })
                            }
                        })

                    }
                })
            }

            return item
        })
        t.additionalDates[i][l].name = e
        setTask(text)
    }
    const handleDateChange = (e, j, i, k, l, m) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.option.map((item3, ll) => {
                                    if (ll === l) {
                                        item3.option.map((item4, mm) => {
                                            if (mm === m) {
                                                item4.value = e
                                            }
                                        })
                                    }
                                })
                            }
                        })

                    }
                })
            }

            return item
        })
        t.additionalDates[i][l].date = e
        setTask(text)
    }
    const onClickButton = (e, j, i) => {

        e.preventDefault();
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {

                if (item[j].variant === "consequence") {
                    item[j].option.push({ type: "textarea", label: "", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} });
                } else if (item[j].variant === "current_control") {
                    item[j].option.push(
                        {
                            type: "textarea",
                            label: "",
                            value: "",
                            option: { type: "file", files: [] }
                        },

                    );
                } else if (item[j].value === "control") {

                    item[j - 1].option.push({
                        type: "textbox1",
                        option: [
                            {
                                label: "Addition Control Proposed",
                                type: "textbox1",
                                value: "",
                                option: [
                                    { label: "Responsibility", value: "", type: "text" },
                                    { label: "Date", type: "date", value: "" },
                                ],
                                current_type: '',
                                seleted: {}
                            },
                        ],
                    });
                }

            }

            return item;
        });
        setTask(text);
    };
    const onchangeText = (e, j, i) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.value = e.target.value
                    }

                })
            }

            return item
        })
        setTask(text)
    }
    const onchangeText1 = (e, j, i, k) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.value = e.target.value
                            }
                        })
                    }

                })
            }

            return item
        })
        setTask(text)
    }

    const toggleButton = (e, j, i, v) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {

                        console.log(item1)
                        item1.values.map((item2) => {
                            item2.selected = false
                        })
                        item1.values[v].selected = true
                        t.additional[i] = item1.values[v].label
                    }

                })
            }

            return item
        })
        setTask(text)
    }

    const handleAssignChange = (e) => {
        const m = meet

        m.userId = e.value

        setMeet(m)
        setSelectedAssignValue(e)
    }
    const onChangeActivity = (e) => {

        const m = meet

        m.activity = e.target.value

        setMeet(m)
    }
    const onChangeDepartment = (e) => {

        const m = meet

        m.department = e.target.value

        setMeet(m)
    }
    const onchangeConsequencefiles = async (e, j, i, k) => {
        const t = meet;
        let text = [];
        const ReactS3Client = new S3(config);
        const filename = new Date().getTime() + e.target.files[0].name;


        await ReactS3Client.uploadFile(
            e.target.files[0], "uploads/consequence/" + filename

        )
            .then((data) => {
                text = t.task.map((item, ii) => {
                    if (i === ii) {
                        item.map((item1, jj) => {
                            if (jj === j) {
                                item1.option.map(async (item2, kk) => {
                                    if (kk === k) {
                                        item2.option.files = []
                                        item2.option.files.push(filename);
                                    }
                                });
                            }
                        });
                    }

                    return item;
                });

            })
            .catch((err) => console.error(err));


        setTask(text);
    };
    const onchangefiles = async (e, j, i, k) => {
        const t = meet;
        let text = []
        const ReactS3Client = new S3(config);
        const filename = new Date().getTime() + e.target.files[0].name;

        await ReactS3Client.uploadFile(e.target.files[0]
            ,
            "uploads/current_control/" + filename
        )
            .then((data) => {
                text = t.task.map((item, ii) => {
                    if (i === ii) {
                        item.map((item1, jj) => {
                            if (jj === j) {
                                item1.option.map(async (item2, kk) => {
                                    if (kk === k) {
                                        item2.option.files = []
                                        item2.option.files.push(filename)

                                    }
                                });
                            }
                        });
                    }

                    return item;
                });
                console.log(data)

            })
            .catch((err) => console.error(err));
        console.log(filename);




        setTask(text);
    }
    const onDeleteAmendFiles = (f) => {
        let file = amend

        file.splice(f, 1);
        setAmend(file)
        setRefreshToggle(!refreshToggle);
    }
    const onClickHazard = (e, i, j, c, h, ca, ha) => {
        console.log(j, i, ca, ha)

        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option[0].allhazards.map((item2, k) => {
                            if (k === c) {
                                item2.hazards.map((it => delete it.active))
                                item2.hazards.map(async (item3, m) => {
                                    if (m === h) {
                                        // if (item3.active === true) {

                                        //   item1.option[1].selected = item1.option[1].selected.filter((item) => item.id !== item3.id);
                                        // } else {
                                        item3.active = true;
                                        // const imageDataUrl = await convertImageUrlToDataUrl('https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/'+item3.image)
                                        // // console.log(imageDataUrl)
                                        // item3.dataURI =imageDataUrl
                                        item1.option[1].selected = []
                                        item1.option[1].selected.push(item3)



                                        // }
                                    }
                                });
                            }
                        })
                    }
                })
            }
            return item
        })

        setTask(text)
    }
    const handleDepartChange = (option) => {
        const m = meet
        m.department = option.label
        setMeet(m)

        setSelectedDepart(option)
        setActivity([])
        const active = departActivity.filter(item => item.id === option.value);

        const final = active[0].ghsTwos.map(item => {
            return { label: item.name, value: item.id }
        })

        setActivity(final)

    }
    const handleActivityChange = (selectedOptions) => {
        const m = meet
        m.activity = selectedOptions.label
        setMeet(m)

        setSelectedActivity(selectedOptions)
    };
    const onUpTask = (e, i) => {
        e.stopPropagation();
        setMeet(prevArrays => {
            const newArrayOfArrays = [...prevArrays];
            [newArrayOfArrays[i], newArrayOfArrays[i - 1]] = [newArrayOfArrays[i - 1], newArrayOfArrays[i]];
            return newArrayOfArrays;
        });
    }
    const onDownTask = (e, i) => {
        e.stopPropagation();
        setMeet(prevArrays => {
            const newArrayOfArrays = [...prevArrays];
            [newArrayOfArrays[i], newArrayOfArrays[i + 1]] = [newArrayOfArrays[i + 1], newArrayOfArrays[i]];
            return newArrayOfArrays;
        });
    }
    const changeTypeControl = (e, j, i, k) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.selected = e;
                                item2.current_type = e.value;
                            }
                        });
                    }
                });
            }

            return item;
        });
        setTask(text);
    }
    const changeTypeAdditionalControl = (e, j, i, k, l) => {
        const t = meet;
        const text = t.task.map((item, ii) => {
            if (i === ii) {
                item.map((item1, jj) => {
                    if (jj === j) {
                        item1.option.map((item2, kk) => {
                            if (kk === k) {
                                item2.option.map((item3, ll) => {
                                    if (ll === l) {
                                        item3.selected = e;
                                        item3.current_type = e.label
                                    }
                                });
                            }
                        });
                    }
                });
            }

            return item;
        });

        setTask(text);
    }
    const sendNotification = async () => {
        const m = meet

        const response = await API.post(SENT_NOTIFICATION_MAIL, {

            activity: { label: '', value: '' },
            depart: { label: m.department, value: m.department },
            member: meet.member,
            leader: meet.leader,
        })
        console.log(response.status)
        if (response.status === 200) {
            customSwal2.fire("Notification Sent!", "", "success");
            setSendShow(false)
        }

    }
    return (
        <>
            {loader === false ?
                <div>
                    {console.log(meet)}
                    <div className="row">
                        <div className="col-12">
                            <div className="card">
                                <div className="card-body">

                                    <h4 className="card-title">Edit RA </h4>
                                    <div className="row">
                                        <div className="col-12">

                                            <form className="forms">
                                                {/* <div className="col-6">
                          <div className="form-group required">
                            <label htmlFor="user_category">Type of RA</label>
                            <Select
                              labelKey="label"
                              id="user_description"
                              onChange={handleTypeChange}
                              options={[
                                {
                                  label: "Routine Work",
                                  value: "Routine Work"
                                },
                                {
                                  label: "Non-Routine Work",
                                  value: "Non-Routine Work"
                                },

                              ]}
                              value={meet.type}
                              placeholder="Type..."
                            />
                          </div>
                        </div> */}
                                                {/* {meet.type.label === 'Routine Work' ?
                          <div className="row">
                            <div className="col-6">
                              <div className="form-group required">
                                <label htmlFor="user_name">
                                  Department
                                </label>
                                <Select
                                  labelKey="label"
                                  id="user_description"
                                  onChange={handleDepartChange}
                                  options={depart}
                                  placeholder="Type..."
                                  defaultValue={{ 'label': meet.department, 'value': meet.department }}
                                />
                              </div>
                            </div>

                            <div className="col-6">
                              <div className="form-group required">
                                <label htmlFor="user_name">Process / Work Activity</label>
                                <Select
                                  labelKey="label"
                                  id="user_description"
                                  onChange={handleActivityChange}
                                  options={activity}
                                  placeholder="Type..."
                                  defaultValue={{ 'label': meet.activity, 'value': meet.activity }}
                                />
                              
                              </div>
                            </div>
                          </div>

                          :
                          <div className="row">
                            <div className="col-6">
                              <div className="form-group required">
                                <label htmlFor="user_name">
                                  Process / Work Activity
                                </label>
                                <textarea
                                  className="form-control"
                                  ref={description}
                                  id="user_name"
                                  defaultValue={meet.activity}
                                  onChange={(e) => onChangeActivity(e)}
                                ></textarea>
                              </div>
                            </div>

                            <div className="col-6">
                              <div className="form-group required">
                                <label htmlFor="user_name">Department</label>
                                <textarea
                                  className="form-control"
                                  ref={department}
                                  id="user_name"
                                  placeholder={
                                    "Department / Function / Project Team"
                                  }
                                  onChange={(e) => onChangeDepartment(e)}
                                  defaultValue={meet.department}
                                ></textarea>
                               
                              </div>
                            </div>
                          </div>} */}
                                                <div className='row'>
                                                    <div className="col-6">
                                                        <div className="form-group required">
                                                            <label htmlFor="user_name">Department</label>
                                                            <textarea
                                                                className="form-control"
                                                                ref={department}
                                                                id="user_name"
                                                                placeholder={
                                                                    "Department / Function / Project Team"
                                                                }
                                                                onChange={(e) => onChangeDepartment(e)}
                                                                defaultValue={meet.department}
                                                            ></textarea>

                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="row">
                                                    <div className="col-12">
                                                        <div className="form-group required">
                                                            <label htmlFor="user_category">
                                                                RA Team Members
                                                            </label>
                                                            <Select
                                                                labelKey="label"
                                                                id="user_description"
                                                                onChange={handleMemberChange}
                                                                options={crew}
                                                                isMulti={true}
                                                                placeholder="Choose Members.."
                                                                value={meet.member}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                            {/* {console.log(task)} */}
                                            {sendShow === true &&
                                                <div className="row">
                                                    <div className="col-12 d-flex justify-content-center">
                                                        <button
                                                            type="button"
                                                            className="btn btn-primary btn-rounded mb-3 "
                                                            onClick={(e) => {
                                                                e.preventDefault();

                                                                sendNotification();

                                                            }}
                                                        >
                                                            Send Notification
                                                        </button>
                                                    </div>
                                                </div>
                                            }
                                            <>

                                                <div className='tasks'>
                                                    {meet.task.map((item1, i) => {
                                                        return (
                                                            < >




                                                                <>
                                                                    <div
                                                                        className="col-12 add-task"
                                                                        style={{
                                                                            padding: 40,
                                                                            marginTop: 20,
                                                                            boxShadow:
                                                                                "rgb(0 0 0 / 20%) 0px 0px 10px 0px",
                                                                        }}
                                                                    >
                                                                        {/* <h3 style={{ textAlign: 'center' }}></h3> */}
                                                                        {item1.map((item, j) => {
                                                                            if (item.type === "textbox") {
                                                                                return (
                                                                                    <div className="form-group required">
                                                                                        <label htmlFor="user_name">
                                                                                            {item.label}
                                                                                        </label>
                                                                                        <Form.Control
                                                                                            type="text"
                                                                                            onChange={(e) =>
                                                                                                onchangeText(e, j, i)
                                                                                            }
                                                                                            value={item.value}
                                                                                        />
                                                                                    </div>
                                                                                );
                                                                            }else if (item.type === "hazards1"){
                                                                                return(
                                                                                    <HZ item={item} i={i} j={j} onchangeText12={onchangeText1} onDeleteTextBox1={onDeleteTextBox} onDeleteFiles1={onDeleteFiles} onchangeConsequencefiles1={onchangeConsequencefiles} onClickButton1={onClickButton} changeTypeControl1={changeTypeControl}/>
                                                                                )

                                                                            } else if (item.variant === "consequence") {
                                                                                return (
                                                                                    <div
                                                                                        className="row mb-5"
                                                                                        style={{
                                                                                            padding: 20,
                                                                                            boxShadow:
                                                                                                "0px 0px 10px 4px #0000001f",
                                                                                        }}
                                                                                    >
                                                                                        <div
                                                                                            className="form-group mt-5 mb-3"
                                                                                            style={{ textAlign: "center" }}
                                                                                        >
                                                                                            <label
                                                                                                htmlFor="user_name"
                                                                                                style={{ fontSize: 20 }}
                                                                                            >
                                                                                                Consequence
                                                                                            </label>
                                                                                        </div>
                                                                                        <div
                                                                                            className="form-group mt-3"
                                                                                            style={{ textAlign: "center" }}
                                                                                        >
                                                                                            <label
                                                                                                htmlFor="user_name"
                                                                                                style={{ fontSize: 15 }}
                                                                                            >
                                                                                              Identify the potential consequence on Safety, Environment, Financial, Security and Community / Brand Exposure due to presence of above hazards.
                                                                                            </label>
                                                                                        </div>

                                                                                        <div className="textbox1">
                                                                                            {item.option.map((opt, k) => {
                                                                                                return (
                                                                                                    <div className="row mb-4 d-flex align-items-center">
                                                                                                        <div className="col-1 p-0">
                                                                                                            <span className="span-circle number"> {k + 1}</span>
                                                                                                        </div>
                                                                                                        <div className="col-3">
                                                                                                            <Select
                                                                                                                labelKey="label"
                                                                                                                id="user_description"
                                                                                                                onChange={(e) => changeTypeControl(e, j, i, k)}
                                                                                                                options={[
                                                                                                                    { 'label': 'Personnel', 'value': 'Personnel' },
                                                                                                                    { 'label': 'Property', 'value': 'Property' },
                                                                                                                    { 'label': 'Environment', 'value': 'Environment' },
                                                                                                                    { 'label': 'Service Loss', 'value': 'Service Loss' },
                                                                                                            ]}
                                                                                                                placeholder="Choose Type.."
                                                                                                                defaultValue={opt.selected}

                                                                                                            />
                                                                                                        </div>
                                                                                                        <div className="col-7 p-0">
                                                                                                            <Form.Control
                                                                                                                type="text"
                                                                                                                onChange={(e) =>
                                                                                                                    onchangeText1(e, j, i, k)
                                                                                                                }
                                                                                                                value={opt.value}
                                                                                                                className="m-0"
                                                                                                            />
                                                                                                        </div>
                                                                                                        <div className="col-1 p-0">
                                                                                                            <i
                                                                                                                className="mdi mdi-delete span-circle delete"
                                                                                                                onClick={() =>
                                                                                                                    onDeleteTextBox(j, i, k)
                                                                                                                }
                                                                                                            ></i>
                                                                                                        </div>
                                                                                                        {opt.option &&
                                                                                                            opt.option.type === 'file' ?
                                                                                                            <div className="form-group">
                                                                                                                {opt.option.files.length !== 0 && (
                                                                                                                    <div
                                                                                                                        className="row mt-3"
                                                                                                                        style={{
                                                                                                                            padding: 10,
                                                                                                                            border: '1px dashed',
                                                                                                                            borderRadius: 10

                                                                                                                        }}
                                                                                                                    >
                                                                                                                        {opt.option.files.map(
                                                                                                                            (files, f) => {
                                                                                                                                return (
                                                                                                                                    <div
                                                                                                                                        className="col-12 d-flex align-items-center justify-content-center"
                                                                                                                                        style={{
                                                                                                                                            position:
                                                                                                                                                "relative",

                                                                                                                                        }}
                                                                                                                                    >
                                                                                                                                        <img
                                                                                                                                            style={{

                                                                                                                                                maxHeight:
                                                                                                                                                    "100%",
                                                                                                                                                maxWidth: "100%"
                                                                                                                                            }}
                                                                                                                                            src={
                                                                                                                                                "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/consequence/" +
                                                                                                                                                files
                                                                                                                                            }
                                                                                                                                            alt="test"
                                                                                                                                        />
                                                                                                                                        <i
                                                                                                                                            className="mdi mdi-delete taskdelete"
                                                                                                                                            style={{
                                                                                                                                                position: 'absolute',
                                                                                                                                                top: '-8px',
                                                                                                                                                right: 22,
                                                                                                                                                color: 'red'
                                                                                                                                            }}
                                                                                                                                            onClick={() =>
                                                                                                                                                onDeleteFiles(
                                                                                                                                                    j,
                                                                                                                                                    i,
                                                                                                                                                    k,
                                                                                                                                                    f
                                                                                                                                                )
                                                                                                                                            }
                                                                                                                                        ></i>
                                                                                                                                    </div>
                                                                                                                                );
                                                                                                                            }
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                )}

                                                                                                                <label
                                                                                                                    className="d-flex justify-content-center mt-4"
                                                                                                                    htmlFor={"filescons" + k + i}
                                                                                                                    style={{ position: 'relative' }}
                                                                                                                >
                                                                                                                    {opt.option.files.length === 0 ?
                                                                                                                        <i
                                                                                                                            style={{
                                                                                                                                fontSize: 55,
                                                                                                                                padding: 10,
                                                                                                                                border: "1px dashed",
                                                                                                                                borderRadius: 10,
                                                                                                                            }}
                                                                                                                            className="typcn typcn-cloud-storage-outline"
                                                                                                                        ></i>
                                                                                                                        :
                                                                                                                        <i
                                                                                                                            style={{
                                                                                                                                fontSize: 55,
                                                                                                                                padding: 10,
                                                                                                                                border: "1px dashed",
                                                                                                                                borderRadius: 10,
                                                                                                                            }}
                                                                                                                            className="ti-exchange-vertical"
                                                                                                                        ></i>
                                                                                                                    }
                                                                                                                    <span style={{ position: 'absolute', bottom: 0, right: 0 }}>*png,gif,jpeg</span>
                                                                                                                </label>
                                                                                                                <Form.Control
                                                                                                                    type="file"
                                                                                                                    id={"filescons" + k + i}
                                                                                                                    style={{
                                                                                                                        display: "none",
                                                                                                                    }}
                                                                                                                    accept="image/png, image/gif, image/jpeg"
                                                                                                                    onChange={(e) =>
                                                                                                                        onchangeConsequencefiles(
                                                                                                                            e,
                                                                                                                            j,
                                                                                                                            i,
                                                                                                                            k

                                                                                                                        )
                                                                                                                    }
                                                                                                                />
                                                                                                            </div>

                                                                                                            : ''}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                        <div
                                                                                            className="form-group"
                                                                                            style={{ textAlign: "center" }}
                                                                                        >
                                                                                            <button
                                                                                                className="btn btn-primary"
                                                                                                onClick={(e) =>
                                                                                                    onClickButton(e, j, i)
                                                                                                }
                                                                                            >
                                                                                                <i className="mdi mdi-plus"></i>
                                                                                            </button>
                                                                                        </div>
                                                                                    </div>
                                                                                );
                                                                            } else if (
                                                                                item.variant === "current_control"
                                                                            ) {
                                                                                return (
                                                                                    <div
                                                                                        className="row mb-5 mt-5"
                                                                                        style={{
                                                                                            padding: 20,
                                                                                            boxShadow:
                                                                                                "0px 0px 10px 4px #0000001f",
                                                                                        }}
                                                                                    >
                                                                                        <div
                                                                                            className="form-group mt-5 mb-3"
                                                                                            style={{ textAlign: "center" }}
                                                                                        >
                                                                                            <label
                                                                                                htmlFor="user_name"
                                                                                                style={{ fontSize: 20 }}
                                                                                            >
                                                                                                Current Controls
                                                                                            </label>
                                                                                        </div>
                                                                                        <div
                                                                                            className="form-group mt-1 mb-3"
                                                                                            style={{ textAlign: "center" }}
                                                                                        >
                                                                                            <label
                                                                                                htmlFor="user_name"
                                                                                                style={{ fontSize: 15 }}
                                                                                            >
                                                                                                Existing measures in place to
                                                                                                mitigate or manage the above
                                                                                                identified hazards and potential
                                                                                                consequences.
                                                                                            </label>
                                                                                        </div>

                                                                                        <div className="textbox1">
                                                                                            {item.option.map((opt, k) => {

                                                                                                if (opt.type === "textarea") {
                                                                                                    return (
                                                                                                        <div className="row mb-4 d-flex align-items-center">
                                                                                                            <div className="col-1 p-0">
                                                                                                                <span className="span-circle number"> {k + 1}</span>
                                                                                                            </div>
                                                                                                            <div className="col-3">
                                                                                                                <Select
                                                                                                                    labelKey="label"
                                                                                                                    id="user_description"
                                                                                                                    onChange={(e) => changeTypeControl(e, j, i, k)}
                                                                                                                    options={[{ 'label': 'No Control', 'value': 'No Control' },
                                                                                                                    { 'label': 'Engineering', 'value': 'Engineering' },
                                                                                                                    { 'label': 'Administrative', 'value': 'Administrative' },
                                                                                                                    { 'label': 'PPE', 'value': 'PPE' }]}
                                                                                                                    placeholder="Choose Type.."
                                                                                                                    defaultValue={opt.selected}

                                                                                                                />
                                                                                                            </div>
                                                                                                            <div className="col-7 p-0">
                                                                                                                <Form.Control
                                                                                                                    type="text"
                                                                                                                    onChange={(e) =>
                                                                                                                        onchangeText1(
                                                                                                                            e,
                                                                                                                            j,
                                                                                                                            i,
                                                                                                                            k
                                                                                                                        )
                                                                                                                    }
                                                                                                                    value={opt.value}
                                                                                                                    className="m-0"
                                                                                                                />
                                                                                                            </div>
                                                                                                            <div className="col-1 p-0">
                                                                                                                <i
                                                                                                                    className="mdi mdi-delete span-circle delete"
                                                                                                                    onClick={() =>
                                                                                                                        onDeleteFileUpload(j, i, k)
                                                                                                                    }
                                                                                                                ></i>
                                                                                                            </div>
                                                                                                            {opt.option.type === 'file' ?
                                                                                                                <div className="form-group">
                                                                                                                    {opt.option.files.length !== 0 && (
                                                                                                                        <div
                                                                                                                            className="row mt-4"
                                                                                                                            style={{

                                                                                                                                padding: 10,
                                                                                                                                border: '1px dashed',
                                                                                                                                borderRadius: 10
                                                                                                                            }}
                                                                                                                        >
                                                                                                                            {opt.option.files.map(
                                                                                                                                (files, f) => {
                                                                                                                                    return (
                                                                                                                                        <div
                                                                                                                                            className="col-12 d-flex align-items-center justify-content-center"
                                                                                                                                            style={{
                                                                                                                                                position:
                                                                                                                                                    "relative",

                                                                                                                                            }}
                                                                                                                                        >
                                                                                                                                            <img
                                                                                                                                                style={{
                                                                                                                                                    maxWidth:
                                                                                                                                                        "100%",
                                                                                                                                                    maxHeight:
                                                                                                                                                        "100%",
                                                                                                                                                }}
                                                                                                                                                src={
                                                                                                                                                    "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/current_control/" +
                                                                                                                                                    files
                                                                                                                                                }
                                                                                                                                                alt="test"
                                                                                                                                            />
                                                                                                                                            <i
                                                                                                                                                className="mdi mdi-delete taskdelete"
                                                                                                                                                style={{
                                                                                                                                                    position:
                                                                                                                                                        "absolute",
                                                                                                                                                    top: '-8px',
                                                                                                                                                    right: 22,
                                                                                                                                                    color: 'red'
                                                                                                                                                }}
                                                                                                                                                onClick={() =>
                                                                                                                                                    onDeleteFiles(
                                                                                                                                                        j,
                                                                                                                                                        i,
                                                                                                                                                        k,
                                                                                                                                                        f
                                                                                                                                                    )
                                                                                                                                                }
                                                                                                                                            ></i>
                                                                                                                                        </div>
                                                                                                                                    );
                                                                                                                                }
                                                                                                                            )}
                                                                                                                        </div>
                                                                                                                    )}

                                                                                                                    <label
                                                                                                                        className="d-flex justify-content-center mt-4"
                                                                                                                        htmlFor={"files" + k + i}
                                                                                                                        style={{ position: 'relative' }}
                                                                                                                    >
                                                                                                                        {opt.option.files.length === 0 ?
                                                                                                                            <i
                                                                                                                                style={{
                                                                                                                                    fontSize: 55,
                                                                                                                                    padding: 10,
                                                                                                                                    border: "1px dashed",
                                                                                                                                    borderRadius: 10,
                                                                                                                                }}
                                                                                                                                className="typcn typcn-cloud-storage-outline"
                                                                                                                            ></i>
                                                                                                                            :
                                                                                                                            <i
                                                                                                                                style={{
                                                                                                                                    fontSize: 55,
                                                                                                                                    padding: 10,
                                                                                                                                    border: "1px dashed",
                                                                                                                                    borderRadius: 10,
                                                                                                                                }}
                                                                                                                                className="ti-exchange-vertical"
                                                                                                                            ></i>
                                                                                                                        }
                                                                                                                        <span style={{ position: 'absolute', bottom: 0, right: 0 }}>*png,gif,jpeg</span>
                                                                                                                    </label>
                                                                                                                    <Form.Control
                                                                                                                        type="file"
                                                                                                                        id={"files" + k + i}
                                                                                                                        style={{
                                                                                                                            display: "none",
                                                                                                                        }}
                                                                                                                        accept="image/png, image/gif, image/jpeg"
                                                                                                                        onChange={(e) =>
                                                                                                                            onchangefiles(
                                                                                                                                e,
                                                                                                                                j,
                                                                                                                                i,
                                                                                                                                k
                                                                                                                            )
                                                                                                                        }
                                                                                                                    />
                                                                                                                </div>

                                                                                                                : ''}
                                                                                                        </div>
                                                                                                    );
                                                                                                }
                                                                                            })}
                                                                                        </div>
                                                                                        <div
                                                                                            className="form-group"
                                                                                            style={{ textAlign: "center" }}
                                                                                        >
                                                                                            <button
                                                                                                className="btn btn-primary"
                                                                                                onClick={(e) =>
                                                                                                    onClickButton(e, j, i)
                                                                                                }
                                                                                            >
                                                                                                <i className="mdi mdi-plus"></i>
                                                                                            </button>
                                                                                        </div>
                                                                                    </div>
                                                                                );
                                                                            } else if (item.type === "textarea") {
                                                                                return (
                                                                                    <div className="form-group">
                                                                                        <label htmlFor="user_name">
                                                                                            {item.label}
                                                                                        </label>
                                                                                        <textarea
                                                                                            onChange={(e) =>
                                                                                                onchangeText(e, j, i)
                                                                                            }
                                                                                            className="form-control"
                                                                                        >
                                                                                            {item.value}
                                                                                        </textarea>
                                                                                    </div>
                                                                                );
                                                                            } else if (item.type === "checkbox1") {
                                                                                return (
                                                                                    <>

                                                                                    </>
                                                                                );
                                                                            } else if (item.type === "hazards") {
                                                                                return (
                                                                                    <div className="row mb-5" style={{
                                                                                        boxShadow:
                                                                                            "0px 0px 10px 4px #0000001f",
                                                                                    }}>
                                                                                        <div
                                                                                            className="form-group mt-5 mb-3"
                                                                                            style={{ textAlign: "center" }}
                                                                                        >
                                                                                            <label
                                                                                                htmlFor="user_name"
                                                                                                style={{ fontSize: 20 }}
                                                                                            >
                                                                                                Hazards
                                                                                            </label>
                                                                                        </div>
                                                                                        <div className="form-group">
                                                                                            <div className="row">
                                                                                                <div
                                                                                                    className="col-12"
                                                                                                    style={{

                                                                                                        overflowY: "auto",
                                                                                                    }}
                                                                                                >
                                                                                                    {/* <label htmlFor="user_name">
                                                      Select potentials hazards
                                                      applicable for this
                                                      sub-activity
                                                    </label> */}
                                                                                                    {item.option[0].allhazards
                                                                                                        .length >= 0 ? (
                                                                                                        <>
                                                                                                            {item.option[0].allhazards.map(
                                                                                                                (cate, c) => {
                                                                                                                    return (
                                                                                                                        <>
                                                                                                                            {/* <Accordion.Header
                                                                  className="accord"
                                                                  style={{
                                                                    padding: 10,
                                                                  }}
                                                                >
                                                                  {cate.name}
                                                                </Accordion.Header> */}
                                                                                                                            {/* <Accordion.Body
                                                                  style={{
                                                                    paddingTop: 10,
                                                                  }}
                                                                > */}
                                                                                                                            {cate.hazards ? (
                                                                                                                                <div className="row">
                                                                                                                                    {cate.hazards.map(
                                                                                                                                        (ha, h) => {
                                                                                                                                            return (
                                                                                                                                                <div
                                                                                                                                                    className="col-4"
                                                                                                                                                    onClick={(
                                                                                                                                                        e
                                                                                                                                                    ) =>
                                                                                                                                                        onClickHazard(
                                                                                                                                                            e,
                                                                                                                                                            i,
                                                                                                                                                            j,
                                                                                                                                                            c,
                                                                                                                                                            h,
                                                                                                                                                            cate,
                                                                                                                                                            ha
                                                                                                                                                        )
                                                                                                                                                    }
                                                                                                                                                >
                                                                                                                                                    <div
                                                                                                                                                        className="row m-2 align-items-center justify-content-center"
                                                                                                                                                        style={
                                                                                                                                                            ha.active ===
                                                                                                                                                                true
                                                                                                                                                                ? {
                                                                                                                                                                    boxShadow:
                                                                                                                                                                        "0px 0px 12px 4px #d6d4d4",
                                                                                                                                                                    border:
                                                                                                                                                                        "2px solid red",
                                                                                                                                                                    cursor:
                                                                                                                                                                        "pointer",
                                                                                                                                                                }
                                                                                                                                                                : {
                                                                                                                                                                    boxShadow:
                                                                                                                                                                        "0px 0px 12px 4px #d6d4d4",
                                                                                                                                                                    border:
                                                                                                                                                                        "2px solid #fff",
                                                                                                                                                                    cursor:
                                                                                                                                                                        "pointer",
                                                                                                                                                                }
                                                                                                                                                        }
                                                                                                                                                    >
                                                                                                                                                        <div className="col-4 p-0 text-center">
                                                                                                                                                            <img
                                                                                                                                                                src={
                                                                                                                                                                    "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                                                                                                    ha.image
                                                                                                                                                                }
                                                                                                                                                                style={{
                                                                                                                                                                    height: 60,
                                                                                                                                                                }}
                                                                                                                                                                alt="sample"
                                                                                                                                                            />
                                                                                                                                                        </div>
                                                                                                                                                        <div className="col-8">
                                                                                                                                                            <p>
                                                                                                                                                                {" "}
                                                                                                                                                                {
                                                                                                                                                                    ha.name
                                                                                                                                                                }{" "}
                                                                                                                                                            </p>
                                                                                                                                                        </div>
                                                                                                                                                    </div>
                                                                                                                                                </div>
                                                                                                                                            );
                                                                                                                                        }
                                                                                                                                    )}
                                                                                                                                </div>
                                                                                                                            ) : (
                                                                                                                                ""
                                                                                                                            )}
                                                                                                                            {/* </Accordion.Body> */}
                                                                                                                        </>
                                                                                                                    );
                                                                                                                }
                                                                                                            )}
                                                                                                        </>
                                                                                                    ) : (
                                                                                                        ""
                                                                                                    )}
                                                                                                </div>
                                                                                                {/* <div
                                                    className="col-6"
                                                    style={{
                                                      background: "#f5f5f5",
                                                      padding: 15,
                                                      height: 500,
                                                      overflowY: "auto",
                                                    }}
                                                  >
                                                    <label htmlFor="user_name">
                                                      Identified Hazards
                                                    </label>
                                                    {item.option[0].allhazards
                                                      .length >= 0 ? (
                                                      <div className="row">
                                                        {item.option[0].allhazards.map(
                                                          (cate, c) => {
                                                            return (
                                                              <>
                                                                {cate.hazards ? (
                                                                  <>
                                                                    {cate.hazards.map(
                                                                      (ha, h) => {
                                                                        return (
                                                                          <>
                                                                            {ha.active ===
                                                                              true ? (
                                                                              <div
                                                                                className="col-6"
                                                                                style={{
                                                                                  marginBottom: 16,
                                                                                }}
                                                                              >
                                                                                <div
                                                                                  className="d-flex"
                                                                                  style={{
                                                                                    boxShadow:
                                                                                      "0px 0px 12px 4px #d6d4d4",
                                                                                  }}
                                                                                >
                                                                                  <div
                                                                                    className=""
                                                                                    style={{
                                                                                      background:
                                                                                        "#fff",
                                                                                    }}
                                                                                  >
                                                                                    <img
                                                                                      src={
                                                                                        "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                                                        ha.image
                                                                                      }
                                                                                      style={{
                                                                                        height: 60,
                                                                                        width: 60,
                                                                                      }}
                                                                                      alt="sample"
                                                                                    />
                                                                                  </div>
                                                                                  <div
                                                                                    className="d-flex align-items-center"
                                                                                    style={{
                                                                                      position:
                                                                                        "relative",
                                                                                      width:
                                                                                        "100%",
                                                                                      paddingLeft: 10,
                                                                                      background:
                                                                                        "#fff",
                                                                                    }}
                                                                                  >
                                                                                    <p>
                                                                                      {" "}
                                                                                      {
                                                                                        ha.name
                                                                                      }{" "}
                                                                                    </p>
                                                                                    <span
                                                                                      style={{
                                                                                        position:
                                                                                          "absolute",
                                                                                        right: 0,
                                                                                        top: "-7px",
                                                                                        fontStyle:
                                                                                          "italic",
                                                                                        fontSize: 12,
                                                                                        color:
                                                                                          "red",
                                                                                        cursor:
                                                                                          "pointer",
                                                                                      }}
                                                                                    >
                                                                                      <i
                                                                                        className="mdi mdi-close"
                                                                                        onClick={(
                                                                                          e
                                                                                        ) =>
                                                                                          onClickHazard(
                                                                                            e,
                                                                                            i,
                                                                                            j,
                                                                                            c,
                                                                                            h,
                                                                                            cate,
                                                                                            ha
                                                                                          )
                                                                                        }
                                                                                      ></i>
                                                                                    </span>
                                                                                  </div>
                                                                                </div>
                                                                              </div>
                                                                            ) : (
                                                                              ""
                                                                            )}
                                                                          </>
                                                                        );
                                                                      }
                                                                    )}{" "}
                                                                  </>
                                                                ) : (
                                                                  ""
                                                                )}
                                                              </>
                                                            );
                                                          }
                                                        )}
                                                      </div>
                                                    ) : (
                                                      ""
                                                    )}
                                                  </div> */}

                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                );
                                                                            } else if (item.type === "initial") {
                                                                                return (
                                                                                    <div className="row">

                                                                                    </div>
                                                                                );
                                                                            }
                                                                        })}
                                                                    </div>
                                                                </>
                                                            </>
                                                        )
                                                    })}
                                                </div>
                                            </>
                                            {/* <div className='col-12 text-center' style={{ padding: 20 }}>
                                                <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); addTask() }}> Add Sub Activity</button></div> */}
                                        </div>
                                    </div>
                                    {/* <div className="col-12" style={{
                                        padding: 20,
                                        boxShadow:
                                            "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                                    }}>
                                        <h5 style={{ textAlign: 'center' }} className="mt-2 mb-3 form-group required">Overall recommendation of the RA Team</h5>

                                        <div className="mb-4 d-flex">
                      <div className="col-1 p-0">
                        <span className="span-circle number"> 1</span>
                      </div>
                      <div className="col-11 ">
                        <Select
                          labelKey="label"
                          id="user_description"
                          onChange={handleRecommendationChange}
                          options={[{ label: 'The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.', value: '0' },
                          { label: 'The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.', value: '1' },
                          { label: 'The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.', value: '2' },]}
                          defaultValue={meet.recommendation}
                          placeholder="Choose ..."
                        />
                      </div>
                    </div>
                    <div className="mb-4 d-flex">
                      <div className="col-1 p-0">
                        <span className="span-circle number"> 2</span>
                      </div>
                      <div className="col-11 ">
                        <Select
                          labelKey="label"
                          id="user_description"
                          onChange={handleAdditionalRecommendation}
                          options={[{ label: 'Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.', value: '0' },
                          { label: 'A formal permit to work is required before work can commence. Duration will be specified by the permit approver.', value: '1' },
                          { label: 'A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.', value: '2' },]}
                          defaultValue={meet.additionRecommendation}
                          placeholder="Choose ..."
                        />
                      </div>
                    </div>



                                    </div> */}

                                    <div className="col-12 mt-4 mb-4" style={{
                                        padding: 20,
                                        boxShadow:
                                            "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                                    }}>
                                        <h5 style={{ textAlign: 'center' }} className="mt-2 mb-3">Team Leader Declaration</h5>
                                        <p style={{ textAlign: 'center' }}>I affirm my position as the Team Leader for this Risk Assessment. The eventual outcome signifies our collective professional judgment, reached through consensus and utilizing our team's fullest capabilities.</p>


                                        <div className="row mt-4">
                                            <div className="col-12 text-center">
                                                {meet.sign ?
                                                    <img style={{ maxWidth: '100%' }} src={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_sign/" + meet.sign} alt='' />
                                                    : <><SignatureCanvas
                                                        penColor="#1F3BB3"
                                                        canvasProps={{
                                                            width: 350,
                                                            height: 70,
                                                            className: "sigCanvas",
                                                            style: {
                                                                boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                                            },
                                                        }}
                                                        ref={sign}


                                                    />  <i className="fa fa-undo undo" onClick={() => sign.current.clear()}></i>
                                                    </>}
                                                <p> {meet.captain}</p>
                                            </div>

                                        </div>
                                    </div>

                                    {meet.teamMemberInvolved.length !== 0 ?

                                        <TeamDeclaration data={meet.teamMemberInvolved} />

                                        : ''}
                                    <div className='col-12 text-center' style={{ padding: 20 }}>
                                        <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); createUserHandler() }}> Update Risk Assessment</button></div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                : ''}

            <div className='row'>
                <div className='col-12'>
                    <div className="card">
                        <div className="card-body">
                            {/* <h4 className="card-title" style={{ textAlign: 'center', fontSize: 20 }}>Reviews,Changes and Updates</h4> */}
                            {/* <SagoTable searchText={"Search Update"} columns={columns} data={update} /> */}
                            <ThemeProvider theme={defaultMaterialTheme}>
                                <MaterialTable
                                    columns={columns}
                                    data={update}
                                    title="Reviews,Changes and Updates"
                                    style={tableStyle}
                                    options={{
                                        actionsColumnIndex: -1
                                    }}
                                    actions={[{
                                        icon: 'visibility',
                                        tooltip: 'View Risk',
                                        onClick: (event, rowData) => {

                                            setAttachmentShow(true)
                                            setViewAttachment(rowData.attachment)
                                            // Do save operation
                                            // console.log(rowData)
                                            // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
                                        }
                                    },]}


                                />
                            </ThemeProvider>
                        </div>
                    </div>
                </div>
            </div>

            <Modal
                show={attachmentShow}
                size={'md'}
                onHide={() => setAttachmentShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header closeButton>
                    Attachment
                </Modal.Header>
                <Modal.Body>
                    <div
                        className="row mt-3"
                        style={{
                            padding: 10,

                            borderRadius: 10

                        }}
                    >
                        {viewAttachment.length != 0 &&
                            viewAttachment.map((files, f) => {
                                return (
                                    <div
                                        className="col-3 d-flex align-items-center justify-content-center"
                                        style={{
                                            position:
                                                "relative",

                                        }}
                                    >
                                        {files.type.match('image') ? <>
                                            <img
                                                style={{

                                                    maxHeight:
                                                        "100%",
                                                    maxWidth: "100%"
                                                }}
                                                src={
                                                    "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" +
                                                    files.name}
                                                alt="test"
                                            />

                                        </> : files.type === 'application/pdf' ? <>
                                            <a target='_blank' href={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" + files.name}>
                                                <i class="fa fa-file-pdf-o fa-3x" aria-hidden="true"></i>
                                            </a>

                                        </> : <>
                                            <a target='_blank' href={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" + files.name}>
                                                <i class="fa fa-file-excel-o fa-3x" aria-hidden="true"></i>
                                            </a>

                                        </>}
                                    </div>
                                );
                            }
                            )}
                    </div>
                </Modal.Body>
            </Modal>
            <Modal
                show={mdShow}
                size={'lg'}
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms row" >
                        <div className="form-group col-6">
                            <label htmlFor="user_name" >Reason For Review</label>
                            <textarea className='form-control' ref={review} ></textarea>
                        </div>
                        <div className="form-group col-6">
                            <label htmlFor="user_name" >Changes</label>
                            <textarea className='form-control' ref={change} ></textarea>
                        </div>
                        <div className="form-group col-6">
                            <label htmlFor="user_category" >Reason for Change</label>
                            <textarea className='form-control' ref={reasonchange} ></textarea>
                        </div>
                        <div className="form-group col-6">
                            <label htmlFor="user_category" >Initiated By</label>
                            <textarea className='form-control' ref={initial} ></textarea>
                        </div>
                        <div className="form-group col-6">
                            <label htmlFor="user_category" >Approved By</label>
                            <textarea className='form-control' ref={approve} ></textarea>
                        </div>
                        <div className="form-group col-6">
                            <label htmlFor="user_category" >Reference</label>
                            <textarea className='form-control' ref={reference} ></textarea>
                        </div>
                        <div className="form-group col-12">
                            <label>Attachment If any</label>
                            <div className='form-group col-12'>

                                <div
                                    className="row mt-3"
                                    style={{
                                        padding: 10,
                                        border: '1px dashed',
                                        borderRadius: 10

                                    }}
                                >
                                    {amend.map((files, f) => {
                                        return (
                                            <div
                                                className="col-3 d-flex align-items-center justify-content-center"
                                                style={{
                                                    position:
                                                        "relative",

                                                }}
                                            >
                                                {files.type.match('image') ? <>
                                                    <img
                                                        style={{

                                                            maxHeight:
                                                                "100%",
                                                            maxWidth: "100%"
                                                        }}
                                                        src={
                                                            "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" +
                                                            files.name}
                                                        alt="test"
                                                    />
                                                    <i
                                                        className="mdi mdi-delete"
                                                        style={{
                                                            position: 'absolute',
                                                            top: '-8px',
                                                            right: 22,
                                                            color: 'red'
                                                        }}
                                                        onClick={() =>
                                                            onDeleteAmendFiles(
                                                                f
                                                            )
                                                        }
                                                    ></i>
                                                </> : files.type === 'application/pdf' ? <>
                                                    <a target='_blank' href={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" + files.name}>
                                                        <i class="fa fa-file-pdf-o fa-3x" aria-hidden="true"></i>
                                                    </a>
                                                    <i
                                                        className="mdi mdi-delete"
                                                        style={{
                                                            position: 'absolute',
                                                            top: '-8px',
                                                            right: 22,
                                                            color: 'red'
                                                        }}
                                                        onClick={() =>
                                                            onDeleteAmendFiles(
                                                                f
                                                            )
                                                        }
                                                    ></i>
                                                </> : <>
                                                    <a target='_blank' href={"https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/risk_amends/" + files.name}>
                                                        <i class="fa fa-file-excel-o fa-3x" aria-hidden="true"></i>
                                                    </a>
                                                    <i
                                                        className="mdi mdi-delete"
                                                        style={{
                                                            position: 'absolute',
                                                            top: '-8px',
                                                            right: 22,
                                                            color: 'red'
                                                        }}
                                                        onClick={() =>
                                                            onDeleteAmendFiles(
                                                                f
                                                            )
                                                        }
                                                    ></i>
                                                </>}
                                            </div>
                                        );
                                    }
                                    )}
                                </div>


                            </div>
                            <label
                                className="d-flex justify-content-center mt-4"
                                htmlFor={"files"}
                            >
                                <i
                                    style={{
                                        fontSize: 55,
                                        padding: 10,
                                        border: "1px dashed",
                                        borderRadius: 10,
                                    }}
                                    className="mdi mdi-cloud-upload"
                                ></i>
                            </label>
                            <Form.Control id='files' type='file' style={{ display: 'none' }} onChange={(e) => amendUploads(e)} />
                        </div>
                        <div className='' style={{ textAlign: 'center' }}>
                            <p>I confim that the risk assessment has been reviewed  as above and changes (if any) have been made as shown here. This change has also been authroized by the relevant authority</p>
                        </div>
                        <div className='col-12' style={{ textAlign: 'center' }}>
                            <SignatureCanvas penColor='#1F3BB3'
                                canvasProps={{ width: 500, height: 200, className: 'sigCanvas', style: { boxShadow: '0px 0px 10px 3px rgb(189 189 189)' } }} ref={sign1} style={{}} />
                            <div className='col-12'>
                                <button className='btn btn-primary' onClick={(e) => { e.preventDefault(); sign1.current.clear() }} >Clear</button>
                            </div>

                        </div>



                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? '' : (
                            <>
                                <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={updateHandler}>Update</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>

        </>
    )
}
export const Column = (props) => {

    return (
        <div className="border p-4 col-sm-6" style={{ background: '#f5f5f5' }}>
            <div><h6 className="card-title">{props.column.tittle}</h6></div>
            <Droppable droppableId={props.column.id}>
                {provided => (
                    <div className="kanbanHeight"
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                    >
                        {props.tasks.map((task, index) =>
                            <Task key={task.id} task={task} index={index} />)}
                        {provided.placeholder}
                    </div>
                )}
            </Droppable >
        </div>
    )

}
export const Task = (props) => {

    return (<Draggable draggableId={props.task.id} index={props.index}>
        {(provided) => (
            <div className="mt-1 board-portlet"
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                ref={provided.innerRef}
            >
                <div className="card-body p-3 bg-white">
                    <div className="media">
                        <div className="media-body">
                            <div className="d-flex">
                                <img src={props.task.imgURL} alt="profile" className="img-sm me-3" />
                                <div>
                                    <h6 className="mb-1">{props.task.name}</h6>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )}
    </Draggable>
    )

}
export default HazardDraft;
