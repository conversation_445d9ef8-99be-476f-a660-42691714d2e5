import React from 'react'
import { Form } from 'react-bootstrap';
import Select from "react-select";
function Consequence({ item, i, j, onchangeText12, onDeleteTextBox1, onDeleteFiles1, onchangeConsequencefiles1, onClickButton1,changeTypeControl1 }) {
  const onchangeText1 = (e, j, i, k) => {
    onchangeText12(e, j, i, k)
  }
  const changeTypeControl = (e, j, i, k) => {
    changeTypeControl1(e, j, i, k)
}

  const onDeleteTextBox = (j, i, k) => {
    onDeleteTextBox1(j, i, k)
  }
  const onDeleteFiles = (j, i, k, f) => {
    onDeleteFiles1(j, i, k, f)
  }
  const onchangeConsequencefiles = (e, j, i, k) => {
    onchangeConsequencefiles1(e, j, i, k)
  }
  const onClickButton = (e, j, i) => {
    onClickButton1(e, j, i)
  }
  return (
    <div
      className="row mb-5"
      style={{
        padding: 20,
        boxShadow:
          "0px 0px 10px 4px #0000001f",
      }}
    >
      <div
        className="form-group mt-5 mb-3"
        style={{ textAlign: "center" }}
      >
        <label
          htmlFor="user_name"
          style={{ fontSize: 20 }}
        >
          Consequence
        </label>
      </div>
      <div
        className="form-group mt-3"
        style={{ textAlign: "center" }}
      >
        <label
          htmlFor="user_name"
          style={{ fontSize: 15 }}
        >
          Identify the potential consequence on Safety, Environment, Financial, Security and Community / Brand Exposure due to presence of above hazards.
        </label>
      </div>

      <div className="textbox1">
        {item.option.map((opt, k) => {
          return (
            <div className="row mb-4 d-flex align-items-center">
              <div className="col-1 p-0">
                <span className="span-circle number"> {k + 1}</span>
              </div>
              <div className="col-3">
                <Select
                  labelKey="label"
                  id="user_description"
                  onChange={(e) => changeTypeControl(e, j, i, k)}
                  options={[
                    { 'label': 'Personnel', 'value': 'Personnel' },
                  { 'label': 'Property', 'value': 'Property' },
                  { 'label': 'Environment', 'value': 'Environment' },
                  { 'label': 'Service Loss', 'value': 'Service Loss' },
                 ]}
                  placeholder="Impact On"

                />
              </div>
              <div className="col-7 p-0">
                <Form.Control
                  type="text"
                  onChange={(e) =>
                    onchangeText1(e, j, i, k)
                  }
                  value={opt.value}
                  className="m-0"
                  placeholder='Description'
                />
              </div>
              <div className="col-1 p-0">
                <i
                  className="mdi mdi-delete span-circle delete"
                  onClick={() =>
                    onDeleteTextBox(j, i, k)
                  }
                ></i>
              </div>
              {opt.option.type === 'file' ?
                <div className="form-group">
                  {opt.option.files.length !== 0 && (
                    <div
                      className="row mt-3"
                      style={{
                        padding: 10,
                        border: '1px dashed',
                        borderRadius: 10

                      }}
                    >
                      {opt.option.files.map(
                        (files, f) => {
                          return (
                            <div
                              className="col-12 d-flex align-items-center justify-content-center"
                              style={{
                                position:
                                  "relative",

                              }}
                            >
                              <img
                                style={{

                                  maxHeight:
                                    "100%",
                                  maxWidth: "100%"
                                }}
                                src={
                                  "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/consequence/" +
                                  files
                                }
                                alt="test"
                              />
                              <i
                                className="mdi mdi-delete taskdelete"
                                style={{
                                  position: 'absolute',
                                  top: '-8px',
                                  right: 22,
                                  color: 'red'
                                }}
                                onClick={() =>
                                  onDeleteFiles(
                                    j,
                                    i,
                                    k,
                                    f
                                  )
                                }
                              ></i>
                            </div>
                          );
                        }
                      )}
                    </div>
                  )}

                  <label
                    className="d-flex justify-content-center mt-4"
                    htmlFor={"filescons" + k + i}
                    style={{ position: 'relative' }}
                  >
                    {opt.option.files.length === 0 ?
                      <i
                        style={{
                          fontSize: 55,
                          padding: 10,
                          border: "1px dashed",
                          borderRadius: 10,
                        }}
                        className="typcn typcn-cloud-storage-outline"
                      ></i>
                      :
                      <i
                        style={{
                          fontSize: 55,
                          padding: 10,
                          border: "1px dashed",
                          borderRadius: 10,
                        }}
                        className="ti-exchange-vertical"
                      ></i>
                    }
                    <span style={{ position: 'absolute', bottom: 0, right: 0 }}>*png,gif,jpeg</span>
                  </label>
                  <Form.Control
                    type="file"
                    id={"filescons" + k + i}
                    style={{
                      display: "none",
                    }}
                    accept="image/png, image/gif, image/jpeg"
                    onChange={(e) =>
                      onchangeConsequencefiles(
                        e,
                        j,
                        i,
                        k

                      )
                    }
                  />
                </div>

                : ''}
            </div>
          );
        })}
      </div>
      <div
        className="form-group"
        style={{ textAlign: "center" }}
      >
        <button
          className="btn btn-primary"
          onClick={(e) =>
            onClickButton(e, j, i)
          }
        >
          <i className="mdi mdi-plus"></i>
        </button>
      </div>
    </div>
  )
}

export default Consequence