import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Checkbox } from 'primereact/checkbox';
import { FilterMatchMode } from 'primereact/api';
import { MultiSelect } from 'primereact/multiselect';
import { Dropdown } from 'primereact/dropdown';

import 'primereact/resources/primereact.css';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primeicons/primeicons.css';

const Summary = ({ obsdata }) => {
    // 1. Transform your data to include top-level fields for reportingDpt & responsibleDpt
    const transformedData = obsdata.map((row) => {
        const remarks = JSON.parse(row.remarks || '{}');
        return {
            ...row,
            reportingDpt: row.workActivityDepartment?.name || '',
            responsibleDpt: remarks.obsSourceName || '',
            isUnsafeAct: row.type === 'Unsafe Act',
            isUnsafeCondition: row.type === 'Unsafe Condition',
            isSeverityHigh: row.severity === 'High',
            isSeverityMedium: row.severity === 'Medium',
            isSeverityLow: row.severity === 'Low',
            isStatusOpen: row.status === 'Actions Assigned',
            isStatusClose: ['Action Verified - Closed', 'Archived without actions', 'Reported & Closed', 'Reported & Rectified on Spot'].includes(row.status),
            isStatusOngoing: ['Actions Taken - Pending Verification', 'Under Review', 'Action Reassigned'].includes(row.status)
        };
    });

    // 2. Extract unique department names for filtering dropdown
    const reportingDeptOptions = [...new Set(transformedData.map((item) => item.reportingDpt))].map(name => ({ label: name, value: name }));
    const responsibleDeptOptions = [...new Set(transformedData.map((item) => item.responsibleDpt))].map(name => ({ label: name, value: name }));

    // 2. Define filters state
    const [filters, setFilters] = useState({
        reportingDpt: { value: null, matchMode: FilterMatchMode.IN },
        responsibleDpt: { value: null, matchMode: FilterMatchMode.IN },
        isUnsafeAct: { value: null, matchMode: FilterMatchMode.EQUALS },
        isUnsafeCondition: { value: null, matchMode: FilterMatchMode.EQUALS },
        isSeverityHigh: { value: null, matchMode: FilterMatchMode.EQUALS },
        isSeverityMedium: { value: null, matchMode: FilterMatchMode.EQUALS },
        isSeverityLow: { value: null, matchMode: FilterMatchMode.EQUALS },
        isStatusOpen: { value: null, matchMode: FilterMatchMode.EQUALS },
        isStatusClose: { value: null, matchMode: FilterMatchMode.EQUALS },
        isStatusOngoing: { value: null, matchMode: FilterMatchMode.EQUALS }
    });

    const booleanFilterOptions = [
        { label: 'True', value: true },
        { label: 'False', value: false }
    ];


    const renderMultiSelectFilter = (options, field) => (
        <MultiSelect
            value={filters[field]?.value || []}
            options={options}
            onChange={(e) => setFilters({ ...filters, [field]: { value: e.value, matchMode: FilterMatchMode.IN } })}
            placeholder="Select Options"
            className="p-column-filter"
            display="chip"
        />
    );

    // 3. Pre-calculate counts for each relevant column
    const totalRows = transformedData.length;
    const unsafeActCount = transformedData.filter((d) => d.type === 'Unsafe Act').length;
    const unsafeConditionCount = transformedData.filter((d) => d.type === 'Unsafe Condition').length;
    const severityHighCount = transformedData.filter((d) => d.severity === 'High').length;
    const severityMediumCount = transformedData.filter((d) => d.severity === 'Medium').length;
    const severityLowCount = transformedData.filter((d) => d.severity === 'Low').length;
    const statusOpenCount = transformedData.filter((d) => d.status === 'Actions Assigned').length;
    const statusCloseCount = transformedData.filter((d) =>
        ['Action Verified - Closed', 'Archived without actions', 'Reported & Closed', 'Reported & Rectified on Spot'].includes(d.status)
    ).length;
    const statusOngoingCount = transformedData.filter((d) =>
        ['Actions Taken - Pending Verification', 'Under Review', 'Action Reassigned'].includes(d.status)
    ).length;





    // 5. Dropdown filter for True/False values
    const renderBooleanDropdownFilter = (field) => (
        <Dropdown
            value={filters[field]?.value}
            options={booleanFilterOptions}
            onChange={(e) => setFilters({ ...filters, [field]: { value: e.value, matchMode: FilterMatchMode.EQUALS } })}
            placeholder="Select"
            className="p-column-filter"
            showClear
        />
    );

    // 4. Helper to center the checkbox
    const centeredCheckbox = (checked) => (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Checkbox checked={checked} disabled />
        </div>
    );

    // 5. Body templates for the checkboxes
    const renderCheckmark = (rowData, field) => centeredCheckbox(rowData.type === field);
    const renderHighSeverity = (rowData) => centeredCheckbox(rowData.severity === 'High');
    const renderMediumSeverity = (rowData) => centeredCheckbox(rowData.severity === 'Medium');
    const renderLowSeverity = (rowData) => centeredCheckbox(rowData.severity === 'Low');

    // For status, we pass a list of valid statuses vs. a single status
    const renderStatus = (rowData, statuses) => {
        const isChecked = Array.isArray(statuses)
            ? statuses.includes(rowData.status)
            : rowData.status === statuses;
        return centeredCheckbox(isChecked);
    };

    return (
        <DataTable
            value={transformedData}
            paginator
            rows={10}
            filterDisplay="menu"
            filters={filters}
            onFilter={(e) => setFilters(e.filters)}  // **Handle filtering**
            rowsPerPageOptions={[10, 25, 50]}
            emptyMessage="No Data found."
        >
            <Column
                field="reportingDpt"
                header="Reporting Dpt."
                filter
                showFilterMenu={true}
                showFilterMatchModes={false}
                filterElement={renderMultiSelectFilter(reportingDeptOptions, "reportingDpt")}
            />
            <Column
                field="responsibleDpt"
                header="Responsible Dpt."
                filter
                showFilterMenu={true}
                showFilterMatchModes={false}
                filterElement={renderMultiSelectFilter(responsibleDeptOptions, "responsibleDpt")}
            />
            <Column
                field="isUnsafeAct"
                header={`Unsafe Act (${unsafeActCount})`}
                body={(rowData) => centeredCheckbox(rowData.isUnsafeAct)}
                filter
                showFilterMenu={true}
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isUnsafeAct")}
            />
            <Column
                field="isUnsafeCondition"
                header={`Unsafe Condition (${unsafeConditionCount})`}
                body={(rowData) => centeredCheckbox(rowData.isUnsafeCondition)}
                filter
                showFilterMenu={true}
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isUnsafeCondition")}
            />
            {/* <Column
                field="unsafeAct"
                header={`Unsafe Act (${unsafeActCount})`}
                body={(rowData) => renderCheckmark(rowData, 'Unsafe Act')}
            />
            <Column
                field="unsafeCondition"
                header={`Unsafe Condition (${unsafeConditionCount})`}
                body={(rowData) => renderCheckmark(rowData, 'Unsafe Condition')}
            /> */}
            {/* <Column
                field="severityHigh"
                header={`Severity High (${severityHighCount})`}
                body={renderHighSeverity}
            />
            <Column
                field="severityMedium"
                header={`Severity Medium (${severityMediumCount})`}
                body={renderMediumSeverity}
            />
            <Column
                field="severityLow"
                header={`Severity Low (${severityLowCount})`}
                body={renderLowSeverity}
            /> */}

            <Column
                field="isSeverityHigh"
                header={`Severity High (${severityHighCount})`}
                body={(rowData) => centeredCheckbox(rowData.isSeverityHigh)}
                filter
                showFilterMenu={true}
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isSeverityHigh")}
            />
            <Column
                field="isSeverityMedium"
                header={`Severity Medium (${severityMediumCount})`}
                body={(rowData) => centeredCheckbox(rowData.isSeverityMedium)}
                filter
                showFilterMenu={true}
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isSeverityMedium")}
            />
            <Column
                field="isSeverityLow"
                header={`Severity Low (${severityLowCount})`}
                body={(rowData) => centeredCheckbox(rowData.isSeverityLow)}
                filter
                showFilterMenu={true}
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isSeverityLow")}
            />
            {/* <Column
                field="statusOpen"
                header={`Status Open (${statusOpenCount})`}
                body={(rowData) => renderStatus(rowData, 'Actions Assigned')}
            />
            <Column
                field="statusClose"
                header={`Status Close (${statusCloseCount})`}
                body={(rowData) =>
                    renderStatus(rowData, [
                        'Action Verified - Closed',
                        'Archived without actions',
                        'Reported & Closed',
                        'Reported & Rectified on Spot'
                    ])
                }
            />
            <Column
                field="statusOngoing"
                header={`Status Ongoing (${statusOngoingCount})`}
                body={(rowData) =>
                    renderStatus(rowData, [
                        'Actions Taken - Pending Verification',
                        'Under Review',
                        'Action Reassigned'
                    ])
                }
            /> */}
            <Column
                field="isStatusOpen"
                header="Status Open"
                body={(rowData) => centeredCheckbox(rowData.isStatusOpen)}
                filter
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isStatusOpen")}
                showFilterMenu
            />


            <Column
                field="isStatusClose"
                header="Status Close"
                body={(rowData) => centeredCheckbox(rowData.isStatusClose)}
                filter
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isStatusClose")}
                showFilterMenu
            />
            <Column
                field="isStatusOngoing"
                header="Status Ongoing"
                body={(rowData) => centeredCheckbox(rowData.isStatusOngoing)}
                filter
                showFilterMatchModes={false}
                filterElement={renderBooleanDropdownFilter("isStatusOngoing")}
                showFilterMenu
            />
        </DataTable>
    );
};

export default Summary;
