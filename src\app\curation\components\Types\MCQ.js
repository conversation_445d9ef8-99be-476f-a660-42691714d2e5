import React, { Component } from "react";
import * as _ from "lodash";
import JoditEditor from "jodit-react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
class MCQ extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tab: "",
      multiple: false,
      toolType: "MCQ",
      title: "",
      defaultValue: "",
      validation: {
        isRequired: false,
      },
      radios: [],
      slider: 0,
      duplicate: false,
      mdShow: false,
      isEmpty: false
    };
    this.config = {
      askBeforePasteHTML: false,
      askBeforePasteFromWord: false,
      defaultActionOnPaste: "insert_clear_html",
      toolbarAdaptive: false,
      toolbarButtonSize: "large",
      toolbarSticky: false,
      buttons:"|,bold,underline,italic,|,font,fontsize,|,superscript,subscript,|,ul,ol,|,outdent,indent,|,align,paste,image,|",
      enableDragAndDropFileToEditor: true,
      uploader: {
        url: 'https://app.acuizen.com/Webservice/uploadEditorImage',
        format: 'json',
        pathVariableName: 'path',
        // filesVariableName: function (e) {
        //   return "images";
        // },
       // filesVariableName: 'images',
        prepareData: function (data) {
            console.log(data);
            return data;
        },
        isSuccess: function (resp) {
            return !resp.error;
        },
        getMsg: function (resp) {
            return resp.msg.join !== undefined ? resp.msg.join(' ') : resp.msg;
        },
        process: function (resp) {
            console.log(resp);
            return {
                files: resp['images'] || [],
                path: resp.path,
                baseurl: resp.baseurl,
                error: resp.error,
                msg: resp.msg
            };
        },
        error: function (e) {
            console.log(e)
            this.events.fire('errorPopap', [e.getMessage(), 'error', 4000]);
        },
        defaultHandlerSuccess: function (data, resp) {
            console.log(data);
            console.log(resp);
            var i, field = 'files';
            if (data[field] && data[field].length) {
                console.log(data[field]);
                for (i = 0; i < data[field].length; i += 1) {
                    this.selection.insertImage(data.baseurl + data[field][i]);
                }
            }
        },
        defaultHandlerError: function (resp) {
            this.events.fire('errorPopap', [this.options.uploader.getMsg(resp)]);
        }
  },
    };
    this.removeOption = this.removeOption.bind(this);
  }

  componentWillMount() {
    this.setState(this.props.field);
  }

  changeValue(stateFor, value) {
    switch (stateFor) {
      case "NAME":
        this.setState({ name: value });
        break;
      case "TITLE":
        this.setState({ title: value });
        break;
      case "DESCRIPTION":
        this.setState({ description: value });
        break;
      case "DEFAULT_VALUE":
        this.setState({ defaultValue: value });
        break;
      case "IS_REQUIRED":
        this.setState({
          validation: { ...this.state.validation, isRequired: value },
        });
        break;
      case "IS_READONLY":
        this.setState({
          validation: { ...this.state.validation, isReadOnly: value },
        });
        break;
      case "MAX":
        this.setState({ validation: { ...this.state.validation, max: value } });
        break;
      case "MIN":
        this.setState({ validation: { ...this.state.validation, min: value } });
        break;
      case "INLINE":
        this.setState({ inline: value });
        break;
      case "MULTIPLE":
        let radios = this.state.radios;
        radios.forEach((ele) => {
          return (ele.selected = false);
        });
        this.setState({
          radios: radios,
          multiple: value,
        });
        break;
      default:
        return;
    }
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  removeOption(index) {
    let radios = this.state.radios;
    radios.splice(index, 1);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  render() {
    console.log(this.props)
    return (
      <div
        className="card mb-3"
        style={this.state.title === '' && this.props.field.isEmpty ? { boxShadow: '0px 0px 12px 3px #dfdfdf', border: '1px solid red', borderRadius: 0 } : { boxShadow: '0px 0px 12px 3px #dfdfdf', borderRadius: 0 }}
      >
        <div className="card-header d-flex justify-content-between" >
          <div> <i className="fa fa-bar-chart  mr-1"></i> IMCQ</div>

          <div className="">
            {this.props.index !== 0 ?
              <span
                className="" style={{ paddingRight: 5 }}
                onClick={() => this.props.moveUp(this.props.index)}
              >
                <i className="mdi mdi-arrow-up"></i>
              </span>
              : ""}
            {this.props.index !== this.props.length ?
              <span
                className="" style={{ paddingRight: 5 }}
                onClick={() => this.props.moveDown(this.props.index)}
              >
                <i className="mdi mdi-arrow-down"></i>
              </span>
              : ''}
            <span
              className=""
              onClick={() => this.props.removeField(this.props.index)}
            >
              <i className="mdi mdi-close"></i>
            </span>
          </div>
        </div>
        <div className="card-body">
          {/* <ul className="nav nav-tabs">
            <li className="nav-item">
              <a
                onClick={(e) => {
                  e.preventDefault();
                  this.setState({ tab: "general" });
                }}
                className={
                  this.state.tab === "general" ? "nav-link active" : "nav-link"
                }
                href="/general"
              >
                General
              </a>
            </li>

            <li className="nav-item">
              <a
                onClick={(e) => {
                  e.preventDefault();
                  this.setState({ tab: "options" });
                }}
                className={
                  this.state.tab === "options" ? "nav-link active" : "nav-link"
                }
                href="/options"
              >
                Options
              </a>
            </li>
            <li
              className="nav-item"
              style={{
                textAlign: "right",
                position: "absolute",
                right: "15px",
              }}
            >
              <a
                onClick={(e) => {
                  e.preventDefault();
                  this.setState({ tab: "" });
                }}
                className={
                  this.state.tab === ""
                    ? "nav-link active font-weight-bold"
                    : "nav-link"
                }
                href="/hide"
              >
                -
              </a>
            </li>
          </ul> */}


          <div className="row">
            <div className="col-12 text-center">


              <Button
                variant="primary"
                onClick={() => this.setState({ mdShow: true })}
              >
                Edit Question
              </Button>
            </div>
            {this.state.title === " " ? (
              ""
            ) : (
              <div className="col-12">
                <div className="form-group mb-3 mt-3">
                  <p
                    dangerouslySetInnerHTML={{ __html: this.state.title }}
                  />
                </div>
              </div>


            )}


          </div>
          <div className="row">
            <div className="col-12">
              <div className="form-check">
                <input
                  defaultChecked={this.state.multiple}
                  onChange={(e) =>
                    this.changeValue("MULTIPLE", e.target.checked)
                  }
                  className="form-check-input"
                  type="checkbox"
                  id="multiple"
                />
                <label className="form-check-label" htmlFor="isRequired">
                  Multiple Selection
                </label>
              </div>
              <div className="form-check">
                <input
                  defaultChecked={this.state.validation.isRequired}
                  onChange={(e) =>
                    this.changeValue("IS_REQUIRED", e.target.checked)
                  }
                  className="form-check-input"
                  type="checkbox"
                  id="isRequired"
                />
                <label className="form-check-label" htmlFor="isRequired">
                  Required
                </label>
              </div>
            </div>
          </div>






          <div className="row">
            <p
              hidden={!this.state.duplicate}
              className="alert text-center alert-danger"
            >
              <strong>Duplicate</strong> Values Found
            </p>
            {this.state.radios ? (
              <table className="table text-center">
                <tbody>
                  {this.state.radios.map((checkbox, index) => {
                    return (
                      <tr key={index}>
                        {this.state.multiple ? (
                          <td style={{ verticalAlign: "middle" }}>
                            <div className="radio">
                              {
                                // <input
                                //   defaultChecked={
                                //     this.state.radios[index].selected
                                //   }
                                //   onChange={(e) =>
                                //     this.changeOptionValue(
                                //       index,
                                //       e.target.checked,
                                //       "SELECTED"
                                //     )
                                //   }
                                //   type="checkbox"
                                // />
                                <div className="form-check">
                                <label className="form-check-label text-muted">
                                  <input
                                      onChange={(e) =>
                                        this.changeOptionValue(
                                          index,
                                          e.target.checked,
                                          "SELECTED"
                                        )
                                      }
                                  
                                    type="checkbox"
                                  
                                    className="form-check-input"
                      
                                    defaultChecked={
                                      this.state.radios[index].selected
                                    }
                                  />
                                  <i className="input-helper"></i>
                                
                                </label>
                              </div>
  
                              }
                            </div>
                          </td>
                        ) : (
                          <td hidden={true}></td>
                        )}

                        <td>
                          <input
                            placeholder="Value"
                            value={this.state.radios[index].value}
                            onChange={(e) =>
                              this.changeOptionValue(
                                index,
                                e.target.value,
                                "VALUE"
                              )
                            }

                            id={checkbox.value}
                            type="text"
                            className="form-control"
                          />
                        </td>
                        {!this.state.multiple ? (
                          <td style={{ verticalAlign: "middle" }}>
                            <div className="form-check">
                              <label className="form-check-label text-muted">
                                <input
                                    onChange={(e) =>
                                      this.changeOptionValue(
                                        index,
                                        e.target.checked,
                                        "SELECTED"
                                      )
                                    }
                                
                                  type="radio"
                                  id={checkbox.value}
                                  className="form-check-input"
                    
                                  defaultChecked={
                                    this.state.radios[index].selected
                                  }
                                />
                                <i className="input-helper"></i>
                              
                              </label>
                            </div>

                            {/* <input
                              name="default"
                              defaultChecked={
                                this.state.radios[index].selected
                              }
                              onChange={(e) =>
                                this.changeOptionValue(
                                  index,
                                  e.target.checked,
                                  "SELECTED"
                                )
                              }
                              id={checkbox.value}
                              type="radio"
                            /> */}
                          </td>
                        ) : (
                          <td hidden={true}></td>
                        )}
                        <td style={{ verticalAlign: "middle" }}>
                          <i
                            onClick={() => this.removeOption(index)}
                            className="mdi mdi-close pull-right"
                          >

                          </i>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            ) : (
              <span></span>
            )}
            <button
              onClick={() => this.addOption()}
              className="btn form-control btn-sm btn-dark"
            >
              Add Option
            </button>
          </div>

        </div>
        <Modal
          show={this.state.mdShow}
          onHide={() => this.setState({ mdShow: false })}
          size="lg"
          aria-labelledby="example-modal-sizes-title-md"
        >
          <Modal.Body>
            <JoditEditor
              ref={(l) => (this.tooList = l)}
              value={this.state.title}
              config={this.config}
              onBlur={(newContent) => this.changeValue("TITLE", newContent)}
              onChange={(newContent) => this.changeValue("TITLE", newContent)}
            />
          </Modal.Body>

          <Modal.Footer className="flex-wrap">
            <>
              <Button
                variant="light"
                onClick={() => this.setState({ mdShow: false })}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={() => this.setState({ mdShow: false })}
              >
                Create
              </Button>
            </>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }

  changeOptionValue(index, value, state) {
    let radios = this.state.radios;

    let radio = {};
    if (state === "DEFAULT_VALUE") {
      this.setState({
        defaultValue: index,
      });
    }
    if (state === "TITLE") {
      radio = {
        ...radios[index],
        title: value,
      };
    } else if (state === "SELECTED") {
      if (this.state.multiple) {
        radio = {
          ...radios[index],
          selected: !radios[index].selected,
        };
      } else {
        radios.forEach((ele) => {
          return (ele.selected = false);
        });
        radio = {
          ...radios[index],
          selected: !radios[index].selected,
        };
      }
    } else if (state === "VALUE") {
      radio = {
        ...radios[index],
        value: value,
      };
    } else {
      radio = {
        ...radios[index],
      };
    }

    radios[index] = radio;
    console.log(radios);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }

  duplicates() {
    let radios = this.state.radios;
    let u = _.uniqBy(radios, "value");
    if (!_.isEqual(radios, u)) {
      this.setState({
        duplicate: true,
      });
    } else {
      this.setState({
        duplicate: false,
      });
    }
  }

  addOption() {
    let radio = {
      value: "",
      selected: false,
      u_select: false
    };
    let radios = this.state.radios;
    radios.push(radio);
    this.setState({
      radios: radios,
    });
    this.duplicates();
    setTimeout(() => {
      return this.props.changeState(this.state, this.props.index);
    }, 0);
  }
}

export default MCQ;
