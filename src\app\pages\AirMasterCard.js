import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';

import IncidentStory from "./IncidentStory";
import AirReviewerViewCard from "./AirReviewerViewCard";
import AirInvestigationViewCard from "./AirInvestigationViewCard";
import AirMedicalViewCard from "./AirMedicalViewCard";
import AirEngineerViewCard from "./AirEngineerViewCard";
import AirCostEstimatorViewCard from "./AirCostEstimatorViewCard";
import AirFinanceViewCard from "./AirFinanceViewCard";
import AirHodViewCard from "./AirHodViewCard";
import AirTruckStatusCard from "./AirTruckStatusCard";
import AirDocumentCard from "./AirDocumentCard";
import API from "../services/API";
import { AIR_WITH_ID_URL } from "../constants";
import cogoToast from "cogo-toast";
import { singlePopup, secondaryPopup } from "./../notifications/Swal";

const TabPanel = (props) => {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}


const AirMasterCard = ({ showModal, setShowModal, data, setData }) => {

    // const personsInvolved = data.personInvolved ? JSON.parse(data.personInvolved).map(person => person.name).join(', ') : null;
    // const witnesses = data.witnessInvolved ? JSON.parse(data.witnessInvolved).map(witness => witness.name).join(', ') : null;
    // const bodyParts = data.bodyPartInjured ? data.bodyPartInjured.map(part => `${part.slug} (intensity: ${part.intensity})`).join(', ') : null;
    const [tabValue, setTabValue] = useState(0);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const closeIR = async () => {

        secondaryPopup.fire({
            title: 'Please review the IR and ensure all the documents / evidence are uploaded. Proceed if you confirm',
            // text: "You won't be able to revert this!",
            icon: 'success',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Confirm'
        }).then(async (result) => {
            if (result.isConfirmed) {

                const response = await API.patch(AIR_WITH_ID_URL(data.id), { status: 'Stage 6: IR Closed', stage: 'closed' });
                if (response.status === 204) {
                    setShowModal(false);
                    cogoToast.success('IR Closed')
                }


            } else {


            }
        })

    }

    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    IR Information - {data.maskId || ''}
                </Modal.Header>

                <Modal.Body>
                    <Box sx={{ display: 'flex', width: '100%' }}>
                        <Tabs
                            orientation="vertical"
                            variant="scrollable"
                            value={tabValue}
                            onChange={handleTabChange}
                            aria-label="Vertical tabs example"
                            sx={{ borderRight: 1, borderColor: 'divider' }}
                        >
                            <Tab label="Incident Initial Report" />

                            <Tab label="Investigation Report" />
                            <Tab label="Medical Comments" />
                            <Tab label="Engineer Comments" />
                            <Tab label="Cost Estimation"  />

                            <Tab label="Finance Report" />
                            <Tab label="HOD Report" />

                            <Tab label="External Truck Receipt" />
                            <Tab label="Investigation Documents" />
                            <Tab label="Receipts" />
                            <Tab label="Survey Report" />

                            {/* Add more tabs as needed */}
                        </Tabs>
                        <TabPanel value={tabValue} index={0}>
                            <IncidentStory data={data} />
                        </TabPanel>
                        <TabPanel value={tabValue} index={1} className="w-100">
                            <AirInvestigationViewCard incidentData={data} />
                        </TabPanel>

                        <TabPanel value={tabValue} index={2} className="w-100">
                            <AirMedicalViewCard data={data} />
                        </TabPanel>

                        <TabPanel value={tabValue} index={3} className="w-100">
                            <AirEngineerViewCard data={data} />
                        </TabPanel>

                        <TabPanel value={tabValue} index={4} className="w-100">
                            <AirCostEstimatorViewCard data={data} />
                        </TabPanel>

                        <TabPanel value={tabValue} index={5} className="w-100">
                            <AirFinanceViewCard data={data} />
                        </TabPanel>
                        <TabPanel value={tabValue} index={6} className="w-100">
                            <AirHodViewCard data={data} />
                        </TabPanel>

                        <TabPanel value={tabValue} index={7} className="w-100">
                            <AirTruckStatusCard data={data} />
                        </TabPanel>
                        <TabPanel value={tabValue} index={8} className="w-100">
                            <AirDocumentCard type="investigation" data={data} />
                        </TabPanel>
                        <TabPanel value={tabValue} index={9} className="w-100">
                            <AirDocumentCard type="receipts" data={data} />
                        </TabPanel>
                        <TabPanel value={tabValue} index={10} className="w-100">
                            <AirDocumentCard type="surveyor" data={data} />
                        </TabPanel>


                        {/* Add more TabPanels as needed based on your tabs */}
                    </Box>

                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {<Button variant="primary" onClick={() => { closeIR(); }}>Close IR</Button>}
                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirMasterCard;