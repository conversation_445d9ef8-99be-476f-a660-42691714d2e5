import React, { useState, useEffect } from 'react';
import API from '../services/API';
import { ACTION_URL, ALL_ACTION_URL, STATIC_URL } from '../constants';

const ActionCardNew = ({ application, type, id }) => {
    const [actions, setActions] = useState([])
    const getActions = async () => {
        const response = await API.get(ALL_ACTION_URL);
        if (response.status === 200) {
            // console.log(response.data.filter(i => i.application === application && i.objectId === id))
            setActions(response.data.filter(i => i.application === application && i.objectId === id && type.includes(i.actionType)))
        }
    }

    useEffect(() => { console.log(application, type, id); getActions() }, [])

    const actionTypeToDisplay = {
        'air_reviewer': 'IR - Review',
        'air_investigator': 'IR - Initiate Investigation',
        'air_cost_estimator': 'IR - Estimate Cost',
        'air_gmops_review': 'IR - Punitive / Correction Action Taker Review',
        'air_trainee': 'IR - Trainee Actions',
        'air_duty_manager': 'IR - Duty Manager',
        'air_cost_reviewer': 'IR - Finance Settlement',
        'air_finance': 'IR - Claims Coordinator',
        'air_surveyor': 'IR - Survey',
        'air_medical_officer': 'IR - Medical Report',
        'air_medical_approve': 'IR - Medical Report Approve',
        'air_engineer': 'IR - Engineer Review',
        'take_investigation_actions': 'IR - Take Actions',
        'verify_actions': 'IR - Verify Actions',
        'retake_actions': 'IR - Retake Actions',
        'air_report_work': 'IR - Medical Report Work',
        'air_hod_review': 'IR - HOD Review',
        'air_hod_finance': 'IR - HOD Review Before Finance Submission'
        // Add other actionType to display string mappings as needed
    };

    return (<>
        <table className='table table-responsive table-striped table-bordered'>
            <thead>
                <tr>

                    <th>Action Type</th>
                    <th>Action Taken</th>
                    <th>Action To Be Taken</th>
                    <th>Uploads</th>
                    <th>Due Date</th>
                    <th>Status</th>
                    <th>Created Date</th>
                    <th>Submitted By</th>
                </tr>
            </thead>
            <tbody>
                {actions.map(action => (
                    <tr key={action.id}>

                        <td>{actionTypeToDisplay[action.actionType]}</td>
                        <td>{action.actionTaken}</td>
                        <td>{action.actionToBeTaken}</td>
                        <td>{
                            (action.uploads && action.uploads.length > 0) ? action.uploads.map(i => {
                                return (
                                    <div>
                                        <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                    </div>
                                )
                            }) : 'No Documents Uploaded'
                        }</td>
                        <td>{action.dueDate}</td>
                        <td>{action.status}</td>
                        <td>{action.createdDate}</td>
                        <td>{action.actionSubmittedBy.email}</td>
                        {/* Extend this to include other properties as needed */}
                    </tr>
                ))}
            </tbody>
        </table>
    </>)
}

export default ActionCardNew;