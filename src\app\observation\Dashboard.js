import React, { useState, useEffect } from "react";
import { Nav, Tab, OverlayTrigger, Tooltip, Popover } from "react-bootstrap";
import MaterialTable from "material-table";
import { RISKASSESSMENT_LIST, RISK_WITH_ID_URL, USERS_URL, OBSERVATION_REPORT_URL, OBSERVATION_REPORT_BY_OTHERS_URL, ACTION_URL } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import CardOverlay from '../pages/CardOverlay';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';
import * as Icon from 'feather-icons-react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography'
import Actions from "./Actions";
import AppSwitch from "../pages/AppSwitch";
import Ehs from "../pages/Ehs";
import Other from "./Others";
import Summary from "./Summary";

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};
function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box >
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};
const Dashboard = () => {
  const user = useSelector((state) => state.login.user)
  console.log(user)
  const history = useHistory();
  const location = useLocation()
  const [data, setData] = useState([])
  const [risk, setRisk] = useState([])
  const [overdue, setOverdue] = useState([])
  const [users, setUsers] = useState()
  const [additional, setAdditional] = useState([])
  const [aOverdue, setAOverdue] = useState([])
  const [access, setAccess] = useState(false)
  const [actionData, setActionData] = useState([]);
  const [obsData, setObsData] = useState([]);
  const [obsDataOther, setObsDataOther] = useState([]);
  const [obsDataOtherFilter, setObsDataOtherFilter] = useState([])
  const [obsFilterData, setObsFilterData] = useState([])
  const [rendered, setRendered] = useState(0)
  const [option, setOption] = useState([{ label: 'Observation', value: 'obs' }, { label: 'ePermit Work', value: 'eptw' }, { label: 'Document', value: 'doc' }, { label: 'Risk', value: 'ra' }, { label: 'Incident', value: 'ir' }])
  const [summary, setSummary] = useState([])

  // useEffect(() => { getAllUsers() }, [])


  useEffect(() => {
    async function fetchData() {
      // First, run getAllUsers and wait for it to finish.

      // After getAllUsers is complete, run the other functions.
      await getObservationData();
      await getObservationDataOthers();
      await getActionData();
    }

    // Call the fetchData function
    fetchData();
  }, []);


  const getAllUsers = async () => {
    const response = await API.get(USERS_URL);
    setUsers(response.data)
  }
  function getName(id) {

    if (id !== '') {
      console.log(users)
      console.log(id)
      // const user = users.find(user => user.id === id)
      // return id ? user?.firstName || '' : ''
    }
  }
  const getActionData = async () => {



    const response = await API.get(ACTION_URL);
    if (response.status === 200) {
      setActionData(response.data)

    }
  }

  const customSwal = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-danger',
      cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
  })
  const customSwal2 = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
  })

  const getPermit = async () => {
    const uriString = { include: ["user"] };

    const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {
      //   const utc = moment()
      //   console.log(moment.utc().format('DD-MM-YYYY'))
      const over = response.data.filter(item =>
        moment().utc(item.nextdate, 'DD-MM-YYYY').isBefore(moment().utc().format())
      )

      setOverdue(over)
      const add = response.data.filter(item =>

        item.additional.includes('No')
      )
      console.log(add)
      setAdditional(add)

      const addControl = add.filter(item => {
        let check = []
        if (item.additionalDates) {

          item.additionalDates.map((item) => {

            item.map((item2) => {

              if (item2.date !== '') {

                const dateString = item2.date;
                const givenDate = moment(dateString);
                const today = moment();
                if (givenDate.isBefore(today)) {
                  console.log('The given date is before today.');
                  console.log(givenDate + 'before')
                  console.log(today + 'today')
                  check.push(true)
                } else if (givenDate.isAfter(today)) {
                  console.log('The given date is after today.');
                  console.log(givenDate + 'after')
                  console.log(today + 'today')
                  check.push(false)
                } else {
                  console.log('The given date is today.');
                }

              }

            })

          }

          )
        }
        return check.includes(true)

      })

      setAOverdue(addControl)
      setRisk(response.data)

    }

    if (user.length !== 0) {
      setAccess(user.roles.some(item => item.name === 'RA Team Leader'))
    }

  }
  const defaultMaterialTheme = createTheme();
  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };
  const viewRisk = (data) => {
    let id = data.id
    if (data.type.label === 'Hazard-Based') {
      history.push('/risk-assessment/viewhazard', { id })
    } else {
      history.push('/risk-assessment/viewrisk', { id })
    }
  }
  const editRisk = (data) => {
    let id = data.id
    if (data.type.label === 'Hazard-Based') {
      history.push('/risk-assessment/amendhazard', { id })
    } else {
      history.push('/risk-assessment/amendrisk', { id })
    }
  }

  const onDelete = async (id) => {

    customSwal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        const response = await API.delete(RISK_WITH_ID_URL(id));
        if (response.status === 204) {

          customSwal2.fire(
            'Deleted!',
            '',
            'success'
          )


        }
        getPermit();
      }
    })

  }

  const tableActions = [

    {
      icon: 'visibility',
      tooltip: 'View RA',
      onClick: (event, rowData) => {

        viewRisk(rowData)
        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      }
    },
    {
      icon: "modeEdit",
      tooltip: "Edit RA",
      onClick: (event, rowData) => {
        if (access) {
          editRisk(rowData)
        } else {
          customSwal2.fire(
            'You Cant Access',
            '',
            'success'
          )
        }
        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      },
    },
    {
      icon: "delete",
      tooltip: "Delete RA",
      onClick: (event, rowData) => {
        if (access) {
          onDelete(rowData.id)
        } else {
          customSwal2.fire(
            'You Cant Access',
            '',
            'success'
          )
        }

        // Do save operation
        // console.log(rowData)
        // viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
      },
    },
  ]
  const columns = [

    {
      field: 'meetid',
      title: 'RA No',


    },
    {
      field: 'activity',
      title: 'Process/Activity',
      render: (row) => {
        if (row.type.label === 'Hazard-Based') {
          return 'N/A';
        } else {
          if (row.activity === 'Others') {
            return row.activity + '-' + row.otherActivity
          } else {
            return row.activity
          }


        }
      }


    },
    {
      field: 'type.label',
      title: 'Type',

      // formatter: (cell, row) => {

      //   return (
      //     <>{row.contractor.name}</>
      //   );
      // }

    },
    {
      field: 'department',
      title: 'Initiated by',



    },


    {
      field: 'date',
      title: 'Published / Amended Date/Time',


    },
    {
      field: 'nextdate',
      title: 'Next Review Date',


    },
    {
      field: 'status',
      title: 'Status',


    },

    {
      field: 'user.firstName',
      title: 'RA Leader',


    },


  ];
  const [value, setValue] = useState('MY ACTIONS');

  const TABS = {
    ACTIONS: "MY ACTIONS",
    DASHBOARD: "DASHBOARD",
    ROUTINE: "ROUTINE",
    NONROUTINE: "NONROUTINE",
    HAZARD: "HAZARD",
    TOOLBOX: "TOOLBOX",
    // UNDER_INVESTIGATION: "UNDER_INVESTIGATION",
    // ACTIONS: "ACTIONS"

  };
  const handleChange = (event, newValue) => {

    setValue(newValue);
  };
  const getCloseActionDate = (item) => {


    const status = setStatus(item)

    if (status === 'Reported & Closed' || status === 'Action Verified - Closed' || status === 'Reported & Rectified on Spot') {
      if (item.actions) {
        const last = item.actions[item.actions.length - 1]
        console.log(last)

        return moment(last.createdDate).format('Do MMM YYYY')
      }




      console.log()

    } else {
      return ''
    }



  }
  const getObservationData = async () => {

    const params = {
      "include": [{ "relation": "ghsOne" }, { "relation": "workActivityDepartment" }, { "relation": "submitted" }, { "relation": "actions" }]

    };
    const response = await API.get(`${OBSERVATION_REPORT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    if (response.status === 200) {
      const preprocessedData = response.data.map(item => ({
        ...item,
        'submitted.firstName': item.submitted ? item.submitted.firstName : '',
        'color': setColor(item),
        created: moment(item.created).format('Do MMM YYYY '),
        type: item.type === 'Safe' ? 'Positive' : JSON.parse(item.remarks).unsafeCondition === true ? "Unsafe Condition" : JSON.parse(item.remarks).unsafeAct === true ? "Unsafe Act" : item.type === 'Positive' ? 'Positive' : item.type,
        status: setStatus(item),
        closeDate: getCloseActionDate(item),
        assignee: item.actionOwnerId
        // assignee: item.actionOwnerId && item.actionOwnerId !== '' ? getName(item.actionOwnerId) : ''
      }));
      setObsData(preprocessedData)
      setObsFilterData(preprocessedData)

    }
  }

  const setColor = (item) => {

    if (item.type === 'Safe' || item.status === 'At Risk - Closed' || item.status === 'Approved' || item.dueDate === '') {
      return 'None'
    }

    if (moment().isAfter(moment(item.dueDate, 'DD-MM-YYYY'))) {
      return 'Overdue'
    } else if (moment().isSame(moment(item.dueDate, 'DD-MM-YYYY'))) {
      return 'Due Soon'
    } else if (moment().isBefore(moment(item.dueDate, 'DD-MM-YYYY'))) {
      return 'Upcoming'
    }



  }
  const setStatus = (item) => {
    console.log(item.status)

    // return item.status;

    if (item.rectifiedStatus === 'Yes') {
      return 'Reported & Rectified on Spot';
    }
    if (item.type === 'Safe') {
      return 'Reported & Closed';
    }

    let status = ''
    switch (item.status) {
      case 'In Review':
        status = 'Actions Taken - Pending Verification'
        break;
      case 'Returned':
        status = 'Action Reassigned'
        break;
      case 'Initiated':
        status = 'Actions Assigned'
        break;
      case 'Approved':
        status = 'Action Verified - Closed'
        break;
      default:
        status = item.status
        break;
    }

    return status;


  }
  const getObservationDataOthers = async () => {

    const params = {
      "include": [{ "relation": "ghsOne" }, { "relation": "workActivityDepartment" }, { "relation": "submitted" }, { "relation": "actions" }]
    };
    const response = await API.get(`${OBSERVATION_REPORT_BY_OTHERS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    if (response.status === 200) {

      const preprocessedData = response.data.map(item => ({
        ...item,
        'submitted.firstName': item.submitted ? item.submitted.firstName : '',
        'color': setColor(item),
        'created': moment(item.created).format('Do MMM YYYY'),
        type: item.type === 'Safe' ? 'Positive' : JSON.parse(item.remarks).unsafeCondition === true ? "Unsafe Condition" : JSON.parse(item.remarks).unsafeAct === true ? "Unsafe Act" : item.type === 'Positive' ? 'Positive' : item.type,
        status: setStatus(item),
        closeDate: getCloseActionDate(item),
        assignee: item.actionOwnerId

      }));
      // response.data.map(item=>{
      //   console.log(JSON.parse(item.remarks))
      // })
      setObsDataOther(preprocessedData)
      setObsDataOtherFilter(preprocessedData)

    }
  }
  const getFilteredActions = (applicationType, statusList) => {
    return actionData.filter(action =>
      action.application === applicationType && statusList.includes(action.status)
    );
  }
  const data1 = [...obsData, ...obsDataOther].sort((a, b) => {
    let aIdLastPart = parseInt(a.maskId.split('-').pop());
    let bIdLastPart = parseInt(b.maskId.split('-').pop());
    return bIdLastPart - aIdLastPart;
  });
  const data2 = obsData.sort((a, b) => {
    let aIdLastPart = parseInt(a.maskId.split('-').pop());
    let bIdLastPart = parseInt(b.maskId.split('-').pop());
    return bIdLastPart - aIdLastPart;
  });
  return (
    <>
      <AppSwitch value={{ label: 'Observation', value: 'observation' }} />

      <Tabs value={value} onChange={handleChange} aria-label="incident report table" className="risk">

        <MTab label={
          <Typography variant="body1" style={customFontStyle}>
            My Actions <span className='headerCount'>{getFilteredActions('Observation', ['open', 'returned']).length}</span>
          </Typography>
        } value={TABS.ACTIONS} />

        <MTab label={
          <Typography variant="body1" style={customFontStyle}>
            Observations Reported by You <span className='headerCount'>{obsData.length}</span>
          </Typography>
        } value={TABS.DASHBOARD} />

        <MTab label={
          <Typography variant="body1" style={customFontStyle}>
            Observations Master Data  <span className='headerCount'>{[...obsData, ...obsDataOther].length}</span>
          </Typography>
        } value={TABS.ROUTINE} />

        <MTab label={
          <Typography variant="body1" style={customFontStyle}>
            Observation Summary
          </Typography>
        } value={TABS.NONROUTINE} />

        {/* <MTab label={"Routine"} value={TABS.ROUTINE} />
        <MTab label={"Non Routine"} value={TABS.NONROUTINE} />
        <MTab label={"High-Risk Hazard Control Measures"} value={TABS.HAZARD} />

        <MTab label={"Toolbox Talk Records"} value={TABS.TOOLBOX} /> */}


      </Tabs>

      <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
        <Actions action={getFilteredActions('Observation', ['open', 'returned'])} applicationType="Observation" setRendered={setRendered} />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.DASHBOARD}>
        {/* <RiskAssessment /> */}
        <Ehs obsdata={data2} />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.ROUTINE}>
        {/* <RiskAssessment /> */}
        <Other obsdata={data1} />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.NONROUTINE}>
        {/* <RiskAssessment /> */}
        <Summary obsdata={[...obsData, ...obsDataOther]} />
      </CustomTabPanel>


    </>
  );
};

export default Dashboard;
