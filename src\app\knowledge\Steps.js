import React, { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import API from "../services/API";
import { CREATE_STEP_TITLE, STEP_WITH_ID_URL } from "../constants";
import { singlePopup, deletePopup } from "./../notifications/Swal";
import { useEffect } from "react";
import { useHistory, useLocation } from "react-router-dom";
const Steps = (props) => {
    const history = useHistory()
    const location = useLocation()
    console.log(location)
    const itemName = useRef();
    const stepName = useRef();
    const [showModal, setShowModal] = useState(false);
    const [smModal, setSmModal] = useState(false);
    const [data, setData] = useState([]);
    const [title, setTitle] = useState('');
    useEffect(() => {
        getStepTitle(location.state);
    }, [location.state]);

    const getStepTitle = async (id) => {
        const response = await API.get(CREATE_STEP_TITLE(id));
        if (response.status === 200) {
            setData(response.data);
        }
    };

    const handleUpdate = async () => {
        const response = await API.post(CREATE_STEP_TITLE(location.state), {
            name: itemName.current.value
        });
        if (response.status === 200) {
            singlePopup.fire("Created!", "", "success");
            getStepTitle(location.state);
            setShowModal(false)
        }
    };
    const handleTitle = async () => {
        const response = await API.patch(STEP_WITH_ID_URL(title.id), {
            name: stepName.current.value,
        });
        if (response.status === 204) {
            singlePopup.fire("Updated!", "", "success");
            getStepTitle(location.state);
            setSmModal(false)
        }
    };
    const deleteStep = (item) => {
        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await fetch(STEP_WITH_ID_URL(item.id), {
                    method: 'DELETE',
                    headers: {
                        "Content-type": "application/json; charset=UTF-8"
                    }
                })
                if (response.ok) {
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                    getStepTitle(location.state);
                }
            }
        })
    }

    const openCurate = (item) => {
        history.push('/knowledge/curate/add', item.id)
    }

    return (
        <div>
            <div className="row">
                <div className="col-lg-12 grid-margin stretch-card">
                    <div className="card">
                        <div className="card-body">
                            <h4 className="card-title">Steps</h4>
                            <div className="row">
                                <div className="col-12">
                                    {data.map((item) => {
                                        return (
                                            <div
                                                className="step mt-3 d-flex col-12"
                                                style={{
                                                    padding: "15px",
                                                    boxShadow: "0px 0px 13px 2px #e8e8e8",
                                                    justifyContent: 'space-between'
                                                }}
                                            >
                                                <div className="col-10 d-flex align-items-center">
                                                    <h4 className="m-0 ">{item.name}</h4>
                                                </div>

                                                <div className="curate d-flex col-2 justify-content-between">
                                                    <i className="mdi mdi-delete" onClick={() => deleteStep(item)}></i>
                                                    <i className="mdi mdi-border-color" onClick={() => { setSmModal(true); setTitle(item) }}></i>
                                                    <i className="mdi mdi-forward" onClick={() => openCurate(item)}></i>
                                                </div>
                                            </div>
                                        );
                                    })}
                                    <div className="mt-4" style={{ textAlign: "center" }}>
                                        <Button
                                            variant="primary"
                                            onClick={() => setShowModal(true)}
                                            className="me-3"
                                        >
                                            Add Step
                                        </Button>
                                        <Button
                                            variant="light"
                                            onClick={() => history.go(-1)}
                                        >
                                           Back
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Modal
                size="md"
                show={showModal}
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-lg"
            >
                <Modal.Header closeButton>
                    <Modal.Title>Add Step Name</Modal.Title>
                </Modal.Header>

                <Modal.Body>
                    <form className="forms-sample">
                        <div className="form-group">
                            <label htmlFor="title">Item Name</label>
                            <input
                                type="text"
                                ref={itemName}
                                className="form-control p-input"
                                id="title"
                                placeholder="Item Name"
                            />
                        </div>
                    </form>
                </Modal.Body>

                <Modal.Footer>
                    <Button variant="light" onClick={() => setShowModal(false)}>
                        Cancel
                    </Button>
                    <Button variant="primary" onClick={handleUpdate}>
                        Save
                    </Button>
                </Modal.Footer>
            </Modal>
            <Modal
                size="md"
                show={smModal}
                onHide={() => setSmModal(false)}
                aria-labelledby="example-modal-sizes-title-lg"
            >
                <Modal.Header closeButton>
                    <Modal.Title>Edit Step Name</Modal.Title>
                </Modal.Header>

                <Modal.Body>
                    <form className="forms-sample">
                        <div className="form-group">
                            <label htmlFor="title">Step Name</label>
                            <input
                                type="text"
                                ref={stepName}
                                className="form-control p-input"
                                defaultValue={title.name}
                                id="title"
                                placeholder="Item Name"
                            />
                        </div>
                    </form>
                </Modal.Body>

                <Modal.Footer>
                    <Button variant="light" onClick={() => setSmModal(false)}>
                        Cancel
                    </Button>
                    <Button variant="primary" onClick={handleTitle}>
                        Update
                    </Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
}

export default Steps;
