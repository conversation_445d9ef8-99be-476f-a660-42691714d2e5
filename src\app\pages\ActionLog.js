import React, { useState, useEffect } from 'react'
import * as Icon from 'feather-icons-react';
import { Dropdown } from 'react-bootstrap';
// import 'bootstrap-daterangepicker/daterangepicker.css';

function ActionLog({ data, dates, total, onSearch }) {
    console.log(total)
    const [date, setDate] = useState('')
    const [count, setCount] = useState(0)

    const [startIndex, setStartIndex] = useState(0);
    const [displayedDates, setDisplayedDates] = useState([]);
    useEffect(() => {

        if (dates.length !== 0) {
            setDisplayedDates(dates.slice(0, 4))
            let cou = 0
            dates.slice(0, 4).forEach(date => (
                cou = data[date].length + cou
            ))
            setCount(cou)
        }

    }, [])



    const handleClick = () => {
        const nextIndexes = startIndex + 3;
        const nextDates = dates.slice(nextIndexes, nextIndexes + 3);
        setDisplayedDates([...displayedDates, ...nextDates]);
        setStartIndex(nextIndexes);
        let cou = 0
        dates.slice(0, nextIndexes + 3).forEach(date => (
            cou = data[date].length + cou
        ))
        setCount(cou)
    };

    const dispalyActionComments = (action) => {
        switch (action.application) {
            case 'DOC':


                break;
            case 'AIR':

                break;
            case 'RA':

                break;
        }



    }


    return (
        <div className='col-12 '>
            <div className='row mb-4'>
                <div className='col-6'>
                    <h5 className='mt-4 fw-bold actionTitle'>My Activity Log</h5>
                </div>
                <div className='col-6 searchbox d-flex justify-content-end align-items-center'>
                    {/* <span className='me-3'>Filter Applied (3)</span> */}

                    <div class="search me-2">
                        <span class="fa fa-search"></span>
                        <input placeholder="Search Activity" className='form-control' onChange={(e) => onSearch(e.target.value)} />
                    </div>
                    <div className="nav-item nav-profile ">

                        <Dropdown>
                            <Dropdown.Toggle className="nav-link filter btn-default">
                                <Icon.Filter currentColor='#D0D5DD' />
                                <Icon.ArrowRight currentColor='#D0D5DD' />
                            </Dropdown.Toggle>
                            <Dropdown.Menu className="option-list navbar-dropdown custom-dropdown">
                                <div className=''>
                                    <h4 className='p-2 fil-h4'>Filter</h4>

                                    <div className='p-2 type-fil'>
                                        <h3 className=' fil-h3'>Action Type</h3>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Observation ' /> Observation</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='ePermit to Work ' /> ePermit to Work</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Incident ' /> Incident</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Document' /> Document</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Risk' />Integrated Risk Management</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Knowledge' /> Knowledge</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Asset' /> Asset</label>
                                    </div>
                                    {/* <div className='p-2 type-fil searchbox'>
                                        <h3 className=' fil-h3'>View Activity between</h3>


                                    </div> */}
                                    <div className='p-2 mt-2 d-flex flex-column'>
                                        <button
                                            type="button"
                                            className="btn btn-outline-light  mb-3 "
                                            onClick={(e) => {

                                            }}
                                        >
                                            Reset Filter
                                        </button>
                                        <button
                                            type="button"
                                            className="btn btn-secondary  mb-3 "
                                            onClick={(e) => {

                                            }}
                                        >

                                            Apply Filter
                                        </button>


                                    </div>
                                </div>
                            </Dropdown.Menu>
                        </Dropdown>


                    </div>
                </div>
            </div>
            <div className="timeline">

                {displayedDates.map(date => (
                    <div className="timeline-wrapper ">
                        <div className="timeline-badge"></div>
                        <div className="timeline-panel">
                            <div className='timeline-date mb-3 fw-bold '>
                                {date}
                            </div>
                            <div className="timeline-inbox">
                                {data[date].map(action => (
                                    <div className='row mb-2 align-items-center'>

                                        <div className='time col-1'>{action.time}</div>
                                        <div className='col-2 '>
                                            {action.application === 'AIR' ?
                                                <div className='incidentbadge badge '>Incident</div> :
                                                action.application === 'DOC' ?
                                                    <div className='documentbadge badge '>Document</div> :
                                                    action.application === 'RA' ?
                                                        <div className='riskbadge badge '>Integrated Risk Management</div> :
                                                        action.application === 'PermitToWork' ? 
                                                         <div className='eptwbadge badge '>EPTW</div> : 
                                                        action.application === 'Observation' ?  
                                                        <div className='obsbadge badge '>OBS</div> : 
                                                        ''
                                                      
                                            }

                                        </div>
                                        <div className='desc col-3'>{action.applicationDetails ? action.applicationDetails.maskId ? action.applicationDetails.maskId : action.applicationDetails.meetid : ''}</div>
                                        <div className='desc col-6'>

                                            {dispalyActionComments(action)}
                                            {action.actionType === 'doc_initiated' ?
                                                <>Document is Initiated </> :
                                                action.actionType === 'doc_reviewed' ?
                                                    <>Document is Reviewed  </>
                                                    :
                                                    action.actionType === 'doc_approved' ?
                                                        <>Document is Approved  </>
                                                        :
                                                        action.actionType === 'air_reviewer' ?
                                                            <>Incident is Reviewed by </>
                                                            :
                                                            action.actionType === 'air_medical_officer' ?
                                                                <>Incident is Sent to Medical Report by </>
                                                                :
                                                                action.actionType === 'air_cost_estimator' ?
                                                                    <>Sent to Cost Estimator </>
                                                                    :
                                                                    action.actionType === 'air_engineer' ?
                                                                        <>Incident is Sent to Engineer Review by </>
                                                                        :
                                                                        action.actionType === 'take_investigation_actions' ?
                                                                            <>Incident is Sent to Take Action by </>
                                                                            :
                                                                            action.actionType === 'reject' ?
                                                                                <>Incident is Reject by </>
                                                                                : action.actionType === 'ra_confirm' ?
                                                                                    <>Draft RiskAssessment Released </>
                                                                                    :
                                                                                    action.actionType === 'approve' ?
                                                                                        <>Sent to Approver </>
                                                                                        :
                                                                                        action.actionType === 'approve' ?
                                                                                            <>Sent to Approver </>
                                                                                            :

                                                                                            ''
                                            }

                                        </div>

                                    </div>
                                ))}


                            </div>
                        </div>



                    </div>
                ))}


                {/* <div className="timeline-wrapper ">
                    <div className="timeline-badge"></div>
                    <div className="timeline-panel">
                        <div className='timeline-date mb-3 fw-bold '>
                            10 Jan, 2024
                        </div>
                        <div className="timeline-inbox">
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='obsbadge badge '>Observation</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='eptwbadge badge '>ePermit to Work</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='incidentbadge badge '>Incident</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='documentbadge badge '>Document</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='riskbadge badge'>Risk</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                        </div>
                    </div>

                </div> */}
            </div>
            <div className='action-option ps-5'>
                <p>Showing {count} of {total} activities</p>
                {count === total ? '' :
                    <p className='loadmore' onClick={handleClick}>Load more</p>
                }
            </div>
        </div>

    )
}

export default ActionLog