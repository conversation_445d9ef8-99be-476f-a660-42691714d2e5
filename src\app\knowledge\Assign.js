import React ,{useEffect} from 'react'
import API from '../services/API';
import {USER_LIST_PROGRAM} from '../constants'

function Assign() {

    useEffect(() => {
        getStepTitle();
    }, [])

    const getStepTitle = async (id) => {
        const response = await API.post('http://localhost:3100/knowledge-session-start',{
            tierThreeId:'6504221d4e847bbd2ef80c9b'
        });
        
    };
    
  return (
    <div>Assign</div>
  )
}

export default Assign