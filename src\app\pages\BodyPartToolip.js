import React, { useState } from 'react';
import { Mo<PERSON>, Button } from 'react-bootstrap';
import { BodyComponent } from 'reactjs-human-body';

const BodyPartTooltip = ({ content, children }) => {
    const [show, setShow] = useState(false);

    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    return (
        <div style={{ position: 'relative', display: 'inline-block' }}>
            <div onClick={handleShow}>
                {children}
            </div>

            <Modal show={show} onHide={handleClose}>
                <Modal.Header closeButton>
                    <Modal.Title>Body Part Details</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <BodyComponent partsInput={content} />
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
};

export default BodyPartTooltip;
