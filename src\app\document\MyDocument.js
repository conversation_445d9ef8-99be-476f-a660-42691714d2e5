import React, { useState, useEffect } from 'react'
import API from '../services/API';
import { DOCUMENT_CATEGORY, DOCUMENTUSER } from '../constants';
import { Row, Image, Col, Modal, Button } from 'react-bootstrap'
function MyDocument() {
    const [category, setCategory] = useState([])
    const [cateShow, setCateShow] = useState(false)
    const [document, setDocument] = useState([])
    const [selectDocument, setSelectDocument] = useState([])
    const [smDocShow, setSmDocShow] = useState(false)
    const [selectId, setSelectId] = useState('')
    useEffect(() => {
        getCategory();
        getDocumentList();

    }, [])
    const getDocumentList = async () => {
        const response = await API.get(DOCUMENTUSER);
        if (response.status === 200) {
            setDocument(response.data)

        }
    }
    const getCategory = async () => {
        const params = {
            include: [
                {
                    relation: "documents"
                },
            ],
        };

        let url = `${DOCUMENT_CATEGORY}?filter=${encodeURIComponent(JSON.stringify(params))}`;
        const response = await API.get(url);
        if (response.status === 200) {
            setCategory(response.data)
        }

    }

    const displayDate = (item) => {
        if (item.documents) {
            let count = item.documents.length
            return item.documents[count - 1].createdAt

        }
        return ''

    }

    const openDoc = (cate) => {
        const list = document.filter(item => item.cate.id === cate.id)
        console.log(list)
        setSelectDocument(list)

        setCateShow(true)
    }
    const openOneDoc = (item) => {
        setSelectId(item)
     


    }
    const renderField = (field, index) => {


        if (field.toolType === 'WEB_LINK') {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <a href={field.title} >{field.title}</a>
              </div>
            </div>
          )
    
    
        } else if (field.toolType === 'IMAGE') {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <img src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_image/' + field.title} style={{ maxWidth: '100%' }} />
              </div>
            </div>
          )
        } else if (field.toolType === 'YOUTUBE') {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <iframe sandbox="allow-same-origin allow-forms allow-popups allow-scripts allow-presentation" src={field.title} allowfullscreen=""></iframe>
              </div>
            </div>
          )
        } else if (field.toolType === 'VIDEO') {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
    
                <video controls style={{ maxWidth: '100%' }}>
                  <source src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_video/' + field.title} type="video/mp4" />
                  Your browser does not support video play.
                </video>
              </div>
            </div>
          )
        } else if (field.toolType === "PARAGRAPH") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p dangerouslySetInnerHTML={{ __html: field.content }} />
    
              </div>
            </div>
          )
        } else if (field.toolType === "AUDIO") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <div className="col-12 text-center">
                  <audio controls style={{ maxWidth: '100%' }}>
                    <source src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_audio/' + field.title} />
                  </audio>
                </div>
    
              </div>
            </div>
          )
        } else if (field.toolType === "PDF") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <div className="col-12 text-center">
                  <a href={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/pdf_uploads/' + field.title} target="_blank"><i className="fa fa-file-pdf-o fa-5x"></i><p style={{ textDecoration: 'none' }}>click to view</p></a></div>
    
              </div>
            </div>
          )
        } else if (field.toolType === "EMBEDCODE") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <div className="col-12 text-center">
                  <div dangerouslySetInnerHTML={{ __html: field.title }} style={{ width: '100%' }} /></div>
    
              </div>
            </div>
          )
        }
        else if (field.toolType === "MCQ") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p
                  dangerouslySetInnerHTML={{ __html: field.title }}
                />
                {field.radios.map(item => {
                  return (
                    <div className="form-check">
                      <input
    
    
                        className="form-check-input"
                        type="checkbox"
                        id="isRequired"
                      />
                      <label className="form-check-label" htmlFor="isRequired">
                        {item.value}
                      </label>
                    </div>
                  )
                })}
    
              </div>
            </div>
          )
        } else if (field.toolType === "TEXT_INPUT") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p>{field.title}</p>
                <input
    
    
                  className="form-control"
                  type="text"
                  id="isRequired"
                />
              </div>
            </div>
          )
        } else if (field.toolType === "IMAGE_INPUT") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p>{field.title}</p>
    
              </div>
            </div>
          )
        } else if (field.toolType === "VIDEO_INPUT") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p>{field.title}</p>
    
              </div>
            </div>
          )
        } else if (field.toolType === "AUDIO_INPUT") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p>{field.title}</p>
    
              </div>
            </div>
          )
        } else if (field.toolType === "OPTION_INPUT") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p
                  dangerouslySetInnerHTML={{ __html: field.title }}
                />
                {field.radios.map(item => {
                  return (
                    <div className="form-check">
                      <input
    
    
                        className="form-check-input"
                        type="checkbox"
                        id="isRequired"
                      />
                      <label className="form-check-label" htmlFor="isRequired">
                        {item.value}
                      </label>
                    </div>
                  )
                })}
    
              </div>
            </div>
          )
        } else if (field.toolType === "SIGN_INPUT") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p>{field.title}</p>
    
              </div>
            </div>
          )
        } else if (field.toolType === "CHECK_INPUT") {
          return (
            <div className='card mb-3'>
              <div className='card-body boxShadow'>
                <p
                  dangerouslySetInnerHTML={{ __html: field.title }}
                />
                {field.radios.map(item => {
                  return (
                    <div className="form-check">
                      <input
    
    
                        className="form-check-input"
                        type="checkbox"
                        id="isRequired"
                      />
                      <label className="form-check-label" htmlFor="isRequired">
                        {item.value}
                      </label>
                    </div>
                  )
                })}
    
              </div>
            </div>
          )
        } else if (field.toolType === "MULTIMEDIA") {
          return (
            <></>
          )
        }
      }
    return (
        <>
            <Row>
                {
                    category.map((item) => {
                        return (
                            <Col xs={3} sm={3} md={3}>
                                <div className='boxShadow' onClick={() => openDoc(item)}>
                                    <div className='card-head  text-center' style={{ background: '#00000047' }}>
                                        {/* <i class="fa fa-file-text fa-4x" style={{ color: '#363F72' }} aria-hidden="true"></i> */}
                                        <img src={require('../../assets/images/doc.jpg')} style={{ maxWidth: '100%', height: 170, width: '100%' }} />
                                    </div>
                                    <div className='card-body bg-black p-3' style={{ color: '#f5f5f5', background: '#000000b0' }}>
                                        <h5>Recent additions</h5>
                                        <p>{displayDate(item)}</p>

                                    </div>

                                    <div className='card-foot p-3' style={{ paddingTop: '20px ' }}>
                                        <h3>{item.name}</h3>
                                        <p style={{ color: '#363F72' }}>Check out all {item.name} <i class=" mdi mdi-arrow-top-right" style={{ fontSize: 18, position: 'relative', bottom: -4 }}></i></p>
                                    </div>
                                </div>
                            </Col>
                        )
                    })
                }

            </Row>
            <Modal
                show={cateShow}
                size='xs'
                onHide={() => setCateShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header closeButton>
                    <h3>{selectDocument.length != 0 ? selectDocument[0].cate.name : ''}</h3>
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-lg-12 ">
                            {selectDocument.map((item) => {
                                return (
                                    <Row className={`p-3 box-doc ${selectId.id === item.id ? 'active' : ''}`} onClick={() => openOneDoc(item)}>
                                        <Col xs={1}>
                                            <div className='round-icon' style={{ background: "#E299311A" }}>
                                                <i class="fa fa-file-text " style={{ color: '#E29931' }} aria-hidden="true"></i>
                                            </div>

                                        </Col>
                                        <Col xs={11} className='d-flex align-items-center'>
                                            <h4>{item.name}</h4>
                                        </Col>

                                    </Row>

                                )
                            })}

                        </div>
                    </div>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    <Button variant="primary" onClick={() => setSmDocShow(true)}>Open Document</Button>
                    <Button variant="light" onClick={() => setCateShow(false)}>Cancel</Button>



                </Modal.Footer>
            </Modal>

            <Modal
                show={smDocShow}
                onHide={() => setSmDocShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Document
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className={`animate-col-md-12`}>
                            {selectId !== '' ?
                                selectId.value ?
                                    JSON.parse(selectId.value).map((field, index) => {
                                        return renderField(field, index)
                                    })
                                    : '' : ''
                            }
                        </div>
                    </div>

                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => setSmDocShow(false)}>Cancel</Button>



                </Modal.Footer>
            </Modal>

        </>
    )
}

export default MyDocument