import React, { useEffect, useState } from "react";
import moment from 'moment';
import TakeActionModal from "../pages/TakeActionModal";
import VerifyActionModal from "../pages/VerifyActionModal";
// import IncidentInvestigationViewModal from "../pages/IncidentInvestigationViewModal";
import { REPORT_INCIDENT_URL_WITH_ID } from "../constants";
import API from "../services/API";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import OBSModal from "./OBSModal";

const Actions = ({ action, applicationType, setRendered }) => {
    console.log(action)
    const currentDate = moment();
    const dueDateMoment = moment(new Date(), 'DD/MM/YYYY');
    const isPastDue = dueDateMoment.isBefore(currentDate);
    const isToday = dueDateMoment.isSame(currentDate, 'day');
    const [actionOne, setActionOne] = useState([])
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        description: { value: null, matchMode: FilterMatchMode.IN },
        dueDate: { value: null, matchMode: FilterMatchMode.IN },
    });



    const renderActionTxt = (actionType) => {
        let actionTxt = ''
        if (actionType === 'dcso_approver') actionTxt = 'Review, Isolate & Approve'
        else if (actionType === 'assessor') actionTxt = 'Review & Assess'
        else if (actionType === 'normalization') actionTxt = 'Normalize'
        else if (actionType === 'approver') actionTxt = 'Review and Approve'
        else if (actionType === 'take_actions_control') actionTxt = 'Implement Control Measures'
        else if (actionType === 'take_actions_ra') actionTxt = 'Review and Update RA / SWP'
        else if (actionType === 'verify_actions') actionTxt = 'Verify Implementation'
        else if (actionType === 'retake_actions') actionTxt = 'Retake Actions'
        else if (actionType === 'reviewer') actionTxt = 'OBS - Verify Actions'
        else if (actionType === 'action_owner') actionTxt = 'OBS - Take Actions'
        else if (actionType === 'hse_action') actionTxt = 'OBS - Assign Actions'
        else if (actionType === 'verify_investigation_actions') actionTxt = 'Approve Investigation'
        else if (actionType === 'take_investigation_actions') actionTxt = 'Take Control Actions'
        else if (actionType === 'ins_take_actions_control') actionTxt = 'Take Actions'
        else if (actionType === 'inspect') actionTxt = 'Take Actions'
        else if (actionType === 'audit') actionTxt = 'Perform Audit'
        else if (actionType === 'audit_take_actions') actionTxt = 'Take Actions'
        else if (actionType === 'audit_verify_actions') actionTxt = 'Verify Actions'
        else if (actionType === 'audit_retake_actions') actionTxt = 'Retake Actions'
        else if (actionType === 'admin_action') actionTxt = "Take Required Immediate Action"
        else if (actionType === 'action_owner_action') actionTxt = "Take Required Action"



        return actionTxt
    }

    let actionTypeText;
    switch (applicationType) {
        case 'Observation':
            actionTypeText = renderActionTxt(action.actionType)
            break;
        case 'INCIDENT':
            actionTypeText = renderActionTxt(action.actionType)
            break;
        case 'PermitToWork':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        case 'Inspection':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        case 'Audit':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        case 'AuditFinding':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        default:
            actionTypeText = '';
    }

    const [showModal, setShowModal] = useState(false)
    const [showVerifyModal, setShowVerifyModal] = useState(false)
    const [showInvestigationModal, setShowInvestigationModal] = useState(false)
    const handleAction = (action) => {
        setActionOne(action)

        // if (action.actionType === 'verify_investigation_actions') {
        //     getReportIncident(action.objectId)
        // }
        // else if (['verify_actions', 'reviewer', 'audit_verify_actions', 'hse_action'].includes(action.actionType)) {
        //     setShowVerifyModal(true)
        // } else {
        setShowModal(true)
        // }

    }

    const [incidentData, setIncidentData] = useState({});
    const getReportIncident = async (id) => {

        const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix', 'incidentCircumstanceCategory', 'incidentCircumstanceDescription', 'incidentCircumstanceType', 'lighting', 'riskCategory', 'surfaceCondition', 'surfaceType', 'workActivity'] }

        const url = `${REPORT_INCIDENT_URL_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;

            setIncidentData(data)

            setShowInvestigationModal(true)
        }
    }

    useEffect(() => {
        // setRendered(Math.random())
    }, [showModal, showVerifyModal, showInvestigationModal])

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-between align-items-center'>
                {applicationType === 'Observation' ?

                    <h5 className="m-0"> A listing of all observation-related actions due from you for the selected location(s) and time frame.</h5>
                    : ''}

                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} />
                </span>
            </div>
        );
    };

    const header = renderHeader();
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };
    const maskIdBodyTemplate = (row) => {
        if (applicationType === 'INCIDENT') {

            if (row.applicationDetails) {
                return (
                    <div className='maskid' onClick={() => handleAction(row)}> {isPastDue ? <span className='overdue'></span> : isToday ? <span className='pending'></span> : ''} {row.applicationDetails && row.applicationDetails.maskId ? `# ${row.applicationDetails.maskId}` : ''}</div>
                )
            } else {
                return (
                    <div className='maskid' onClick={() => handleAction(row)}>  # {row.maskId} </div>
                )
            }


        } else {
            return (
                <div className='maskid' onClick={() => handleAction(row)}> {isPastDue ? <span className='overdue'></span> : isToday ? <span className='pending'></span> : ''} {row.applicationDetails && row.applicationDetails.maskId ? `# ${row.applicationDetails.maskId}` : ''}</div>
            )
        }
    }
    const actionTextBodyTemplate = (row) => {
        return (
            <div> {renderActionTxt(row.actionType)}</div>
        )

    }
    return (
        <>
            <DataTable value={action} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                <Column field="applicationDetails.maskId" body={maskIdBodyTemplate} header="ID" sortable style={{ width: '15%' }} ></Column>

                <Column field="description" header="Required Action" body={actionTextBodyTemplate} sortable style={{ width: '15%' }} ></Column>

                <Column field="dueDate" header="Due Date" sortable style={{ width: '15%' }} ></Column>

            </DataTable>


            {showModal && <OBSModal data={actionOne} applicationType={applicationType} showModal={showModal} setShowModal={setShowModal} />}

        </>
    );
}

export default Actions;