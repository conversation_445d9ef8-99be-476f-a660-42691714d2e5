import React, { useState, useEffect, useCallback } from 'react';
import API from '../services/API';
import { GENERAL_GROUP_URL } from '../constants';

const FormSection = ({ form, handleEmailChange, handlePercentageChange, index, thirdParty, readOnly, notification }) => {
    return (
        <div className="row mb-3">
            <div className="col-6">
                <div className="form-group d-flex align-items-center mb-0">
                    <select
                        className="form-select"
                        value={form.emails}
                        onChange={(e) => handleEmailChange(e, index)}
                        disabled={readOnly}
                    >
                        <option value={''}>Choose Third Party</option>
                        {thirdParty?.map((name, nameIndex) => (
                            <option key={nameIndex} value={name.id}>{name.name}</option>
                        ))}
                    </select>
                </div>
            </div>
           {!notification && <div className="col-6">
                <div className="form-group d-flex align-items-center mb-0">
                    <select
                        className="form-select"
                        value={form.percentage}
                        onChange={(e) => handlePercentageChange(e, index)}
                        disabled={readOnly}
                    >
                        <option value={''}>Select Percentage</option>
                        <option value={'25'}>25 %</option>
                        <option value={'50'}>50 %</option>
                        <option value={'75'}>75 %</option>
                        <option value={'100'}>100 %</option>
                        <option value={'125'}>125 %</option>
                        <option value={'150'}>150 %</option>
                    </select>
                </div>
            </div> }
        </div>
    );
};

const ThirdPartyForm = ({ readOnly, updateForms, values, notification = false }) => {
    const [forms, setForms] = useState([{ emails: '', percentage: '25' }]);
    const [generalGroups, setGeneralGroups] = useState([]);
    const [thirdParty, setThirdParty] = useState(null);

    useEffect(() => {
        if (values) {
            setForms(values)
        }
        const getGeneralGroups = async () => {
            try {
                const response = await API.get(GENERAL_GROUP_URL);
                if (response.status === 200) {
                    setGeneralGroups(response.data);
                    setThirdParty(response.data.filter(i => i.group === 'Third Party'));
                }
            } catch (error) {
                console.error("Failed to fetch data", error);
            }
        };
        getGeneralGroups();
    }, []);

    const handleAddForm = () => {
        setForms([...forms, { emails: '', percentage: '25' }]);
    };

    const handleRemoveForm = useCallback((index) => {
        setForms(prevForms => prevForms.filter((_, idx) => idx !== index));
    }, []);

    const handleEmailChange = useCallback((e, index) => {
        setForms(prevForms => {
            const newForms = [...prevForms];
            newForms[index].emails = e.target.value;
            return newForms;
        });
    }, []);

    const handlePercentageChange = useCallback((e, index) => {
        setForms(prevForms => {
            const newForms = [...prevForms];
            newForms[index].percentage = e.target.value;
            return newForms;
        });
    }, []);

    useEffect(() => { updateForms(forms) }, [forms])

    return (
        <div>
            {forms.map((form, index) => (
                <div key={index} className="mb-3">
                    <FormSection
                        form={form}
                        handleEmailChange={handleEmailChange}
                        handlePercentageChange={handlePercentageChange}
                        index={index}
                        readOnly={readOnly}
                        thirdParty={thirdParty}
                        notification={notification}
                    />
                    {(forms.length > 1 && !readOnly) && (
                        <button type="button" className="btn btn-danger" onClick={() => handleRemoveForm(index)}>Remove</button>
                    )}
                </div>
            ))}
            {!readOnly && <button type="button" className="btn btn-primary mb-5" onClick={handleAddForm}>Add More</button>}
        </div>
    );
};

export default ThirdPartyForm;
