import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { ACTION_OWNER_LIST, API_URL, GMS1_URL, OBS_ACTION_ARCHIVE, DYNAMIC_TITLES_URL, OBS_ACTION_ASSIGN, OBS_REVIEWER_ASSIGN, STATIC_URL, ACTION_REVIEWER_LIST, GET_USER_BY_ROLES_URL, GC_ACTION_ASSIGN, GC_ACTION_OWNER_SUMBIT } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import { RadioButton } from "primereact/radiobutton";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import { useSelector } from "react-redux";
import { useHistory } from 'react-router-dom';
import moment from "moment";
import Swal from "sweetalert2";
import Select from "react-select";
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});
const OBSModal = ({ data, applicationType, showModal, setShowModal }) => {
    console.log(data)
    const location = useHistory()
    const user = useSelector((state) => state.login.user)
    const more = {}
    const workActivityDepartmentId = data.applicationDetails.workActivityDepartmentId ? data.applicationDetails.workActivityDepartmentId : ''
    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])
    const [reviewer, setReviewer] = useState([])
    const [contractor, setContarctor] = useState([])
    const [severity, setSeverity] = useState('')
    const [assign, setAssign] = useState('No')
    const [comments, setComments] = useState('')
    const [commentsF, setCommentsF] = useState('')
    const [actionTBT, setActionTBT] = useState('')
    const [dueDate, setDueDate] = useState(null)
    const [ownerType, setOwnerType] = useState('')
    const [ownerName, setOwnerName] = useState('')
    const [ownerId, setOwnerId] = useState('')
    const [reviewerName, setReviewerName] = useState('')
    const [reviewerId, setReviewerId] = useState('')
    const userId = useRef();
    const departId = useRef();
    const activityId = useRef();
    const [activity, setActivity] = useState([])
    const [departActivity, setDepartActivity] = useState([])
    const [depart, setDepart] = useState([])
    const actionTaken = useRef();
    const [selectedDepart, setSelectedDepart] = useState({ name: '', id: '' })
    const [selectedActivity, setSelectedDActivity] = useState({ name: '', id: '' })
    const [title, setTitle] = useState([])
    const [selectedOption, setSelectedOption] = useState(null);

    const [immediateActionTaken, setImmediateActionTaken] = useState('')
    const [immediateActionDate, setImmediateActionDate] = useState(null)

    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {

        if (applicationType === 'GoodCatch') {
            getActionOwners();
            // getContractorActionOwners()
            getActionReviewer()
            getWorkActivity()
            getDynamicTitle()

        }

        setSeverity(data.applicationDetails.severity)



    }, [])

    const getDynamicTitle = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);
        setTitle(response.data)

        // setUsers(response.data)

    }
    const getWorkActivity = async () => {
        const uriString = { include: ["ghsTwos"] };

        const url = `${GMS1_URL}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {

            // setDepartActivity(response.data)
            const depart = response.data.map(item => {
                return { value: item.id, label: item.name }
            })
            setDepart(depart)
            setDepartActivity(response.data)
            handleDepartChange('auto', response.data)
        }
    };


    // useEffect(() => {
    //     handleDepartChange('auto', departActivity)
    // }, [departActivity])

    const getActionOwners = async () => {
        const response = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'good-catch-action-owner' });
        if (response.status === 200) {

            let data = [];
            response.data.map((item) => {

                // if (item.id !== user.id) {
                data.push({ label: item.firstName, value: item.id });
                // }
            });
            setUsers(data)
        }
    }
    const getActionReviewer = async () => {
        const response = await API.post(ACTION_REVIEWER_LIST, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setReviewer(response.data)
        }
    }
    // const getContractorActionOwners = async () => {
    //     const response = await API.get(CONTRACTOR_LIST);
    //     if (response.status === 200) {
    //         setContarctor(response.data)
    //     }
    // }
    const validationCheckReview = () => {
        if (assign == 'No') {
            if (comments === '' || immediateActionDate === '' || immediateActionTaken === '') {
                // setRequiredCheck(true)
                cogoToast.warn('Please fill all the required fields')

            } else {
                // setRequiredCheck(false)
                submitArchiveAction()
            }
        } else {
            if (ownerId === '' || actionTBT === '' || dueDate === null || immediateActionDate === '' || immediateActionTaken === '') {

                cogoToast.warn('Please fill all the required fields')

            } else {
                submitAssignAction()
            }
        }
    }

    const submitArchiveAction = async () => {
        var reqData = {}
        reqData['comments'] = comments

        console.log('Req data', reqData)

        const response = await API.patch(GC_ACTION_ASSIGN(data.objectId, data.id), reqData);
        if (response.status === 204) {
            customSwal2.fire('This Observation has been archived', "", 'success').then((result) => {
                if (result.isConfirmed) {
                    setShowModal(false)
                    location.go(0)
                }
            });

        }
    }

    const validationCheck = () => {

        if (actionTaken.current.value === '' || files.length == 0) {

            cogoToast.warn('Please fill all the required fields')
        } else {

            submitAction()
        }
    }

    const validationCheck2 = (type) => {
        if (commentsF === '') {

            cogoToast.warn('Please fill all the required fields')
        } else {

            submitApproveAction(type)
        }
    }
    const submitApproveAction = async (type) => {

        var moreDetails = Object.assign({}, more)
        moreDetails['actionReviewer'] = user?.firstName
        moreDetails['actionReviewerId'] = user?.id

        var reqData = {}
        reqData['actionType'] = type === 'approve' ? 'approve' : 'reject'
        reqData['comments'] = commentsF
        type === 'return' && (reqData['assignedToId'] = [data.applicationDetails.actionBy.id])
        reqData['dueDate'] = data.dueDate
        reqData['actionTaken'] = data.actionTaken
        reqData['actionToBeTaken'] = data.actionToBeTaken
        reqData['objectId'] = data.objectId
        reqData['description'] = data.description
        reqData['createdDate'] = data.createdDate
        // reqData['remarks'] = JSON.stringify(moreDetails)
        console.log(reqData)

        const response = await API.patch(OBS_REVIEWER_ASSIGN(data.id), reqData);
        if (response.status === 204) {
            customSwal2.fire(type == 'approve' ? `This action has been approved.` : `This action has been returned to the action owner.`, "", 'success').then((result) => {
                if (result.isConfirmed) {
                    setShowModal(false)
                    location.go(0)
                }
            });

        }

    }
    const submitAction = async () => {

        try {

            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {

                const originalNames = fileResponse.data.files.map(file => file.originalname);
                console.log(originalNames)

                console.log('Promise resolved successfully at CWIT');

                

                var reqData = {}
                reqData['actionTaken'] = actionTaken.current.value
                reqData['actionOwnerUploads'] = originalNames
             


                const response = await API.patch(GC_ACTION_OWNER_SUMBIT(data.objectId,data.id), reqData);
                if (response.status === 204) {
                    customSwal2.fire('This action has been submitted !', "", 'success').then((result) => {
                        if (result.isConfirmed) {
                            setShowModal(false)
                            location.go(0)
                        }
                    });

                }

            }
        } catch (e) { console.log(e); setShowModal(false) }

    }

    const submitAssignAction = async () => {
        var reqData = {}

        reqData['actionOwnerId'] = assign === 'Yes' ? ownerId : ''
        reqData['actionToBeTaken'] = actionTBT
        reqData['immediateActionDate'] = moment(immediateActionDate).format("YYYY-MM-DDTHH:mm:ssZ");
        reqData['immediateActionTaken'] = immediateActionTaken

        reqData['dueDate'] = assign === 'Yes' ? moment(dueDate).format('DD-MM-YYYY') : ''


        console.log(reqData)

        const response = await API.patch(GC_ACTION_ASSIGN(data.objectId, data.id), reqData);
        if (response.status === 204) {
            // cogoToast.success('Action Assigned!')
            // setShowModal(false)
            customSwal2.fire('Action Assigned!', "", 'success').then((result) => {
                if (result.isConfirmed) {
                    setShowModal(false)
                    location.go(0)
                }
            });
        }

    }
    const getOwner = (e) => {
        setOwnerId(e.value);
        // setOwnerType(e.selectedOptions[0].getAttribute("data-id"))
        // setOwnerName(e.selectedOptions[0].text)

    }
    const getReviewer = (e) => {
        setReviewerId(e.value);
        setReviewerName(e.selectedOptions[0].text)
    }
    // const handleSubmit = async () => {
    //     try {

    //         const formData = new FormData();
    //         files.forEach((file, index) => {
    //             formData.append('file', file);
    //         });
    //         const token = localStorage.getItem('access_token');
    //         const fileResponse = await axios.post(`${API_URL}/files`, formData, {
    //             headers: {
    //                 'Content-Type': 'multipart/form-data',
    //                 'Authorization': `Bearer ${token}`,
    //             },
    //         });

    //         if (fileResponse.status === 200) {

    //             const originalNames = fileResponse.data.files.map(file => file.originalname);

    //             let url = '';
    //             let actionType = '';
    //             let assignedToId = '';

    //             switch (applicationType) {
    //                 case 'Observation':
    //                     url = OBSERVATION_REVIEWER_SUBMIT_URL(data.id);
    //                     actionType = 'reviewer'
    //                     assignedToId = userId.current.value
    //                     break;

    //             }

    //             const response = await API.patch(url, {
    //                 actionType: actionType,
    //                 comments: data.comments ? data.comments : '',
    //                 actionTaken: actionTaken.current.value,
    //                 assignedToId: assignedToId,
    //                 actionToBeTaken: data.actionToBeTaken,
    //                 objectId: data.objectId,
    //                 description: data.description,
    //                 dueDate: data.dueDate,
    //                 uploads: originalNames,
    //                 createdDate: data.createdDate

    //             })

    //             if (response.status === 204) {
    //                 cogoToast.success('Submitted!')
    //                 setShowModal(false)
    //             }
    //         }
    //     } catch (e) { console.log(e); setShowModal(false) }
    // }
    const handleDepartChange = (option, data1) => {


    }

    const handleChange = (selected) => {
        setSelectedOption(selected);
        getOwner(selected); // Pass selected user to getOwner
    };
    const handleActivityChange = (e) => {
        setSelectedDActivity({ name: e.selectedOptions[0].text, id: e.value })

    }

    const displayTitle = (type) => {

        const locTitle = title.filter(item => item.title === type)

        return locTitle[0].altTitle

    }
    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>
                        {applicationType === 'GoodCatch' && <p className="card-text"> Good Catch -{data.applicationDetails.maskId}</p>}
                    </div>

                </Modal.Header>

                <Modal.Body>


                    {data.applicationDetails && <div className="">
                        <div className="card">

                            <div className="card-body p-0">

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Reporter</p>
                                        <p className="obs-content">
                                            {data.actionSubmittedBy.firstName}
                                        </p>
                                    </div>
                                    {data.applicationDetails.actionOwner &&
                                        <div className="col-md-6">
                                            <p className="obs-title">Admin</p>
                                            <p className="obs-content">
                                                {data.applicationDetails.actionOwner.firstName}
                                            </p>
                                        </div>
                                    }
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Area</p>
                                        <p className="obs-content">
                                            {data.applicationDetails.locationThree
                                                ? data.applicationDetails.locationThree.name
                                                : "Not Available"}
                                        </p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Zone</p>
                                        <p className="obs-content">
                                            {data.applicationDetails.locationFour
                                                ? data.applicationDetails.locationFour.name
                                                : "Not Available"}
                                        </p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Observation</p>
                                        <p className="obs-content">{data.applicationDetails.whatDidYouObserve || "N/A"}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Where Observed</p>
                                        <p className="obs-content">{data.applicationDetails.whereDidYouObserve || "N/A"}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">What Could Have Gone Wrong</p>
                                        <p className="obs-content">{data.applicationDetails.whatCouldHaveGoneWrong || "N/A"}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Preventive Action</p>
                                        <p className="obs-content">{data.applicationDetails.preventiveAction || "N/A"}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Work Activity</p>
                                        <p className="obs-content">{data.applicationDetails.workActivity || "N/A"}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Status</p>
                                        <p className="obs-content">{data.applicationDetails.status || "N/A"}</p>
                                    </div>
                                </div>





                                <div className="mb-3 mt-3">
                                    {data.actionType === 'hse_action' ?
                                        <h4>Review the observation and assign actions if necessary or achive</h4>
                                        : data.actionType === 'action_owner' ?
                                            <h4>Take action on ther observation assigned and upload necessary evidence</h4>
                                            : data.actionType === 'reviewer' ? <h4>Review the actions taken by the action assignee.</h4> : ''}
                                </div>
                                {/* <h5>{data.applicationDetails.locationThree.name + ' > '} {data.applicationDetails.locationFour.name} </h5>
                                <div className="mb-3 mt-3">
                                    <h5>Reported By : {data.actionSubmittedBy.firstName}</h5>
                                </div>
                                <div className="">
                                    <h6>Description of the At-risk observation</h6>
                                    <p>{data.description}</p>
                                </div> */}
                                {
                                    (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && <div className="mb-3">
                                        <label className="form-label">Uploads</label>
                                        <div className="border p-3 row">
                                            {
                                                (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && data.applicationDetails.uploads.map(i => (
                                                    <div className="col-md-3">
                                                        <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                    </div>

                                                ))
                                            }

                                        </div>
                                    </div>
                                }

                                {data.actionType === 'admin_action' && <>
                                    <div className="mb-3">
                                        <label className="form-label required">Immediate Action Taken </label>
                                        <textarea className="form-control" rows="3" value={immediateActionTaken} onChange={(e) => setImmediateActionTaken(e.target.value)}></textarea>
                                    </div>

                                    <div className="mb-3">
                                        <label className="form-label required">Immediate Action Taken Date </label>
                                        <DatePicker
                                            selected={immediateActionDate}
                                            onChange={(e) => setImmediateActionDate(e)}
                                            className="form-control"
                                            dateFormat={'dd-MM-yyyy'}
                                            minDate={new Date()}
                                        />
                                    </div>


                                </>}


                                {data.actionType === 'admin_action' && <>


                                    <div className="mb-3">
                                        <h6 className="required">Actions to be taken</h6>


                                        <div className="d-flex align-items-center">
                                            <div className="flex align-items-center m-3">
                                                <RadioButton inputId="ingredient12" name="pizza1" value="Yes" onChange={(e) => setAssign(e.value)} checked={assign === 'Yes'} />
                                                <label htmlFor="ingredient12" className="ms-2">Yes</label>
                                            </div>
                                            <div className="flex align-items-center m-3">
                                                <RadioButton inputId="ingredient22" name="pizza1" value="No" onChange={(e) => setAssign(e.value)} checked={assign === 'No'} />
                                                <label htmlFor="ingredient22" className="ms-2">No</label>
                                            </div>

                                        </div>
                                    </div>
                                </>}

                                {data.actionType === 'admin_action' && <>
                                    {assign === 'Yes' ? <>

                                        <div className="mb-3">
                                            <label className="form-label required"> Action to be Taken </label>
                                            <textarea className="form-control" rows="3" value={actionTBT} onChange={(e) => setActionTBT(e.target.value)}></textarea>
                                        </div>


                                        <div className="mb-3">
                                            <label className="form-label required">Action Assign to</label>
                                            <Select
                                                options={users}
                                                placeholder="Select User"
                                                onChange={handleChange}
                                                value={selectedOption}
                                                isClearable
                                            />
                                        </div>

                                        <div className="mb-3">
                                            <label className="form-label required">Due Date (DD-MM-YYYY)</label>
                                            <DatePicker
                                                selected={dueDate}
                                                onChange={(e) => setDueDate(e)}
                                                className="form-control"
                                                dateFormat={'dd-MM-yyyy'}
                                                minDate={new Date()}
                                            />
                                        </div>

                                    </> :

                                        <div className="mb-3">
                                            <label className="form-label required">Reasons for archiving without actions</label>
                                            <textarea className="form-control" rows="3" value={comments} onChange={(e) => setComments(e.value)}></textarea>
                                        </div>
                                    }
                                </>}

                                {data.actionType === 'action_owner_action' &&
                                    <>

                                        <div className="col-md-12">
                                            <p className="obs-title">Immediate Action Taken</p>
                                            <p className="obs-content">{data.applicationDetails.immediateActionTaken || "N/A"}</p>
                                        </div>
                                        <div className="col-md-12">
                                            <p className="obs-title">Immediate Action Date</p>
                                            <p className="obs-content">{moment(data.applicationDetails.immediateActionDate).format('DD-MM-YYYY') || "N/A"}</p>
                                        </div>
                                        <div className="row mt-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Action to be taken</p>
                                                <p className="obs-content">{data.applicationDetails.actionToBeTaken}</p>

                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title">dueDate</p>
                                                <p className="obs-content">{data.applicationDetails.dueDate}</p>

                                            </div>
                                        </div>

                                        <div className="mb-3 mt-3">
                                            <label className="form-label required">Action taken</label>
                                            <textarea ref={actionTaken} className="form-control" rows="3"></textarea>
                                        </div>
                                        <div className="mb-3">
                                            <label className="form-label required">Upload evidence</label>
                                            <DropzoneArea
                                                acceptedFiles={[

                                                    'image/jpeg',
                                                    'image/png'

                                                ]}
                                                dropzoneText={"Drag and Drop Evidence Images"}
                                                filesLimit={5}
                                                maxFileSize={104857600}

                                                onChange={handleFileChange}

                                            />
                                        </div>
                                        {/* <div className="mb-3">
                                            <label className="form-label required">Action Reviewed By</label>
                                            <select className="form-select" onChange={(e) => getReviewer(e.target)}>
                                                <option>Select</option>
                                              
                                                {
                                                    reviewer.map(u => (
                                                        <option data-id={'user'} key={u.id} value={u.id}>{u.firstName}</option>
                                                    ))
                                                }
                                            </select>
                                        </div> */}
                                    </>
                                }
                                {data.actionType === 'reviewer' &&
                                    <>
                                        <div className="mb-3">
                                            <label className="form-label">Action to be taken</label>
                                            <p>{data.actionToBeTaken}</p>
                                        </div>
                                        <div className="mb-3">
                                            <label className="form-label">Action taken</label>
                                            <p>{data.actionTaken}</p>
                                            <p>{more?.actionTimestamp}</p>
                                            <p>{data.applicationDetails.actionBy.firstName}</p>
                                        </div>
                                        {
                                            (data.uploads && data.uploads.length > 0) && <div className="mb-3">
                                                <label className="form-label">Evidence Uploads</label>
                                                <div className="border p-3 row">
                                                    {
                                                        (data.uploads && data.uploads.length > 0) && data.uploads.map(i => (
                                                            <div className="col-md-3">
                                                                <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                            </div>

                                                        ))
                                                    }

                                                </div>
                                            </div>
                                        }


                                        <div className="mb-3">
                                            <label className="form-label required">Comments</label>
                                            <textarea className="form-control" rows="3" defaultValue={commentsF} onChange={(e) => setCommentsF(e.target.value)}></textarea>
                                        </div>
                                    </>

                                }





                                {/* <div className="mb-3">
                                    <label className="form-label">Action taken</label>
                                    <textarea ref={actionTaken} className="form-control" rows="3"></textarea>
                                </div> */}

                                {/* <div className="mb-3">
                                    <label className="form-label">Upload evidence</label>
                                    <DropzoneArea
                                        acceptedFiles={[

                                            'image/jpeg',
                                            'image/png'

                                        ]}
                                        dropzoneText={"Drag and Drop Evidence Images"}
                                        filesLimit={5}
                                        maxFileSize={104857600}

                                        onChange={handleFileChange}

                                    />
                                </div> */}

                            </div>
                        </div>
                    </div>}



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    {data.actionType === 'admin_action' &&
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={validationCheckReview}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            {assign == 'Yes' ? 'Assign' : 'Submit'}
                        </Button>
                    }

                    {data.actionType === 'action_owner_action' &&
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={validationCheck}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            Submit
                        </Button>
                    }

                    {data.actionType === 'reviewer' && <>
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={() => validationCheck2('return')}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            Return
                        </Button>
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={() => validationCheck2('approve')}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            Approve
                        </Button>
                    </>}

                    <Button
                        variant="light"
                        onClick={() => setShowModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>
        </>
    )
}

export default OBSModal