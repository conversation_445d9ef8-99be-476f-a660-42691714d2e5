import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { ACTION_OWNER_LIST, API_URL, GMS1_URL, TIER2_TIER3_URL, OBS_ACTION_ARCHIVE, OBS_ACTION_ASSIGN, OBS_REVIEWER_ASSIGN, STATIC_URL, ACTION_REVIEWER_LIST, LOCATION_THREE } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import { RadioButton } from "primereact/radiobutton";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import { useSelector } from "react-redux";
import { useHistory } from 'react-router-dom';
import moment from "moment";
import Swal from "sweetalert2";
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});
const OBSModal = ({ data, applicationType, showModal, setShowModal }) => {
    console.log(data)
    const location = useHistory()
    const user = useSelector((state) => state.login.user)

    const [more, setMore] = useState(data.applicationDetails.remarks != '' ? JSON.parse(data.applicationDetails.remarks) : {})

    const defaultOption = more?.unsafeCondition
        ? "unsafeCondition"
        : more?.unsafeAct
            ? "unsafeAct"
            : "";
    const [selectedOption, setSelectedOption] = useState(defaultOption);
    const workActivityDepartmentId = data.applicationDetails.workActivityDepartmentId ? data.applicationDetails.workActivityDepartmentId : ''
    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])
    const [reviewer, setReviewer] = useState([])
    const [contractor, setContarctor] = useState([])
    const [severity, setSeverity] = useState('')
    const [assign, setAssign] = useState('Yes')
    const [comments, setComments] = useState('')
    const [commentsF, setCommentsF] = useState('')
    const [actionTBT, setActionTBT] = useState('')
    const [dueDate, setDueDate] = useState(null)
    const [ownerType, setOwnerType] = useState('')
    const [ownerName, setOwnerName] = useState('')
    const [ownerId, setOwnerId] = useState('')
    const [reviewerName, setReviewerName] = useState('')
    const [reviewerId, setReviewerId] = useState('')
    const userId = useRef();
    const departId = useRef();
    const activityId = useRef();
    const zoneId = useRef();
    const areaId = useRef();
    const [activity, setActivity] = useState([])
    const [departActivity, setDepartActivity] = useState([])
    const [depart, setDepart] = useState([])
    const actionTaken = useRef();
    const [selectedDepart, setSelectedDepart] = useState({ name: '', id: '' })
    const [selectedActivity, setSelectedDActivity] = useState({ name: '', id: '' })
    const [locationThree, setLocationThree] = useState([])
    const [three, setThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    const [selectLocationFour, setSelectLocationFour] = useState('')
    const [selectLocationThree, setSelectLocationThree] = useState('')
    const [description, setDescription] = useState('')


    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {

        if (applicationType === 'Observation') {
            getActionOwners();
            // getContractorActionOwners()
            getActionReviewer()
            getWorkActivity()
            getThreeLocation()
        }

        setSeverity(data.applicationDetails.severity)
        setActionTBT(data.actionToBeTaken)
        setSelectLocationThree(data.applicationDetails.locationThreeId)
        setDescription(data.applicationDetails.description)


    }, [])

    useEffect(() => {
        getLocationFour()

    }, [selectLocationThree])

    const getThreeLocation = async () => {
        const uriString = { include: ["locationFours"] };
        const url = `${LOCATION_THREE}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const depart = response.data.map(item => {
                return { value: item.id, label: item.name }
            })
            setThree(response.data)
            setLocationThree(depart)
            handleLocationChange('auto', response.data)
        }
    }

    const getLocationFour = async () => {
        // const response = await API.get(TIER2_TIER3_URL(selectLocationThree));
        // if (response.status === 200) {
        //     const depart = response.data.map(item => {
        //         return { value: item.id, label: item.name }
        //     })

        //     setLocationFour(depart)

        //     setSelectLocationFour(data.applicationDetails.locationFourId)
        // }
    }

    const getWorkActivity = async () => {
        const uriString = { include: ["ghsTwos"] };

        const url = `${GMS1_URL}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {

            // setDepartActivity(response.data)
            const depart = response.data.map(item => {
                return { value: item.id, label: item.name }
            })
            setDepart(depart)
            setDepartActivity(response.data)
            handleDepartChange('auto', response.data)
        }
    };


    // useEffect(() => {
    //     handleDepartChange('auto', departActivity)
    // }, [departActivity])

    const getActionOwners = async () => {
        const response = await API.post(ACTION_OWNER_LIST, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }
    const getActionReviewer = async () => {
        const response = await API.post(ACTION_REVIEWER_LIST, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setReviewer(response.data)
        }
    }
    // const getContractorActionOwners = async () => {
    //     const response = await API.get(CONTRACTOR_LIST);
    //     if (response.status === 200) {
    //         setContarctor(response.data)
    //     }
    // }
    const validationCheckReview = () => {
        if (assign == 'No') {
            if (comments === '') {
                // setRequiredCheck(true)
                cogoToast.warn('Please fill all the required fields')

            } else {
                // setRequiredCheck(false)
                submitArchiveAction()
            }
        } else {
            if (userId.current.value === '' || actionTBT === '' || dueDate === null) {

                cogoToast.warn('Please fill all the required fields')

            } else {
                submitAssignAction()
            }
        }
    }

    const submitArchiveAction = async () => {
        var moreDetails = Object.assign({}, more)
        moreDetails['obsReviewer'] = user?.firstName
        moreDetails['obsReviewerId'] = user?.id
        moreDetails['archiveComments'] = comments

        var reqData = {}
        reqData['severity'] = severity
        reqData['remarks'] = JSON.stringify(moreDetails)

        console.log('Req data', reqData)

        const response = await API.patch(OBS_ACTION_ARCHIVE(data.objectId, data.id), reqData);
        if (response.status === 204) {
            customSwal2.fire('This Observation has been archived', "", 'success').then((result) => {
                if (result.isConfirmed) {
                    setShowModal(false)
                    location.go(0)
                }
            });

        }
    }

    const validationCheck = () => {

        if (actionTaken.current.value === '' || files.length == 0) {

            cogoToast.warn('Please fill all the required fields')
        } else {

            submitAction()
        }
    }

    const validationCheck2 = (type) => {
        if (commentsF === '') {

            cogoToast.warn('Please fill all the required fields')
        } else {

            submitApproveAction(type)
        }
    }
    const submitApproveAction = async (type) => {

        var moreDetails = Object.assign({}, more)
        moreDetails['actionReviewer'] = user?.firstName
        moreDetails['actionReviewerId'] = user?.id

        var reqData = {}
        reqData['actionType'] = type === 'approve' ? 'approve' : 'reject'
        reqData['comments'] = commentsF
        type === 'return' && (reqData['assignedToId'] = [data.applicationDetails.actionBy.id])
        reqData['dueDate'] = data.dueDate
        reqData['actionTaken'] = data.actionTaken
        reqData['actionToBeTaken'] = data.actionToBeTaken
        reqData['objectId'] = data.objectId
        reqData['description'] = data.description
        reqData['createdDate'] = data.createdDate
        // reqData['remarks'] = JSON.stringify(moreDetails)
        console.log(reqData)

        const response = await API.patch(OBS_REVIEWER_ASSIGN(data.id), reqData);
        if (response.status === 204) {
            customSwal2.fire(type == 'approve' ? `This action has been approved.` : `This action has been returned to the action owner.`, "", 'success').then((result) => {
                if (result.isConfirmed) {
                    setShowModal(false)
                    location.go(0)
                }
            });

        }

    }
    const submitAction = async () => {

        try {

            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {

                const originalNames = fileResponse.data.files.map(file => file.originalname);

                console.log('Promise resolved successfully at CWIT');

                // var moreDetails = Object.assign({}, more)
                // moreDetails['actionOwner'] = user?.firstName
                // moreDetails['actionTimestamp'] = moment(new Date()).format('DD-MM-YYYY HH:mm A')

                var reqData = {}
                reqData['actionType'] = 'reviewer'
                reqData['actionTaken'] = actionTaken.current.value
                reqData['assignedToId'] = [reviewerId]
                reqData['comments'] = data.comments || ''
                reqData['actionToBeTaken'] = data.applicationDetails.actionToBeTaken || ''
                reqData['objectId'] = data.objectId
                reqData['description'] = data.description
                reqData['dueDate'] = data.dueDate
                reqData['uploads'] = originalNames
                reqData['createdDate'] = data.createdDate


                const response = await API.patch(OBS_REVIEWER_ASSIGN(data.id), reqData);
                if (response.status === 204) {
                    customSwal2.fire('This action has been submitted for review.!', "", 'success').then((result) => {
                        if (result.isConfirmed) {
                            setShowModal(false)
                            location.go(0)
                        }
                    });

                }

            }
        } catch (e) { console.log(e); setShowModal(false) }

    }

    const submitAssignAction = async () => {
        var reqData = {}
        var moreDetails = Object.assign({}, more)

        moreDetails['department'] = departId.current.selectedOptions[0].text
        moreDetails['workActivity'] = activityId.current.value === '' ? '' : activityId.current.selectedOptions[0].text

        moreDetails['actionOwner'] = ownerName
        // moreDetails['obsReviewer'] = user?.firstName
        // moreDetails['obsReviewerId'] = user?.id
        reqData['description'] =description
        reqData['locationThreeId'] =areaId.current.value
        reqData['locationFourId'] =zoneId.current.value

        reqData['workActivityDepartmentId'] = departId.current.value
        reqData['workActivityId'] = activityId.current.value === '' ? '' : activityId.current.value
        reqData['actionOwnerId'] = ownerId
        reqData['actionToBeTaken'] = actionTBT
        reqData['severity'] = severity
        reqData['dueDate'] = moment(dueDate).format('DD-MM-YYYY')
        reqData['remarks'] = JSON.stringify(moreDetails)

        console.log(reqData)

        const response = await API.patch(OBS_ACTION_ASSIGN(data.objectId, data.id), reqData);
        if (response.status === 204) {
            // cogoToast.success('Action Assigned!')
            // setShowModal(false)
            customSwal2.fire('Action Assigned!', "", 'success').then((result) => {
                if (result.isConfirmed) {
                    setShowModal(false)
                    location.go(0)
                }
            });
        }

    }
    const getOwner = (e) => {
        setOwnerId(e.value);
        setOwnerType(e.selectedOptions[0].getAttribute("data-id"))
        setOwnerName(e.selectedOptions[0].text)

    }
    const getReviewer = (e) => {
        setReviewerId(e.value);
        setReviewerName(e.selectedOptions[0].text)
    }
    // const handleSubmit = async () => {
    //     try {

    //         const formData = new FormData();
    //         files.forEach((file, index) => {
    //             formData.append('file', file);
    //         });
    //         const token = localStorage.getItem('access_token');
    //         const fileResponse = await axios.post(`${API_URL}/files`, formData, {
    //             headers: {
    //                 'Content-Type': 'multipart/form-data',
    //                 'Authorization': `Bearer ${token}`,
    //             },
    //         });

    //         if (fileResponse.status === 200) {

    //             const originalNames = fileResponse.data.files.map(file => file.originalname);

    //             let url = '';
    //             let actionType = '';
    //             let assignedToId = '';

    //             switch (applicationType) {
    //                 case 'Observation':
    //                     url = OBSERVATION_REVIEWER_SUBMIT_URL(data.id);
    //                     actionType = 'reviewer'
    //                     assignedToId = userId.current.value
    //                     break;

    //             }

    //             const response = await API.patch(url, {
    //                 actionType: actionType,
    //                 comments: data.comments ? data.comments : '',
    //                 actionTaken: actionTaken.current.value,
    //                 assignedToId: assignedToId,
    //                 actionToBeTaken: data.actionToBeTaken,
    //                 objectId: data.objectId,
    //                 description: data.description,
    //                 dueDate: data.dueDate,
    //                 uploads: originalNames,
    //                 createdDate: data.createdDate

    //             })

    //             if (response.status === 204) {
    //                 cogoToast.success('Submitted!')
    //                 setShowModal(false)
    //             }
    //         }
    //     } catch (e) { console.log(e); setShowModal(false) }
    // }
    const handleDepartChange = (option, data1) => {
        if (option === 'auto') {
            const active = data1.find(item => item.id === workActivityDepartmentId);
            console.log(active)

            const activity1 = active.ghsTwos.map(item => {
                return { label: item.name, value: item.id }
            })

            setActivity(activity1)
        } else {
            setSelectedDepart({ name: option.selectedOptions[0].text, id: option.value })
            setActivity([])
            const active = departActivity.filter(item => item.id === option.value);

            const activity1 = active[0].ghsTwos.map(item => {
                return { label: item.name, value: item.id }
            })

            setActivity(activity1)
        }

    }

    const handleLocationChange = (option, data1) => {
        if (option === 'auto') {
            const active = data1.find(item => item.id === data.applicationDetails.locationThreeId);
            console.log(active)

            const activity1 = active.locationFours.map(item => {
                return { label: item.name, value: item.id }
            })

            setLocationFour(activity1)
        } else {

            console.log(locationThree)
            // setSelectedDepart({ name: option.selectedOptions[0].text, id: option.value })
            setSelectLocationThree(option.value)
            setLocationFour([])
            const active = three.filter(item => item.id === option.value);
            console.log(active)

            const activity1 = active[0].locationFours.map(item => {
                return { label: item.name, value: item.id }
            })

            setLocationFour(activity1)
        }

    }

    const handleSelectionChange = (newOption) => {
        setSelectedOption(newOption);

        // Update the `more` object dynamically
        setMore((prevMore) => ({
            ...prevMore,
            unsafeCondition: newOption === "unsafeCondition",
            unsafeAct: newOption === "unsafeAct",
        }));
    };
    const handleActivityChange = (e) => {
        setSelectedDActivity({ name: e.selectedOptions[0].text, id: e.value })

    }
    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>
                        {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span> - {data.applicationDetails.maskId}</p>}
                    </div>

                </Modal.Header>

                <Modal.Body>


                    {data.applicationDetails && <div className="">
                        <div className="card">

                            <div className="card-body p-0">
                                {data.actionType === 'hse_action' ? <>
                                    <div className="mb-3">
                                        <label htmlFor="unsafeOption" className="form-label">Select an Option:</label>
                                        <select
                                            id="unsafeOption"
                                            className="form-select"
                                            value={selectedOption}
                                            onChange={(e) => handleSelectionChange(e.target.value)}
                                        >
                                            <option value="">-- Choose an Option --</option>
                                            <option value="unsafeCondition">Unsafe Condition</option>
                                            <option value="unsafeAct">Unsafe Act</option>
                                        </select>
                                    </div>

                                </> : <>

                                    {more?.unsafeCondition && <h4 className="p-2" style={{ backgroundColor: '#be1f24', color: '#fff', display: 'inline-block' }}>{'Unsafe Condition'}</h4>}
                                    {more?.unsafeAct && <h4 className="p-2" style={{ backgroundColor: '#be1f24', color: '#fff', display: 'inline-block' }}>{'Unsafe Act'}</h4>}

                                </>}
                                {(data.actionType == 'action_owner' && data.comments != '') && <>
                                    <h4>Returned comments from reviewer</h4>
                                    <p>{data.comments}</p>

                                </>}



                                <div className="mb-3 mt-3">
                                    {data.actionType === 'hse_action' ?
                                        <h4>Review the observation and assign actions if necessary or achive</h4>
                                        : data.actionType === 'action_owner' ?
                                            <h4>Take action on ther observation assigned and upload necessary evidence</h4>
                                            : data.actionType === 'reviewer' ? <h4>Review the actions taken by the action assignee.</h4> : ''}
                                </div>

                                {data.actionType === 'hse_action' ? <>
                                    <div className="mb-3">
                                        <label className="form-label required">Area</label>
                                        <select className="form-select" ref={areaId} onChange={(e) => handleLocationChange(e.target)} value={selectLocationThree !== '' ? selectLocationThree : data.applicationDetails.locationThreeId}>
                                            <option value={''}>Select</option>
                                            {locationThree.map(item => {
                                                return (
                                                    <option value={item.value}>{item.label}</option>
                                                )
                                            })}
                                        </select>
                                    </div>
                                    <div className="mb-3">
                                        <label className="form-label required">Zone</label>
                                        <select className="form-select" ref={zoneId} onChange={(e) => setSelectLocationFour(e.target.value)} value={selectLocationFour !== '' ? selectLocationFour : data.applicationDetails.locationFourId}>
                                            <option value={''}>Select</option>
                                            {locationFour.map(item => {
                                                return (
                                                    <option value={item.value}>{item.label}</option>
                                                )
                                            })}
                                        </select>
                                    </div>
                                </> :
                                    <h5>{data.applicationDetails.locationThree.name + ' > '} {data.applicationDetails.locationFour.name} </h5>
                                }
                                <div className="mb-3 mt-3">
                                    <h5>Reported By : {data.actionSubmittedBy.firstName}</h5>
                                </div>
                                <div className="">
                                    <h6>Description of the At-risk observation</h6>
                                    {data.actionType === 'hse_action' ? <>
                                        <textarea className="form-control" rows="3" value={description} onChange={(e) => setDescription(e.target.value)}></textarea>
                                    </> :
                                        <p>{data.description}</p>
                                    }
                                </div>
                                {
                                    (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && <div className="mb-3">
                                        <label className="form-label">Uploads</label>
                                        <div className="border p-3 row">
                                            {
                                                (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && data.applicationDetails.uploads.map(i => (
                                                    <div className="col-md-3">
                                                        <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                    </div>

                                                ))
                                            }

                                        </div>
                                    </div>
                                }
                                {data.actionType === 'hse_action' && <>
                                    <div className="mb-3">
                                        <label className="form-label required">Department</label>
                                        <select className="form-select" ref={departId} onChange={(e) => handleDepartChange(e.target)} value={selectedDepart.id !== '' ? selectedDepart.id : data.applicationDetails.workActivityDepartmentId}>
                                            <option value={''}>Select</option>
                                            {depart.map(item => {
                                                return (
                                                    <option value={item.value}>{item.label}</option>
                                                )
                                            })}
                                        </select>
                                    </div>
                                    <div className="mb-3">
                                        <label className="form-label ">Work Activity</label>
                                        <select className="form-select" ref={activityId} onChange={(e) => handleActivityChange(e.target)} value={selectedActivity.id !== '' ? selectedActivity.id : data.applicationDetails.workActivityId
                                        }>
                                            <option value={''}>Select</option>
                                            {activity.map(item => {
                                                return (
                                                    <option value={item.value}>{item.label}</option>
                                                )
                                            })}
                                        </select>
                                    </div>
                                    <div className="mb-3">
                                        <h6 className="required">Severity Level</h6>

                                        <div className="d-flex align-items-center">
                                            <div className="flex align-items-center m-3">
                                                <RadioButton inputId="ingredient1" name="pizza" value="High" onChange={(e) => setSeverity(e.value)} checked={severity === 'High'} />
                                                <label htmlFor="ingredient1" className="ms-2">High</label>
                                            </div>
                                            <div className="flex align-items-center m-3">
                                                <RadioButton inputId="ingredient2" name="pizza" value="Medium" onChange={(e) => setSeverity(e.value)} checked={severity === 'Medium'} />
                                                <label htmlFor="ingredient2" className="ms-2">Medium</label>
                                            </div>
                                            <div className="flex align-items-center m-3">
                                                <RadioButton inputId="ingredient3" name="pizza" value="Low" onChange={(e) => setSeverity(e.value)} checked={severity === 'Low'} />
                                                <label htmlFor="ingredient3" className="ms-2">Low</label>
                                            </div>

                                        </div>
                                    </div>
                                    <div className="mb-3">
                                        <h6 className="required">Actions to be taken</h6>


                                        <div className="d-flex align-items-center">
                                            <div className="flex align-items-center m-3">
                                                <RadioButton inputId="ingredient12" name="pizza1" value="Yes" onChange={(e) => setAssign(e.value)} checked={assign === 'Yes'} />
                                                <label htmlFor="ingredient12" className="ms-2">Yes</label>
                                            </div>
                                            <div className="flex align-items-center m-3">
                                                <RadioButton inputId="ingredient22" name="pizza1" value="No" onChange={(e) => setAssign(e.value)} checked={assign === 'No'} />
                                                <label htmlFor="ingredient22" className="ms-2">No</label>
                                            </div>

                                        </div>
                                    </div>
                                </>}
                                {data.actionType === 'hse_action' && <>
                                    {assign === 'Yes' ? <>

                                        <div className="mb-3">
                                            <label className="form-label required">Actions</label>
                                            <textarea className="form-control" rows="3" value={actionTBT} onChange={(e) => setActionTBT(e.target.value)}></textarea>
                                        </div>


                                        <div className="mb-3">
                                            <label className="form-label required">Action Assign to</label>
                                            <select className="form-select" ref={userId} onChange={(e) => getOwner(e.target)}>
                                                <option>Select</option>
                                                {/* {
                                                    contractor.map(u => (
                                                        <option data-id={'contractor'} key={u.id} value={u.id} style={{ color: 'red' }}>{u.name}</option>
                                                    ))
                                                } */}
                                                {
                                                    users.map(u => (
                                                        <option data-id={'user'} key={u.id} value={u.id}>{u.firstName}</option>
                                                    ))
                                                }
                                            </select>
                                        </div>

                                        <div className="mb-3">
                                            <label className="form-label required">Due Date (DD-MM-YYYY)</label>
                                            <DatePicker
                                                selected={dueDate}
                                                onChange={(e) => setDueDate(e)}
                                                className="form-control"
                                                dateFormat={'dd-MM-yyyy'}
                                                minDate={new Date()}
                                            />
                                        </div>

                                    </> :

                                        <div className="mb-3">
                                            <label className="form-label required">Reasons for archiving without actions</label>
                                            <textarea className="form-control" rows="3" value={comments} onChange={(e) => setComments(e.value)}></textarea>
                                        </div>
                                    }
                                </>}
                                {data.actionType === 'action_owner' &&
                                    <>
                                        <div className="mb-3">
                                            <label className="form-label">Action to be taken</label>
                                            <p>{data.applicationDetails.actionToBeTaken}</p>

                                        </div>
                                        <div className="mb-3">
                                            <label className="form-label required">Action taken</label>
                                            <textarea ref={actionTaken} className="form-control" rows="3"></textarea>
                                        </div>
                                        <div className="mb-3">
                                            <label className="form-label required">Upload evidence</label>
                                            <DropzoneArea
                                                acceptedFiles={[

                                                    'image/jpeg',
                                                    'image/png'

                                                ]}
                                                dropzoneText={"Drag and Drop Evidence Images"}
                                                filesLimit={5}
                                                maxFileSize={104857600}

                                                onChange={handleFileChange}

                                            />
                                        </div>
                                        <div className="mb-3">
                                            <label className="form-label required">Action Reviewed By</label>
                                            <select className="form-select" onChange={(e) => getReviewer(e.target)}>
                                                <option>Select</option>
                                                {/* {
                                                    contractor.map(u => (
                                                        <option data-id={'contractor'} key={u.id} value={u.id} style={{ color: 'red' }}>{u.name}</option>
                                                    ))
                                                } */}
                                                {
                                                    reviewer.map(u => (
                                                        <option data-id={'user'} key={u.id} value={u.id}>{u.firstName}</option>
                                                    ))
                                                }
                                            </select>
                                        </div>
                                    </>
                                }
                                {data.actionType === 'reviewer' &&
                                    <>
                                        <div className="mb-3">
                                            <label className="form-label">Action to be taken</label>
                                            <p>{data.actionToBeTaken}</p>
                                        </div>
                                        <div className="mb-3">
                                            <label className="form-label">Action taken</label>
                                            <p>{data.actionTaken}</p>
                                            <p>{more?.actionTimestamp}</p>
                                            <p>{data.applicationDetails.actionBy.firstName}</p>
                                        </div>
                                        {
                                            (data.uploads && data.uploads.length > 0) && <div className="mb-3">
                                                <label className="form-label">Evidence Uploads</label>
                                                <div className="border p-3 row">
                                                    {
                                                        (data.uploads && data.uploads.length > 0) && data.uploads.map(i => (
                                                            <div className="col-md-3">
                                                                <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                            </div>

                                                        ))
                                                    }

                                                </div>
                                            </div>
                                        }


                                        <div className="mb-3">
                                            <label className="form-label required">Comments</label>
                                            <textarea className="form-control" rows="3" defaultValue={commentsF} onChange={(e) => setCommentsF(e.target.value)}></textarea>
                                        </div>
                                    </>

                                }





                                {/* <div className="mb-3">
                                    <label className="form-label">Action taken</label>
                                    <textarea ref={actionTaken} className="form-control" rows="3"></textarea>
                                </div> */}

                                {/* <div className="mb-3">
                                    <label className="form-label">Upload evidence</label>
                                    <DropzoneArea
                                        acceptedFiles={[

                                            'image/jpeg',
                                            'image/png'

                                        ]}
                                        dropzoneText={"Drag and Drop Evidence Images"}
                                        filesLimit={5}
                                        maxFileSize={104857600}

                                        onChange={handleFileChange}

                                    />
                                </div> */}

                            </div>
                        </div>
                    </div>}



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    {data.actionType === 'hse_action' &&
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={validationCheckReview}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            {assign == 'Yes' ? 'Assign' : 'Archive'}
                        </Button>
                    }

                    {data.actionType === 'action_owner' &&
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={validationCheck}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            Assign to Action Reviewer
                        </Button>
                    }

                    {data.actionType === 'reviewer' && <>
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={() => validationCheck2('return')}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            Return
                        </Button>
                        <Button
                            variant="primary"
                            className='me-2 mt-2'
                            onClick={() => validationCheck2('approve')}
                            sx={{ mt: 1, mr: 1 }}
                        >
                            Approve
                        </Button>
                    </>}

                    <Button
                        variant="light"
                        onClick={() => setShowModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>
        </>
    )
}

export default OBSModal