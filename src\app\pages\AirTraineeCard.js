import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";

import Select from "react-select";
import { DropzoneArea } from 'material-ui-dropzone'; 

import { USERS_URL, AIR_GM_OPS_URL, AIR_TRAINEE_URL, AIR_GMOPS_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";

const AirTraineeCard = ({ showModal, setShowModal, data }) => {

    const [showNext, setShowNext] = useState(false);
    const handleReject = () => {

    }

    const handleContinue = () => {
        setShowNext(true)
    }

    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }

    const [comments, setComments] = useState('');

    const steps = [
        {
            label: 'Trainer Action',
            description: (<>





                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">Trainer Actions Taken</label>
                            <textarea value={comments} onChange={(e) => setComments(e.target.value)} className="form-control"> </textarea>
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <DropzoneArea
                                acceptedFiles={[
                                    'application/pdf',
                                    'image/jpeg',
                                    'image/png'

                                ]}
                                dropzoneText={"Drag and drop files / documents / pictures"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                onChange={handleFileChange}
                            />
                        </div>
                    </div>
                </div>


            </>)
        }
    ]


    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(true);
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_GMOPS_WITH_ID_URL(data.id, data.actionId), {
                traineeAction: {

                    comments: comments,

                }
            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            // Proceed to the next step
            setActiveStep((prevActiveStep) => prevActiveStep + 1);
            setIsClicked(false);

        } catch (error) {
            console.error('An error occurred:', error);
            setIsClicked(false);

        }
        setIsClicked(false);
    };

    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    IR Information
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div>
                        <div className="col-md-6">
                            <Box>
                                <Stepper activeStep={activeStep} orientation="vertical">
                                    {steps.map((step, index) => (
                                        <Step key={step.label}>
                                            <StepLabel>
                                                {step.label}
                                            </StepLabel>
                                            <StepContent>
                                                <Typography>{step.description}</Typography>
                                                <Box sx={{ mb: 2 }}>
                                                    <div>

                                                        {index === steps.length - 1 ? (
                                                            <>
                                                                <div className='form-group'>
                                                                    {/* <select onChange={(e) => setSelectedReviewer(e.target.value)} className='form-control'>
                                                                <option value={''}>Choose Incident Owner</option>
                                                                {
                                                                    incidentReviewer.map(user => {
                                                                        return (
                                                                            <option value={user.id}>{user.firstName}</option>
                                                                        )
                                                                    })
                                                                }
                                                            </select> */}
                                                                </div>
                                                                <Button
                                                                    variant="light"
                                                                    className='me-2 mt-2'
                                                                    onClick={handleSubmit}
                                                                    sx={{ mt: 1, mr: 1 }}
                                                                    disabled={isClicked}
                                                                >
                                                                    Submit
                                                                </Button>
                                                            </>

                                                        ) : (

                                                            <Button
                                                                variant="light"
                                                                className='me-2 mt-2'
                                                                onClick={handleNext}
                                                                sx={{ mt: 1, mr: 1 }}
                                                            
                                                            >
                                                                Continue
                                                            </Button>
                                                        )}

                                                        <Button
                                                            disabled={index === 0}
                                                            className='mt-2'
                                                            onClick={handleBack}
                                                            sx={{ mt: 1, mr: 1 }}
                                                        >
                                                            Back
                                                        </Button>
                                                    </div>
                                                </Box>
                                            </StepContent>
                                        </Step>
                                    ))}
                                </Stepper>
                                {activeStep === steps.length && (
                                    <Paper square elevation={0} sx={{ p: 3 }}>
                                        <Typography>Submitted! Action Card will be disappeared from the list!</Typography>

                                    </Paper>
                                )}
                            </Box>
                        </div>
                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirTraineeCard;